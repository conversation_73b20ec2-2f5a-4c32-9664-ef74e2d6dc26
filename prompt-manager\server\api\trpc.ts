/**
 * tRPC 服务端配置
 * 定义tRPC上下文、中间件和过程
 */

import { initTRPC, TRPCError } from '@trpc/server';
import { type CreateNextContextOptions } from '@trpc/server/adapters/next';
import superjson from 'superjson';
import { ZodError } from 'zod';

// 暂时注释掉NextAuth相关导入
// import { type Session } from 'next-auth';
// import { getServerSession } from 'next-auth/next';
// import { authOptions } from '~/lib/auth';
import { db } from '~/lib/db';

/**
 * 创建tRPC上下文
 * 包含数据库连接和用户会话信息
 */
export const createTRPCContext = async (opts: CreateNextContextOptions) => {
  const { req, res } = opts;

  // 暂时移除会话获取，直接返回null
  // const session = await getServerSession(req, res, authOptions);

  return {
    db,
    session: null, // 暂时设为null
    req,
    res,
  };
};

/**
 * 初始化tRPC
 */
const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * 导出tRPC路由器和过程创建器
 */
export const createTRPCRouter = t.router;

/**
 * 公共过程 - 不需要认证
 */
export const publicProcedure = t.procedure;

/**
 * 认证中间件
 * 确保用户已登录
 */
const enforceUserIsAuthed = t.middleware(({ ctx, next }) => {
  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({
    ctx: {
      // 推断出 `session` 不为 null
      session: { ...ctx.session, user: ctx.session.user },
    },
  });
});

/**
 * 受保护的过程 - 需要用户认证
 */
export const protectedProcedure = t.procedure.use(enforceUserIsAuthed);

/**
 * 类型定义
 */
export type Context = Awaited<ReturnType<typeof createTRPCContext>>;
