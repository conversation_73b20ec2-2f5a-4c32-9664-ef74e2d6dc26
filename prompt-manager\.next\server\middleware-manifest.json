{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "G0rvZwDjitE6aMxnjTLk6LX63W71j0ht0ONrGDcB0pc=", "__NEXT_PREVIEW_MODE_ID": "3bccd414c97e7af29b8971c99c1645ec", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8e534dbb368338acd1898888920d8e2195ddb32052bc8a9c7dd6fb4770aee741", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "33a0b4fa2324cc310ba669363eaf6c6718ffb087bd10e4663f618e43a36fb995"}}}, "functions": {}, "sortedMiddleware": ["/"]}