{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "G0rvZwDjitE6aMxnjTLk6LX63W71j0ht0ONrGDcB0pc=", "__NEXT_PREVIEW_MODE_ID": "aeaa577a35d6fc0dbf876e1c0598afb3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "07cee53e7c3283bafd7f0e9fb44a2abb7219c0cc8f421a5a6fbabfb00813f45d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "55f31d295a63a8aec0ed10eb72ab21bc77207c14f039397de151c043ba9c404e"}}}, "functions": {}, "sortedMiddleware": ["/"]}