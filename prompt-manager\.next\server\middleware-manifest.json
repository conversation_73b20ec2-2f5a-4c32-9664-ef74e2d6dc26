{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api(?!\\/auth)|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api(?!/auth)|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "G0rvZwDjitE6aMxnjTLk6LX63W71j0ht0ONrGDcB0pc=", "__NEXT_PREVIEW_MODE_ID": "d32d01be11f971d5e62beb9830b1c064", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "60c6ee697038daf63cc9b9669678a6d0de1643932095d5b7a9cd4b661db6f84d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe0cb5c67be997512ad06d8368155209bb2d95e501869cec31eecec33cf095d3"}}}, "sortedMiddleware": ["/"], "functions": {}}