{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "G0rvZwDjitE6aMxnjTLk6LX63W71j0ht0ONrGDcB0pc=", "__NEXT_PREVIEW_MODE_ID": "bb0c8c484a539e9103b13ae69c939ab5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7b1fbb5566091ed4a8241862bcc9f93c77349618afc4c5139f9d97ab3a3f5557", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "59b782952e111348bb902e684cc60ac5a259f855b61df4c1146c1d2a7d233851"}}}, "functions": {}, "sortedMiddleware": ["/"]}