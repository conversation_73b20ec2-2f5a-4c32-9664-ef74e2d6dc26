{"version": 3, "sources": ["../../../src/server/app-render/react-large-shell-error.ts"], "sourcesContent": ["// TODO: isWellKnownError -> isNextInternalError\n// isReactLargeShellError -> isWarning\nexport function isReactLargeShellError(\n  error: unknown\n): error is Error & { digest?: string } {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    'message' in error &&\n    typeof error.message === 'string' &&\n    error.message.startsWith('This rendered a large document (>')\n  )\n}\n"], "names": ["isReactLargeShellError", "error", "message", "startsWith"], "mappings": "AAAA,gDAAgD;AAChD,sCAAsC;;;;;+BACtBA;;;eAAAA;;;AAAT,SAASA,uBACdC,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACV,aAAaA,SACb,OAAOA,MAAMC,OAAO,KAAK,YACzBD,MAAMC,OAAO,CAACC,UAAU,CAAC;AAE7B", "ignoreList": [0]}