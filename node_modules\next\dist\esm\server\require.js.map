{"version": 3, "sources": ["../../src/server/require.ts"], "sourcesContent": ["import path from 'path'\nimport {\n  PAGES_MANIFEST,\n  SERVER_DIRECTORY,\n  APP_PATHS_MANIFEST,\n} from '../shared/lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport { PageNotFoundError, MissingStaticPage } from '../shared/lib/utils'\nimport { LRUCache } from '../server/lib/lru-cache'\nimport { loadManifest } from './load-manifest.external'\nimport { promises } from 'fs'\n\nconst isDev = process.env.NODE_ENV === 'development'\nconst pagePathCache = !isDev ? new LRUCache<string | null>(1000) : null\n\nexport function getMaybePagePath(\n  page: string,\n  distDir: string,\n  locales: readonly string[] | undefined,\n  isAppPath: boolean\n): string | null {\n  const cacheKey = `${page}:${distDir}:${locales}:${isAppPath}`\n\n  let pagePath = pagePathCache?.get(cacheKey)\n\n  // If we have a cached path, we can return it directly.\n  if (pagePath) return pagePath\n\n  const serverBuildPath = path.join(distDir, SERVER_DIRECTORY)\n  let appPathsManifest: undefined | PagesManifest\n\n  if (isAppPath) {\n    appPathsManifest = loadManifest(\n      path.join(serverBuildPath, APP_PATHS_MANIFEST),\n      !isDev\n    ) as PagesManifest\n  }\n  const pagesManifest = loadManifest(\n    path.join(serverBuildPath, PAGES_MANIFEST),\n    !isDev\n  ) as PagesManifest\n\n  try {\n    page = denormalizePagePath(normalizePagePath(page))\n  } catch (err) {\n    console.error(err)\n    throw new PageNotFoundError(page)\n  }\n\n  const checkManifest = (manifest: PagesManifest) => {\n    let curPath = manifest[page]\n\n    if (!manifest[curPath] && locales) {\n      const manifestNoLocales: typeof pagesManifest = {}\n\n      for (const key of Object.keys(manifest)) {\n        manifestNoLocales[normalizeLocalePath(key, locales).pathname] =\n          pagesManifest[key]\n      }\n      curPath = manifestNoLocales[page]\n    }\n    return curPath\n  }\n\n  if (appPathsManifest) {\n    pagePath = checkManifest(appPathsManifest)\n  }\n\n  if (!pagePath) {\n    pagePath = checkManifest(pagesManifest)\n  }\n\n  if (!pagePath) {\n    pagePathCache?.set(cacheKey, null)\n    return null\n  }\n\n  pagePath = path.join(serverBuildPath, pagePath)\n\n  pagePathCache?.set(cacheKey, pagePath)\n  return pagePath\n}\n\nexport function getPagePath(\n  page: string,\n  distDir: string,\n  locales: string[] | undefined,\n  isAppPath: boolean\n): string {\n  const pagePath = getMaybePagePath(page, distDir, locales, isAppPath)\n\n  if (!pagePath) {\n    throw new PageNotFoundError(page)\n  }\n\n  return pagePath\n}\n\nexport async function requirePage(\n  page: string,\n  distDir: string,\n  isAppPath: boolean\n): Promise<any> {\n  const pagePath = getPagePath(page, distDir, undefined, isAppPath)\n  if (pagePath.endsWith('.html')) {\n    return promises.readFile(pagePath, 'utf8').catch((err) => {\n      throw new MissingStaticPage(page, err.message)\n    })\n  }\n\n  const mod = process.env.NEXT_MINIMAL\n    ? // @ts-ignore\n      __non_webpack_require__(pagePath)\n    : require(pagePath)\n  return mod\n}\n"], "names": ["path", "PAGES_MANIFEST", "SERVER_DIRECTORY", "APP_PATHS_MANIFEST", "normalizeLocalePath", "normalizePagePath", "denormalizePagePath", "PageNotFoundError", "MissingStaticPage", "L<PERSON><PERSON><PERSON>", "loadManifest", "promises", "isDev", "process", "env", "NODE_ENV", "pagePath<PERSON>ache", "getMaybePagePath", "page", "distDir", "locales", "isAppPath", "cache<PERSON>ey", "pagePath", "get", "serverBuildPath", "join", "appPathsManifest", "pagesManifest", "err", "console", "error", "checkManifest", "manifest", "curPath", "manifestNoLocales", "key", "Object", "keys", "pathname", "set", "getPagePath", "requirePage", "undefined", "endsWith", "readFile", "catch", "message", "mod", "NEXT_MINIMAL", "__non_webpack_require__", "require"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SACEC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,QACb,0BAAyB;AAChC,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,gDAA+C;AAEnF,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,sBAAqB;AAC1E,SAASC,QAAQ,QAAQ,0BAAyB;AAClD,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SAASC,QAAQ,QAAQ,KAAI;AAE7B,MAAMC,QAAQC,QAAQC,GAAG,CAACC,QAAQ,KAAK;AACvC,MAAMC,gBAAgB,CAACJ,QAAQ,IAAIH,SAAwB,QAAQ;AAEnE,OAAO,SAASQ,iBACdC,IAAY,EACZC,OAAe,EACfC,OAAsC,EACtCC,SAAkB;IAElB,MAAMC,WAAW,GAAGJ,KAAK,CAAC,EAAEC,QAAQ,CAAC,EAAEC,QAAQ,CAAC,EAAEC,WAAW;IAE7D,IAAIE,WAAWP,iCAAAA,cAAeQ,GAAG,CAACF;IAElC,uDAAuD;IACvD,IAAIC,UAAU,OAAOA;IAErB,MAAME,kBAAkBzB,KAAK0B,IAAI,CAACP,SAASjB;IAC3C,IAAIyB;IAEJ,IAAIN,WAAW;QACbM,mBAAmBjB,aACjBV,KAAK0B,IAAI,CAACD,iBAAiBtB,qBAC3B,CAACS;IAEL;IACA,MAAMgB,gBAAgBlB,aACpBV,KAAK0B,IAAI,CAACD,iBAAiBxB,iBAC3B,CAACW;IAGH,IAAI;QACFM,OAAOZ,oBAAoBD,kBAAkBa;IAC/C,EAAE,OAAOW,KAAK;QACZC,QAAQC,KAAK,CAACF;QACd,MAAM,IAAItB,kBAAkBW;IAC9B;IAEA,MAAMc,gBAAgB,CAACC;QACrB,IAAIC,UAAUD,QAAQ,CAACf,KAAK;QAE5B,IAAI,CAACe,QAAQ,CAACC,QAAQ,IAAId,SAAS;YACjC,MAAMe,oBAA0C,CAAC;YAEjD,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACL,UAAW;gBACvCE,iBAAiB,CAAC/B,oBAAoBgC,KAAKhB,SAASmB,QAAQ,CAAC,GAC3DX,aAAa,CAACQ,IAAI;YACtB;YACAF,UAAUC,iBAAiB,CAACjB,KAAK;QACnC;QACA,OAAOgB;IACT;IAEA,IAAIP,kBAAkB;QACpBJ,WAAWS,cAAcL;IAC3B;IAEA,IAAI,CAACJ,UAAU;QACbA,WAAWS,cAAcJ;IAC3B;IAEA,IAAI,CAACL,UAAU;QACbP,iCAAAA,cAAewB,GAAG,CAAClB,UAAU;QAC7B,OAAO;IACT;IAEAC,WAAWvB,KAAK0B,IAAI,CAACD,iBAAiBF;IAEtCP,iCAAAA,cAAewB,GAAG,CAAClB,UAAUC;IAC7B,OAAOA;AACT;AAEA,OAAO,SAASkB,YACdvB,IAAY,EACZC,OAAe,EACfC,OAA6B,EAC7BC,SAAkB;IAElB,MAAME,WAAWN,iBAAiBC,MAAMC,SAASC,SAASC;IAE1D,IAAI,CAACE,UAAU;QACb,MAAM,IAAIhB,kBAAkBW;IAC9B;IAEA,OAAOK;AACT;AAEA,OAAO,eAAemB,YACpBxB,IAAY,EACZC,OAAe,EACfE,SAAkB;IAElB,MAAME,WAAWkB,YAAYvB,MAAMC,SAASwB,WAAWtB;IACvD,IAAIE,SAASqB,QAAQ,CAAC,UAAU;QAC9B,OAAOjC,SAASkC,QAAQ,CAACtB,UAAU,QAAQuB,KAAK,CAAC,CAACjB;YAChD,MAAM,IAAIrB,kBAAkBU,MAAMW,IAAIkB,OAAO;QAC/C;IACF;IAEA,MAAMC,MAAMnC,QAAQC,GAAG,CAACmC,YAAY,GAEhCC,wBAAwB3B,YACxB4B,QAAQ5B;IACZ,OAAOyB;AACT", "ignoreList": [0]}