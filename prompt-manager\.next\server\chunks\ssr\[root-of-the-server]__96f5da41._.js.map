{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/Toast.tsx"], "sourcesContent": ["/**\n * Toast通知组件\n * 用于显示操作反馈信息\n */\n\n'use client';\n\nimport { useEffect, useState } from 'react';\n\nexport interface ToastProps {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message?: string;\n  duration?: number;\n  onClose: (id: string) => void;\n}\n\nexport default function Toast({\n  id,\n  type,\n  title,\n  message,\n  duration = 5000,\n  onClose,\n}: ToastProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isLeaving, setIsLeaving] = useState(false);\n\n  useEffect(() => {\n    // 进入动画\n    const timer = setTimeout(() => setIsVisible(true), 100);\n    return () => clearTimeout(timer);\n  }, []);\n\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        handleClose();\n      }, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration]);\n\n  const handleClose = () => {\n    setIsLeaving(true);\n    setTimeout(() => {\n      onClose(id);\n    }, 300);\n  };\n\n  const getTypeStyles = () => {\n    switch (type) {\n      case 'success':\n        return {\n          bg: 'bg-green-50',\n          border: 'border-green-200',\n          icon: 'text-green-400',\n          title: 'text-green-800',\n          message: 'text-green-700',\n          iconPath: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',\n        };\n      case 'error':\n        return {\n          bg: 'bg-red-50',\n          border: 'border-red-200',\n          icon: 'text-red-400',\n          title: 'text-red-800',\n          message: 'text-red-700',\n          iconPath: 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',\n        };\n      case 'warning':\n        return {\n          bg: 'bg-yellow-50',\n          border: 'border-yellow-200',\n          icon: 'text-yellow-400',\n          title: 'text-yellow-800',\n          message: 'text-yellow-700',\n          iconPath: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 16.5c-.77.833.192 2.5 1.732 2.5z',\n        };\n      case 'info':\n        return {\n          bg: 'bg-blue-50',\n          border: 'border-blue-200',\n          icon: 'text-blue-400',\n          title: 'text-blue-800',\n          message: 'text-blue-700',\n          iconPath: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',\n        };\n    }\n  };\n\n  const styles = getTypeStyles();\n\n  return (\n    <div\n      className={`max-w-sm w-full ${styles.bg} ${styles.border} border rounded-lg shadow-lg pointer-events-auto transform transition-all duration-300 ease-in-out ${\n        isVisible && !isLeaving\n          ? 'translate-x-0 opacity-100'\n          : 'translate-x-full opacity-0'\n      }`}\n    >\n      <div className=\"p-4\">\n        <div className=\"flex items-start\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className={`h-6 w-6 ${styles.icon}`}\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d={styles.iconPath}\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3 w-0 flex-1\">\n            <p className={`text-sm font-medium ${styles.title}`}>{title}</p>\n            {message && (\n              <p className={`mt-1 text-sm ${styles.message}`}>{message}</p>\n            )}\n          </div>\n          <div className=\"ml-4 flex-shrink-0 flex\">\n            <button\n              onClick={handleClose}\n              className={`rounded-md inline-flex ${styles.title} hover:${styles.message} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-${type}-50 focus:ring-${type}-600`}\n            >\n              <span className=\"sr-only\">关闭</span>\n              <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAFA;;;AAae,SAAS,MAAM,EAC5B,EAAE,EACF,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,IAAI,EACf,OAAO,EACI;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;QACP,MAAM,QAAQ,WAAW,IAAM,aAAa,OAAO;QACnD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,GAAG;YAChB,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT,QAAQ;QACV,GAAG;IACL;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,IAAI;oBACJ,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;YACF,KAAK;gBACH,OAAO;oBACL,IAAI;oBACJ,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;YACF,KAAK;gBACH,OAAO;oBACL,IAAI;oBACJ,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;YACF,KAAK;gBACH,OAAO;oBACL,IAAI;oBACJ,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;QACJ;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,WAAW,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,mGAAmG,EAC1J,aAAa,CAAC,YACV,8BACA,8BACJ;kBAEF,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAW,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;4BACnC,MAAK;4BACL,SAAQ;4BACR,QAAO;sCAEP,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAG,OAAO,QAAQ;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAW,CAAC,oBAAoB,EAAE,OAAO,KAAK,EAAE;0CAAG;;;;;;4BACrD,yBACC,8OAAC;gCAAE,WAAW,CAAC,aAAa,EAAE,OAAO,OAAO,EAAE;0CAAG;;;;;;;;;;;;kCAGrD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAW,CAAC,uBAAuB,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,uEAAuE,EAAE,KAAK,eAAe,EAAE,KAAK,IAAI,CAAC;;8CAEnL,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;oCAAU,SAAQ;oCAAY,MAAK;8CAChD,cAAA,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/ToastContainer.tsx"], "sourcesContent": ["/**\n * Toast容器组件\n * 管理多个Toast通知的显示\n */\n\n'use client';\n\nimport { createContext, useContext, useState, useCallback } from 'react';\nimport Toast, { ToastProps } from './Toast';\n\ninterface ToastContextType {\n  showToast: (toast: Omit<ToastProps, 'id' | 'onClose'>) => void;\n  showSuccess: (title: string, message?: string) => void;\n  showError: (title: string, message?: string) => void;\n  showWarning: (title: string, message?: string) => void;\n  showInfo: (title: string, message?: string) => void;\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\n\nexport const useToast = () => {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n\ninterface ToastProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ToastProvider({ children }: ToastProviderProps) {\n  const [toasts, setToasts] = useState<ToastProps[]>([]);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n\n  const showToast = useCallback((toast: Omit<ToastProps, 'id' | 'onClose'>) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const newToast: ToastProps = {\n      ...toast,\n      id,\n      onClose: removeToast,\n    };\n    setToasts(prev => [...prev, newToast]);\n  }, [removeToast]);\n\n  const showSuccess = useCallback((title: string, message?: string) => {\n    showToast({ type: 'success', title, message });\n  }, [showToast]);\n\n  const showError = useCallback((title: string, message?: string) => {\n    showToast({ type: 'error', title, message });\n  }, [showToast]);\n\n  const showWarning = useCallback((title: string, message?: string) => {\n    showToast({ type: 'warning', title, message });\n  }, [showToast]);\n\n  const showInfo = useCallback((title: string, message?: string) => {\n    showToast({ type: 'info', title, message });\n  }, [showToast]);\n\n  const contextValue: ToastContextType = {\n    showToast,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n  };\n\n  return (\n    <ToastContext.Provider value={contextValue}>\n      {children}\n      \n      {/* Toast容器 */}\n      <div className=\"fixed top-0 right-0 z-50 p-6 space-y-4 pointer-events-none\">\n        {toasts.map(toast => (\n          <Toast key={toast.id} {...toast} />\n        ))}\n      </div>\n    </ToastContext.Provider>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AACA;AAHA;;;;AAaA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAErD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAuB;YAC3B,GAAG,KAAK;YACR;YACA,SAAS;QACX;QACA,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC,GAAG;QAAC;KAAY;IAEhB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,UAAU;YAAE,MAAM;YAAW;YAAO;QAAQ;IAC9C,GAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC5C,UAAU;YAAE,MAAM;YAAS;YAAO;QAAQ;IAC5C,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,UAAU;YAAE,MAAM;YAAW;YAAO;QAAQ;IAC9C,GAAG;QAAC;KAAU;IAEd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC3C,UAAU;YAAE,MAAM;YAAQ;YAAO;QAAQ;IAC3C,GAAG;QAAC;KAAU;IAEd,MAAM,eAAiC;QACrC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;;YAC3B;0BAGD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAA,sBACV,8OAAC,iIAAA,CAAA,UAAK;wBAAiB,GAAG,KAAK;uBAAnB,MAAM,EAAE;;;;;;;;;;;;;;;;AAK9B", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/auth/SessionProvider.tsx"], "sourcesContent": ["/**\n * 会话状态管理组件\n * 暂时简化版本，等依赖安装完成后恢复\n */\n\n'use client';\n\ninterface SessionProviderProps {\n  children: React.ReactNode;\n  session?: any | null;\n}\n\nexport default function SessionProvider({ children, session }: SessionProviderProps) {\n  // 暂时简化，直接返回children\n  return <>{children}</>;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAOe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAwB;IACjF,oBAAoB;IACpB,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/providers/TRPCProvider.tsx"], "sourcesContent": ["/**\n * tRPC Provider组件\n * 暂时简化版本，等依赖安装完成后恢复\n */\n\n'use client';\n\ninterface TRPCProviderProps {\n  children: React.ReactNode;\n}\n\nexport default function TRPCProvider({ children }: TRPCProviderProps) {\n  // 暂时简化，直接返回children\n  return <>{children}</>;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAMe,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAClE,oBAAoB;IACpB,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}