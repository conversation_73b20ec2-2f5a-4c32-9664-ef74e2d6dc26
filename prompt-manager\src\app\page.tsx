'use client';

import { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import SearchBar from '@/components/ui/SearchBar';
import PromptCard from '@/components/ui/PromptCard';
import PromptDetailModal from '@/components/ui/PromptDetailModal';
import PromptEditModal from '@/components/ui/PromptEditModal';
import CategoryManageModal from '@/components/ui/CategoryManageModal';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import { useToast } from '@/components/ui/ToastContainer';

interface SearchFilters {
  query: string;
  categoryId: string | null;
  tags: string[];
  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title';
  sortOrder: 'asc' | 'desc';
}

export default function Home() {
  const { showSuccess, showError, showInfo } = useToast();

  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    categoryId: null,
    tags: [],
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });

  // 模态框状态
  const [selectedPrompt, setSelectedPrompt] = useState<any>(null);
  const [editingPrompt, setEditingPrompt] = useState<any>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});

  // 模拟提示词数据
  const mockPrompts = [
    {
      id: '1',
      title: '写作助手 - 文章大纲生成',
      content: '请帮我为以下主题创建一个详细的文章大纲：[主题]\n\n要求：\n1. 包含引言、主体和结论\n2. 主体部分至少3个要点\n3. 每个要点包含2-3个子点\n4. 提供吸引人的标题建议',
      description: '帮助用户快速生成文章大纲，提高写作效率',
      category: { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },
      tags: [
        { id: '1', name: 'AI', color: '#3B82F6' },
        { id: '3', name: '创意', color: '#F59E0B' },
      ],
      usageCount: 25,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z',
    },
    {
      id: '2',
      title: '代码生成 - React组件模板',
      content: '请为我生成一个React函数组件，要求：\n\n组件名：[组件名]\n功能：[功能描述]\n\n请包含：\n- TypeScript类型定义\n- Props接口\n- 基本的JSX结构\n- 简单的样式类名\n- 必要的注释',
      description: '快速生成React组件的基础模板代码',
      category: { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },
      tags: [
        { id: '2', name: '编程', color: '#10B981' },
        { id: '1', name: 'AI', color: '#3B82F6' },
      ],
      usageCount: 18,
      createdAt: '2024-01-10T09:00:00Z',
      updatedAt: '2024-01-18T11:20:00Z',
    },
    {
      id: '3',
      title: '翻译工具 - 专业文档翻译',
      content: '请将以下内容翻译成[目标语言]，要求：\n\n1. 保持专业术语的准确性\n2. 语言流畅自然\n3. 保留原文格式\n4. 如有专业术语，请在括号内标注原文\n\n原文：\n[待翻译内容]',
      description: '专业文档翻译，保持术语准确性和格式完整',
      category: { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },
      tags: [
        { id: '4', name: '商务', color: '#EF4444' },
      ],
      usageCount: 12,
      createdAt: '2024-01-05T14:00:00Z',
      updatedAt: '2024-01-15T16:45:00Z',
    },
  ];

  const handleFiltersChange = (newFilters: SearchFilters) => {
    setFilters(newFilters);
  };

  const handlePromptEdit = (prompt: any) => {
    setEditingPrompt(prompt);
    setShowEditModal(true);
  };

  const handlePromptDelete = (promptId: string) => {
    setConfirmAction(() => () => {
      console.log('删除提示词:', promptId);
      // 这里应该调用API删除提示词
    });
    setShowConfirmDialog(true);
  };

  const handlePromptCopy = (content: string) => {
    console.log('复制内容:', content);
    showSuccess('复制成功', '提示词内容已复制到剪贴板');
  };

  const handlePromptView = (prompt: any) => {
    setSelectedPrompt(prompt);
    setShowDetailModal(true);
  };

  const handleNewPrompt = () => {
    setEditingPrompt(null);
    setShowEditModal(true);
  };

  const handlePromptSave = (promptData: any) => {
    console.log('保存提示词:', promptData);
    // 这里应该调用API保存提示词
    showSuccess('保存成功', '提示词已保存');
  };

  const handleCategorySave = (categoryData: any) => {
    console.log('保存分类:', categoryData);
    // 这里应该调用API保存分类
  };

  const handleCategoryUpdate = (id: string, categoryData: any) => {
    console.log('更新分类:', id, categoryData);
    // 这里应该调用API更新分类
  };

  const handleCategoryDelete = (id: string) => {
    console.log('删除分类:', id);
    // 这里应该调用API删除分类
  };

  return (
    <MainLayout
      onNewPrompt={handleNewPrompt}
      onManageCategories={() => setShowCategoryModal(true)}
    >
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">我的提示词</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理和使用您的AI提示词库
          </p>
        </div>

        {/* 搜索和筛选 */}
        <SearchBar
          filters={filters}
          onFiltersChange={handleFiltersChange}
          placeholder="搜索提示词标题、内容或描述..."
        />

        {/* 提示词列表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {mockPrompts.map((prompt) => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              onEdit={handlePromptEdit}
              onDelete={handlePromptDelete}
              onCopy={handlePromptCopy}
              onView={handlePromptView}
            />
          ))}
        </div>

        {/* 空状态 */}
        {mockPrompts.length === 0 && (
          <div className="text-center py-12">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到提示词</h3>
            <p className="mt-1 text-sm text-gray-500">开始创建您的第一个提示词吧。</p>
            <div className="mt-6">
              <button
                onClick={handleNewPrompt}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                新建提示词
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 模态框组件 */}
      <PromptDetailModal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        prompt={selectedPrompt}
        onEdit={handlePromptEdit}
        onDelete={handlePromptDelete}
        onCopy={handlePromptCopy}
      />

      <PromptEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        prompt={editingPrompt}
        onSave={handlePromptSave}
      />

      <CategoryManageModal
        isOpen={showCategoryModal}
        onClose={() => setShowCategoryModal(false)}
        onSave={handleCategorySave}
        onUpdate={handleCategoryUpdate}
        onDelete={handleCategoryDelete}
      />

      <ConfirmDialog
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={confirmAction}
        message="此操作无法撤销，确定要继续吗？"
      />
    </MainLayout>
  );
}
