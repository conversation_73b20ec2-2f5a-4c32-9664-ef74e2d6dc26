{"version": 3, "sources": ["../../../../src/shared/lib/router/router.ts"], "sourcesContent": ["// tslint:disable:no-console\nimport type { ComponentType } from 'react'\nimport type { DomainLocale } from '../../../server/config'\nimport type { MittEmitter } from '../mitt'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RouterEvent } from '../../../client/router'\nimport type { StyleSheetTuple } from '../../../client/page-loader'\nimport type { UrlObject } from 'url'\nimport type PageLoader from '../../../client/page-loader'\nimport type { AppContextType, NextPageContext, NEXT_DATA } from '../utils'\nimport { removeTrailingSlash } from './utils/remove-trailing-slash'\nimport {\n  getClientBuildManifest,\n  isAssetError,\n  markAssetError,\n} from '../../../client/route-loader'\nimport { handleClientScriptLoad } from '../../../client/script'\nimport isError, { getProperError } from '../../../lib/is-error'\nimport { denormalizePagePath } from '../page-path/denormalize-page-path'\nimport { normalizeLocalePath } from '../i18n/normalize-locale-path'\nimport mitt from '../mitt'\nimport { getLocationOrigin, getURL, loadGetInitialProps, ST } from '../utils'\nimport { isDynamicRoute } from './utils/is-dynamic'\nimport { parseRelativeUrl } from './utils/parse-relative-url'\nimport { getRouteMatcher } from './utils/route-matcher'\nimport { getRouteRegex } from './utils/route-regex'\nimport { formatWithValidation } from './utils/format-url'\nimport { detectDomainLocale } from '../../../client/detect-domain-locale'\nimport { parsePath } from './utils/parse-path'\nimport { addLocale } from '../../../client/add-locale'\nimport { removeLocale } from '../../../client/remove-locale'\nimport { removeBasePath } from '../../../client/remove-base-path'\nimport { addBasePath } from '../../../client/add-base-path'\nimport { hasBasePath } from '../../../client/has-base-path'\nimport { resolveHref } from '../../../client/resolve-href'\nimport { isAPIRoute } from '../../../lib/is-api-route'\nimport { getNextPathnameInfo } from './utils/get-next-pathname-info'\nimport { formatNextPathnameInfo } from './utils/format-next-pathname-info'\nimport { compareRouterStates } from './utils/compare-states'\nimport { isLocalURL } from './utils/is-local-url'\nimport { isBot } from './utils/is-bot'\nimport { omit } from './utils/omit'\nimport { interpolateAs } from './utils/interpolate-as'\nimport { disableSmoothScrollDuringRouteTransition } from './utils/disable-smooth-scroll'\nimport type { Params } from '../../../server/request/params'\nimport { MATCHED_PATH_HEADER } from '../../../lib/constants'\n\nlet resolveRewrites: typeof import('./utils/resolve-rewrites').default\nif (process.env.__NEXT_HAS_REWRITES) {\n  resolveRewrites = (\n    require('./utils/resolve-rewrites') as typeof import('./utils/resolve-rewrites')\n  ).default\n}\n\ndeclare global {\n  interface Window {\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n  }\n}\n\ninterface RouteProperties {\n  shallow: boolean\n}\n\ninterface TransitionOptions {\n  shallow?: boolean\n  locale?: string | false\n  scroll?: boolean\n  unstable_skipClientCache?: boolean\n}\n\ninterface NextHistoryState {\n  url: string\n  as: string\n  options: TransitionOptions\n}\n\nexport type HistoryState =\n  | null\n  | { __NA: true; __N?: false }\n  | { __N: false; __NA?: false }\n  | ({ __NA?: false; __N: true; key: string } & NextHistoryState)\n\nfunction buildCancellationError() {\n  return Object.assign(new Error('Route Cancelled'), {\n    cancelled: true,\n  })\n}\n\ninterface MiddlewareEffectParams<T extends FetchDataOutput> {\n  fetchData?: () => Promise<T>\n  locale?: string\n  asPath: string\n  router: Router\n}\n\nexport async function matchesMiddleware<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<boolean> {\n  const matchers = await Promise.resolve(\n    options.router.pageLoader.getMiddleware()\n  )\n  if (!matchers) return false\n\n  const { pathname: asPathname } = parsePath(options.asPath)\n  // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n  const cleanedAs = hasBasePath(asPathname)\n    ? removeBasePath(asPathname)\n    : asPathname\n  const asWithBasePathAndLocale = addBasePath(\n    addLocale(cleanedAs, options.locale)\n  )\n\n  // Check only path match on client. Matching \"has\" should be done on server\n  // where we can access more info such as headers, HttpOnly cookie, etc.\n  return matchers.some((m) =>\n    new RegExp(m.regexp).test(asWithBasePathAndLocale)\n  )\n}\n\nfunction stripOrigin(url: string) {\n  const origin = getLocationOrigin()\n\n  return url.startsWith(origin) ? url.substring(origin.length) : url\n}\n\nfunction prepareUrlAs(router: NextRouter, url: Url, as?: Url) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = resolveHref(router, url, true)\n  const origin = getLocationOrigin()\n  const hrefWasAbsolute = resolvedHref.startsWith(origin)\n  const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin)\n\n  resolvedHref = stripOrigin(resolvedHref)\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs\n\n  const preparedUrl = hrefWasAbsolute ? resolvedHref : addBasePath(resolvedHref)\n  const preparedAs = as\n    ? stripOrigin(resolveHref(router, as))\n    : resolvedAs || resolvedHref\n\n  return {\n    url: preparedUrl,\n    as: asWasAbsolute ? preparedAs : addBasePath(preparedAs),\n  }\n}\n\nfunction resolveDynamicRoute(pathname: string, pages: string[]) {\n  const cleanPathname = removeTrailingSlash(denormalizePagePath(pathname))\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname\n  }\n\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some((page) => {\n      if (isDynamicRoute(page) && getRouteRegex(page).re.test(cleanPathname)) {\n        pathname = page\n        return true\n      }\n    })\n  }\n  return removeTrailingSlash(pathname)\n}\n\nfunction getMiddlewareData<T extends FetchDataOutput>(\n  source: string,\n  response: Response,\n  options: MiddlewareEffectParams<T>\n) {\n  const nextConfig = {\n    basePath: options.router.basePath,\n    i18n: { locales: options.router.locales },\n    trailingSlash: Boolean(process.env.__NEXT_TRAILING_SLASH),\n  }\n  const rewriteHeader = response.headers.get('x-nextjs-rewrite')\n\n  let rewriteTarget =\n    rewriteHeader || response.headers.get('x-nextjs-matched-path')\n\n  const matchedPath = response.headers.get(MATCHED_PATH_HEADER)\n\n  if (\n    matchedPath &&\n    !rewriteTarget &&\n    !matchedPath.includes('__next_data_catchall') &&\n    !matchedPath.includes('/_error') &&\n    !matchedPath.includes('/404')\n  ) {\n    // leverage x-matched-path to detect next.config.js rewrites\n    rewriteTarget = matchedPath\n  }\n\n  if (rewriteTarget) {\n    if (\n      rewriteTarget.startsWith('/') ||\n      process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE\n    ) {\n      const parsedRewriteTarget = parseRelativeUrl(rewriteTarget)\n      const pathnameInfo = getNextPathnameInfo(parsedRewriteTarget.pathname, {\n        nextConfig,\n        parseData: true,\n      })\n\n      let fsPathname = removeTrailingSlash(pathnameInfo.pathname)\n      return Promise.all([\n        options.router.pageLoader.getPageList(),\n        getClientBuildManifest(),\n      ]).then(([pages, { __rewrites: rewrites }]: any) => {\n        let as = addLocale(pathnameInfo.pathname, pathnameInfo.locale)\n\n        if (\n          isDynamicRoute(as) ||\n          (!rewriteHeader &&\n            pages.includes(\n              normalizeLocalePath(removeBasePath(as), options.router.locales)\n                .pathname\n            ))\n        ) {\n          const parsedSource = getNextPathnameInfo(\n            parseRelativeUrl(source).pathname,\n            {\n              nextConfig: process.env.__NEXT_HAS_REWRITES\n                ? undefined\n                : nextConfig,\n              parseData: true,\n            }\n          )\n\n          as = addBasePath(parsedSource.pathname)\n          parsedRewriteTarget.pathname = as\n        }\n\n        if (process.env.__NEXT_HAS_REWRITES) {\n          const result = resolveRewrites(\n            as,\n            pages,\n            rewrites,\n            parsedRewriteTarget.query,\n            (path: string) => resolveDynamicRoute(path, pages),\n            options.router.locales\n          )\n\n          if (result.matchedPage) {\n            parsedRewriteTarget.pathname = result.parsedAs.pathname\n            as = parsedRewriteTarget.pathname\n            Object.assign(parsedRewriteTarget.query, result.parsedAs.query)\n          }\n        } else if (!pages.includes(fsPathname)) {\n          const resolvedPathname = resolveDynamicRoute(fsPathname, pages)\n\n          if (resolvedPathname !== fsPathname) {\n            fsPathname = resolvedPathname\n          }\n        }\n\n        const resolvedHref = !pages.includes(fsPathname)\n          ? resolveDynamicRoute(\n              normalizeLocalePath(\n                removeBasePath(parsedRewriteTarget.pathname),\n                options.router.locales\n              ).pathname,\n              pages\n            )\n          : fsPathname\n\n        if (isDynamicRoute(resolvedHref)) {\n          const matches = getRouteMatcher(getRouteRegex(resolvedHref))(as)\n          Object.assign(parsedRewriteTarget.query, matches || {})\n        }\n\n        return {\n          type: 'rewrite' as const,\n          parsedAs: parsedRewriteTarget,\n          resolvedHref,\n        }\n      })\n    }\n    const src = parsePath(source)\n    const pathname = formatNextPathnameInfo({\n      ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n      defaultLocale: options.router.defaultLocale,\n      buildId: '',\n    })\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: `${pathname}${src.query}${src.hash}`,\n    })\n  }\n\n  const redirectTarget = response.headers.get('x-nextjs-redirect')\n\n  if (redirectTarget) {\n    if (redirectTarget.startsWith('/')) {\n      const src = parsePath(redirectTarget)\n      const pathname = formatNextPathnameInfo({\n        ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n        defaultLocale: options.router.defaultLocale,\n        buildId: '',\n      })\n\n      return Promise.resolve({\n        type: 'redirect-internal' as const,\n        newAs: `${pathname}${src.query}${src.hash}`,\n        newUrl: `${pathname}${src.query}${src.hash}`,\n      })\n    }\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: redirectTarget,\n    })\n  }\n\n  return Promise.resolve({ type: 'next' as const })\n}\n\ninterface WithMiddlewareEffectsOutput extends FetchDataOutput {\n  effect: Awaited<ReturnType<typeof getMiddlewareData>>\n}\n\nasync function withMiddlewareEffects<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<WithMiddlewareEffectsOutput | null> {\n  const matches = await matchesMiddleware(options)\n  if (!matches || !options.fetchData) {\n    return null\n  }\n\n  const data = await options.fetchData()\n\n  const effect = await getMiddlewareData(data.dataHref, data.response, options)\n\n  return {\n    dataHref: data.dataHref,\n    json: data.json,\n    response: data.response,\n    text: data.text,\n    cacheKey: data.cacheKey,\n    effect,\n  }\n}\n\nexport type Url = UrlObject | string\n\nexport type BaseRouter = {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  locale?: string | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isLocaleDomain: boolean\n}\n\nexport type NextRouter = BaseRouter &\n  Pick<\n    Router,\n    | 'push'\n    | 'replace'\n    | 'reload'\n    | 'back'\n    | 'forward'\n    | 'prefetch'\n    | 'beforePopState'\n    | 'events'\n    | 'isFallback'\n    | 'isReady'\n    | 'isPreview'\n  >\n\nexport type PrefetchOptions = {\n  priority?: boolean\n  locale?: string | false\n  unstable_skipClientCache?: boolean\n}\n\nexport type PrivateRouteInfo =\n  | (Omit<CompletePrivateRouteInfo, 'styleSheets'> & { initial: true })\n  | CompletePrivateRouteInfo\n\nexport type CompletePrivateRouteInfo = {\n  Component: ComponentType\n  styleSheets: StyleSheetTuple[]\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n  props?: Record<string, any>\n  err?: Error\n  error?: any\n  route?: string\n  resolvedAs?: string\n  query?: ParsedUrlQuery\n}\n\nexport type AppProps = Pick<CompletePrivateRouteInfo, 'Component' | 'err'> & {\n  router: Router\n} & Record<string, any>\nexport type AppComponent = ComponentType<AppProps>\n\ntype Subscription = (\n  data: PrivateRouteInfo,\n  App: AppComponent,\n  resetScroll: { x: number; y: number } | null\n) => Promise<void>\n\ntype BeforePopStateCallback = (state: NextHistoryState) => boolean\n\ntype ComponentLoadCancel = (() => void) | null\n\ntype HistoryMethod = 'replaceState' | 'pushState'\n\nconst manualScrollRestoration =\n  process.env.__NEXT_SCROLL_RESTORATION &&\n  typeof window !== 'undefined' &&\n  'scrollRestoration' in window.history &&\n  !!(function () {\n    try {\n      let v = '__next'\n      // eslint-disable-next-line no-sequences\n      return sessionStorage.setItem(v, v), sessionStorage.removeItem(v), true\n    } catch (n) {}\n  })()\n\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND')\n\nfunction fetchRetry(\n  url: string,\n  attempts: number,\n  options: Pick<RequestInit, 'method' | 'headers'>\n): Promise<Response> {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n    method: options.method || 'GET',\n    headers: Object.assign({}, options.headers, {\n      'x-nextjs-data': '1',\n    }),\n  }).then((response) => {\n    return !response.ok && attempts > 1 && response.status >= 500\n      ? fetchRetry(url, attempts - 1, options)\n      : response\n  })\n}\n\ninterface FetchDataOutput {\n  dataHref: string\n  json: Record<string, any> | null\n  response: Response\n  text: string\n  cacheKey: string\n}\n\ninterface FetchNextDataParams {\n  dataHref: string\n  isServerRender: boolean\n  parseJSON: boolean | undefined\n  hasMiddleware?: boolean\n  inflightCache: NextDataCache\n  persistCache: boolean\n  isPrefetch: boolean\n  isBackground?: boolean\n  unstable_skipClientCache?: boolean\n}\n\nfunction tryToParseAsJSON(text: string) {\n  try {\n    return JSON.parse(text)\n  } catch (error) {\n    return null\n  }\n}\n\nfunction fetchNextData({\n  dataHref,\n  inflightCache,\n  isPrefetch,\n  hasMiddleware,\n  isServerRender,\n  parseJSON,\n  persistCache,\n  isBackground,\n  unstable_skipClientCache,\n}: FetchNextDataParams): Promise<FetchDataOutput> {\n  const { href: cacheKey } = new URL(dataHref, window.location.href)\n  const getData = (params?: { method?: 'HEAD' | 'GET' }) =>\n    fetchRetry(dataHref, isServerRender ? 3 : 1, {\n      headers: Object.assign(\n        {} as HeadersInit,\n        isPrefetch ? { purpose: 'prefetch' } : {},\n        isPrefetch && hasMiddleware ? { 'x-middleware-prefetch': '1' } : {},\n        process.env.NEXT_DEPLOYMENT_ID\n          ? { 'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID }\n          : {}\n      ),\n      method: params?.method ?? 'GET',\n    })\n      .then((response) => {\n        if (response.ok && params?.method === 'HEAD') {\n          return { dataHref, response, text: '', json: {}, cacheKey }\n        }\n\n        return response.text().then((text) => {\n          if (!response.ok) {\n            /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */\n            if (\n              hasMiddleware &&\n              [301, 302, 307, 308].includes(response.status)\n            ) {\n              return { dataHref, response, text, json: {}, cacheKey }\n            }\n\n            if (response.status === 404) {\n              if (tryToParseAsJSON(text)?.notFound) {\n                return {\n                  dataHref,\n                  json: { notFound: SSG_DATA_NOT_FOUND },\n                  response,\n                  text,\n                  cacheKey,\n                }\n              }\n            }\n\n            const error = new Error(`Failed to load static props`)\n\n            /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */\n            if (!isServerRender) {\n              markAssetError(error)\n            }\n\n            throw error\n          }\n\n          return {\n            dataHref,\n            json: parseJSON ? tryToParseAsJSON(text) : null,\n            response,\n            text,\n            cacheKey,\n          }\n        })\n      })\n      .then((data) => {\n        if (\n          !persistCache ||\n          process.env.NODE_ENV !== 'production' ||\n          data.response.headers.get('x-middleware-cache') === 'no-cache'\n        ) {\n          delete inflightCache[cacheKey]\n        }\n        return data\n      })\n      .catch((err) => {\n        if (!unstable_skipClientCache) {\n          delete inflightCache[cacheKey]\n        }\n        if (\n          // chrome\n          err.message === 'Failed to fetch' ||\n          // firefox\n          err.message === 'NetworkError when attempting to fetch resource.' ||\n          // safari\n          err.message === 'Load failed'\n        ) {\n          markAssetError(err)\n        }\n        throw err\n      })\n\n  // when skipping client cache we wait to update\n  // inflight cache until successful data response\n  // this allows racing click event with fetching newer data\n  // without blocking navigation when stale data is available\n  if (unstable_skipClientCache && persistCache) {\n    return getData({}).then((data) => {\n      if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n        // only update cache if not marked as no-cache\n        inflightCache[cacheKey] = Promise.resolve(data)\n      }\n\n      return data\n    })\n  }\n\n  if (inflightCache[cacheKey] !== undefined) {\n    return inflightCache[cacheKey]\n  }\n  return (inflightCache[cacheKey] = getData(\n    isBackground ? { method: 'HEAD' } : {}\n  ))\n}\n\ninterface NextDataCache {\n  [asPath: string]: Promise<FetchDataOutput>\n}\n\nexport function createKey() {\n  return Math.random().toString(36).slice(2, 10)\n}\n\nfunction handleHardNavigation({\n  url,\n  router,\n}: {\n  url: string\n  router: Router\n}) {\n  // ensure we don't trigger a hard navigation to the same\n  // URL as this can end up with an infinite refresh\n  if (url === addBasePath(addLocale(router.asPath, router.locale))) {\n    throw new Error(\n      `Invariant: attempted to hard navigate to the same URL ${url} ${location.href}`\n    )\n  }\n  window.location.href = url\n}\n\nconst getCancelledHandler = ({\n  route,\n  router,\n}: {\n  route: string\n  router: Router\n}) => {\n  let cancelled = false\n  const cancel = (router.clc = () => {\n    cancelled = true\n  })\n\n  const handleCancelled = () => {\n    if (cancelled) {\n      const error: any = new Error(\n        `Abort fetching component for route: \"${route}\"`\n      )\n      error.cancelled = true\n      throw error\n    }\n\n    if (cancel === router.clc) {\n      router.clc = null\n    }\n  }\n  return handleCancelled\n}\n\nexport default class Router implements BaseRouter {\n  basePath: string\n\n  /**\n   * Map of all components loaded in `Router`\n   */\n  components: { [pathname: string]: PrivateRouteInfo }\n  // Server Data Cache (full data requests)\n  sdc: NextDataCache = {}\n  // Server Background Cache (HEAD requests)\n  sbc: NextDataCache = {}\n\n  sub: Subscription\n  clc: ComponentLoadCancel\n  pageLoader: PageLoader\n  _bps: BeforePopStateCallback | undefined\n  events: MittEmitter<RouterEvent>\n  _wrapApp: (App: AppComponent) => any\n  isSsr: boolean\n  _inFlightRoute?: string | undefined\n  _shallow?: boolean | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isReady: boolean\n  isLocaleDomain: boolean\n  isFirstPopStateEvent = true\n  _initialMatchesMiddlewarePromise: Promise<boolean>\n  // static entries filter\n  _bfl_s?: import('../../lib/bloom-filter').BloomFilter\n  // dynamic entires filter\n  _bfl_d?: import('../../lib/bloom-filter').BloomFilter\n\n  private state: Readonly<{\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    asPath: string\n    locale: string | undefined\n    isFallback: boolean\n    isPreview: boolean\n  }>\n\n  private _key: string = createKey()\n\n  static events: MittEmitter<RouterEvent> = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    {\n      initialProps,\n      pageLoader,\n      App,\n      wrapApp,\n      Component,\n      err,\n      subscription,\n      isFallback,\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview,\n    }: {\n      subscription: Subscription\n      initialProps: any\n      pageLoader: any\n      Component: ComponentType\n      App: AppComponent\n      wrapApp: (WrapAppComponent: AppComponent) => any\n      err?: Error\n      isFallback: boolean\n      locale?: string\n      locales?: readonly string[]\n      defaultLocale?: string\n      domainLocales?: readonly DomainLocale[]\n      isPreview?: boolean\n    }\n  ) {\n    // represents the current component key\n    const route = removeTrailingSlash(pathname)\n\n    // set up the component cache (by route keys)\n    this.components = {}\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP,\n      }\n    }\n\n    this.components['/_app'] = {\n      Component: App as ComponentType,\n      styleSheets: [\n        /* /_app does not need its stylesheets managed */\n      ],\n    }\n\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events\n\n    this.pageLoader = pageLoader\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic =\n      isDynamicRoute(pathname) && self.__NEXT_DATA__.autoExport\n\n    this.basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\n    this.sub = subscription\n    this.clc = null\n    this._wrapApp = wrapApp\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true\n    this.isLocaleDomain = false\n    this.isReady = !!(\n      self.__NEXT_DATA__.gssp ||\n      self.__NEXT_DATA__.gip ||\n      self.__NEXT_DATA__.isExperimentalCompile ||\n      (self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp) ||\n      (!autoExportDynamic &&\n        !self.location.search &&\n        !process.env.__NEXT_HAS_REWRITES)\n    )\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locales = locales\n      this.defaultLocale = defaultLocale\n      this.domainLocales = domainLocales\n      this.isLocaleDomain = !!detectDomainLocale(\n        domainLocales,\n        self.location.hostname\n      )\n    }\n\n    this.state = {\n      route,\n      pathname,\n      query,\n      asPath: autoExportDynamic ? pathname : as,\n      isPreview: !!isPreview,\n      locale: process.env.__NEXT_I18N_SUPPORT ? locale : undefined,\n      isFallback,\n    }\n\n    this._initialMatchesMiddlewarePromise = Promise.resolve(false)\n\n    if (typeof window !== 'undefined') {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (!as.startsWith('//')) {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        const options: TransitionOptions = { locale }\n        const asPath = getURL()\n\n        this._initialMatchesMiddlewarePromise = matchesMiddleware({\n          router: this,\n          locale,\n          asPath,\n        }).then((matches) => {\n          // if middleware matches we leave resolving to the change function\n          // as the server needs to resolve for correct priority\n          ;(options as any)._shouldResolveHref = as !== pathname\n\n          this.changeState(\n            'replaceState',\n            matches\n              ? asPath\n              : formatWithValidation({\n                  pathname: addBasePath(pathname),\n                  query,\n                }),\n            asPath,\n            options\n          )\n          return matches\n        })\n      }\n\n      window.addEventListener('popstate', this.onPopState)\n\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (process.env.__NEXT_SCROLL_RESTORATION) {\n        if (manualScrollRestoration) {\n          window.history.scrollRestoration = 'manual'\n        }\n      }\n    }\n  }\n\n  onPopState = (e: PopStateEvent): void => {\n    const { isFirstPopStateEvent } = this\n    this.isFirstPopStateEvent = false\n\n    const state = e.state as HistoryState\n\n    if (!state) {\n      // We get state as undefined for two reasons.\n      //  1. With older safari (< 8) and older chrome (< 34)\n      //  2. When the URL changed with #\n      //\n      // In the both cases, we don't need to proceed and change the route.\n      // (as it's already changed)\n      // But we can simply replace the state with the new changes.\n      // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n      // So, doing the following for (1) does no harm.\n      const { pathname, query } = this\n      this.changeState(\n        'replaceState',\n        formatWithValidation({ pathname: addBasePath(pathname), query }),\n        getURL()\n      )\n      return\n    }\n\n    // __NA is used to identify if the history entry can be handled by the app-router.\n    if (state.__NA) {\n      window.location.reload()\n      return\n    }\n\n    if (!state.__N) {\n      return\n    }\n\n    // Safari fires popstateevent when reopening the browser.\n    if (\n      isFirstPopStateEvent &&\n      this.locale === state.options.locale &&\n      state.as === this.asPath\n    ) {\n      return\n    }\n\n    let forcedScroll: { x: number; y: number } | undefined\n    const { url, as, options, key } = state\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      if (manualScrollRestoration) {\n        if (this._key !== key) {\n          // Snapshot current scroll position:\n          try {\n            sessionStorage.setItem(\n              '__next_scroll_' + this._key,\n              JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n            )\n          } catch {}\n\n          // Restore old scroll position:\n          try {\n            const v = sessionStorage.getItem('__next_scroll_' + key)\n            forcedScroll = JSON.parse(v!)\n          } catch {\n            forcedScroll = { x: 0, y: 0 }\n          }\n        }\n      }\n    }\n    this._key = key\n\n    const { pathname } = parseRelativeUrl(url)\n\n    // Make sure we don't re-render on initial load,\n    // can be caused by navigating back from an external site\n    if (\n      this.isSsr &&\n      as === addBasePath(this.asPath) &&\n      pathname === addBasePath(this.pathname)\n    ) {\n      return\n    }\n\n    // If the downstream application returns falsy, return.\n    // They will then be responsible for handling the event.\n    if (this._bps && !this._bps(state)) {\n      return\n    }\n\n    this.change(\n      'replaceState',\n      url,\n      as,\n      Object.assign<{}, TransitionOptions, TransitionOptions>({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n        // @ts-ignore internal value not exposed on types\n        _h: 0,\n      }),\n      forcedScroll\n    )\n  }\n\n  reload(): void {\n    window.location.reload()\n  }\n\n  /**\n   * Go back in history\n   */\n  back() {\n    window.history.back()\n  }\n\n  /**\n   * Go forward in history\n   */\n  forward() {\n    window.history.forward()\n  }\n\n  /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  push(url: Url, as?: Url, options: TransitionOptions = {}) {\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      // TODO: remove in the future when we update history before route change\n      // is complete, as the popstate event should handle this capture.\n      if (manualScrollRestoration) {\n        try {\n          // Snapshot scroll position right before navigating to a new page:\n          sessionStorage.setItem(\n            '__next_scroll_' + this._key,\n            JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n          )\n        } catch {}\n      }\n    }\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('pushState', url, as, options)\n  }\n\n  /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  replace(url: Url, as?: Url, options: TransitionOptions = {}) {\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('replaceState', url, as, options)\n  }\n\n  async _bfl(\n    as: string,\n    resolvedAs?: string,\n    locale?: string | false,\n    skipNavigate?: boolean\n  ) {\n    if (process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED) {\n      if (!this._bfl_s && !this._bfl_d) {\n        const { BloomFilter } =\n          require('../../lib/bloom-filter') as typeof import('../../lib/bloom-filter')\n\n        type Filter = ReturnType<\n          import('../../lib/bloom-filter').BloomFilter['export']\n        >\n        let staticFilterData: Filter | undefined\n        let dynamicFilterData: Filter | undefined\n\n        try {\n          ;({\n            __routerFilterStatic: staticFilterData,\n            __routerFilterDynamic: dynamicFilterData,\n          } = (await getClientBuildManifest()) as any as {\n            __routerFilterStatic?: Filter\n            __routerFilterDynamic?: Filter\n          })\n        } catch (err) {\n          // failed to load build manifest hard navigate\n          // to be safe\n          console.error(err)\n          if (skipNavigate) {\n            return true\n          }\n          handleHardNavigation({\n            url: addBasePath(\n              addLocale(as, locale || this.locale, this.defaultLocale)\n            ),\n            router: this,\n          })\n          return new Promise(() => {})\n        }\n\n        const routerFilterSValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_S_FILTER as any\n\n        if (!staticFilterData && routerFilterSValue) {\n          staticFilterData = routerFilterSValue ? routerFilterSValue : undefined\n        }\n\n        const routerFilterDValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_D_FILTER as any\n\n        if (!dynamicFilterData && routerFilterDValue) {\n          dynamicFilterData = routerFilterDValue\n            ? routerFilterDValue\n            : undefined\n        }\n\n        if (staticFilterData?.numHashes) {\n          this._bfl_s = new BloomFilter(\n            staticFilterData.numItems,\n            staticFilterData.errorRate\n          )\n          this._bfl_s.import(staticFilterData)\n        }\n\n        if (dynamicFilterData?.numHashes) {\n          this._bfl_d = new BloomFilter(\n            dynamicFilterData.numItems,\n            dynamicFilterData.errorRate\n          )\n          this._bfl_d.import(dynamicFilterData)\n        }\n      }\n\n      let matchesBflStatic = false\n      let matchesBflDynamic = false\n      const pathsToCheck: Array<{ as?: string; allowMatchCurrent?: boolean }> =\n        [{ as }, { as: resolvedAs }]\n\n      for (const { as: curAs, allowMatchCurrent } of pathsToCheck) {\n        if (curAs) {\n          const asNoSlash = removeTrailingSlash(\n            new URL(curAs, 'http://n').pathname\n          )\n          const asNoSlashLocale = addBasePath(\n            addLocale(asNoSlash, locale || this.locale)\n          )\n\n          if (\n            allowMatchCurrent ||\n            asNoSlash !==\n              removeTrailingSlash(new URL(this.asPath, 'http://n').pathname)\n          ) {\n            matchesBflStatic =\n              matchesBflStatic ||\n              !!this._bfl_s?.contains(asNoSlash) ||\n              !!this._bfl_s?.contains(asNoSlashLocale)\n\n            for (const normalizedAS of [asNoSlash, asNoSlashLocale]) {\n              // if any sub-path of as matches a dynamic filter path\n              // it should be hard navigated\n              const curAsParts = normalizedAS.split('/')\n              for (\n                let i = 0;\n                !matchesBflDynamic && i < curAsParts.length + 1;\n                i++\n              ) {\n                const currentPart = curAsParts.slice(0, i).join('/')\n                if (currentPart && this._bfl_d?.contains(currentPart)) {\n                  matchesBflDynamic = true\n                  break\n                }\n              }\n            }\n\n            // if the client router filter is matched then we trigger\n            // a hard navigation\n            if (matchesBflStatic || matchesBflDynamic) {\n              if (skipNavigate) {\n                return true\n              }\n              handleHardNavigation({\n                url: addBasePath(\n                  addLocale(as, locale || this.locale, this.defaultLocale)\n                ),\n                router: this,\n              })\n              return new Promise(() => {})\n            }\n          }\n        }\n      }\n    }\n    return false\n  }\n\n  private async change(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions,\n    forcedScroll?: { x: number; y: number }\n  ): Promise<boolean> {\n    if (!isLocalURL(url)) {\n      handleHardNavigation({ url, router: this })\n      return false\n    }\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    const isQueryUpdating = (options as any)._h === 1\n\n    if (!isQueryUpdating && !options.shallow) {\n      await this._bfl(as, undefined, options.locale)\n    }\n\n    let shouldResolveHref =\n      isQueryUpdating ||\n      (options as any)._shouldResolveHref ||\n      parsePath(url).pathname === parsePath(as).pathname\n\n    const nextState = {\n      ...this.state,\n    }\n\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    // or a navigation has occurred\n    const readyStateChange = this.isReady !== true\n    this.isReady = true\n    const isSsr = this.isSsr\n\n    if (!isQueryUpdating) {\n      this.isSsr = false\n    }\n\n    // if a route transition is already in progress before\n    // the query updating is triggered ignore query updating\n    if (isQueryUpdating && this.clc) {\n      return false\n    }\n\n    const prevLocale = nextState.locale\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      nextState.locale =\n        options.locale === false\n          ? this.defaultLocale\n          : options.locale || nextState.locale\n\n      if (typeof options.locale === 'undefined') {\n        options.locale = nextState.locale\n      }\n\n      const parsedAs = parseRelativeUrl(\n        hasBasePath(as) ? removeBasePath(as) : as\n      )\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        this.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        nextState.locale = localePathResult.detectedLocale\n        parsedAs.pathname = addBasePath(parsedAs.pathname)\n        as = formatWithValidation(parsedAs)\n        url = addBasePath(\n          normalizeLocalePath(\n            hasBasePath(url) ? removeBasePath(url) : url,\n            this.locales\n          ).pathname\n        )\n      }\n      let didNavigate = false\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if the locale isn't configured hard navigate to show 404 page\n        if (!this.locales?.includes(nextState.locale!)) {\n          parsedAs.pathname = addLocale(parsedAs.pathname, nextState.locale)\n          handleHardNavigation({\n            url: formatWithValidation(parsedAs),\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      const detectedDomain = detectDomainLocale(\n        this.domainLocales,\n        undefined,\n        nextState.locale\n      )\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if we are navigating to a domain locale ensure we redirect to the\n        // correct domain\n        if (\n          !didNavigate &&\n          detectedDomain &&\n          this.isLocaleDomain &&\n          self.location.hostname !== detectedDomain.domain\n        ) {\n          const asNoBasePath = removeBasePath(as)\n          handleHardNavigation({\n            url: `http${detectedDomain.http ? '' : 's'}://${\n              detectedDomain.domain\n            }${addBasePath(\n              `${\n                nextState.locale === detectedDomain.defaultLocale\n                  ? ''\n                  : `/${nextState.locale}`\n              }${asNoBasePath === '/' ? '' : asNoBasePath}` || '/'\n            )}`,\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      if (didNavigate) {\n        return new Promise(() => {})\n      }\n    }\n\n    // marking route changes as a navigation start entry\n    if (ST) {\n      performance.mark('routeChange')\n    }\n\n    const { shallow = false, scroll = true } = options\n    const routeProps = { shallow }\n\n    if (this._inFlightRoute && this.clc) {\n      if (!isSsr) {\n        Router.events.emit(\n          'routeChangeError',\n          buildCancellationError(),\n          this._inFlightRoute,\n          routeProps\n        )\n      }\n      this.clc()\n      this.clc = null\n    }\n\n    as = addBasePath(\n      addLocale(\n        hasBasePath(as) ? removeBasePath(as) : as,\n        options.locale,\n        this.defaultLocale\n      )\n    )\n    const cleanedAs = removeLocale(\n      hasBasePath(as) ? removeBasePath(as) : as,\n      nextState.locale\n    )\n    this._inFlightRoute = as\n\n    const localeChange = prevLocale !== nextState.locale\n\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n\n    if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n      nextState.asPath = cleanedAs\n      Router.events.emit('hashChangeStart', as, routeProps)\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, {\n        ...options,\n        scroll: false,\n      })\n      if (scroll) {\n        this.scrollToHash(cleanedAs)\n      }\n      try {\n        await this.set(nextState, this.components[nextState.route], null)\n      } catch (err) {\n        if (isError(err) && err.cancelled) {\n          Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n        }\n        throw err\n      }\n\n      Router.events.emit('hashChangeComplete', as, routeProps)\n      return true\n    }\n\n    let parsed = parseRelativeUrl(url)\n    let { pathname, query } = parsed\n\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages: string[], rewrites: any\n    try {\n      ;[pages, { __rewrites: rewrites }] = await Promise.all([\n        this.pageLoader.getPageList(),\n        getClientBuildManifest(),\n        this.pageLoader.getMiddleware(),\n      ])\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState'\n    }\n\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as\n\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname\n      ? removeTrailingSlash(removeBasePath(pathname))\n      : pathname\n\n    let route = removeTrailingSlash(pathname)\n    const parsedAsPathname = as.startsWith('/') && parseRelativeUrl(as).pathname\n\n    // if we detected the path as app route during prefetching\n    // trigger hard navigation\n    if ((this.components[pathname] as any)?.__appRouter) {\n      handleHardNavigation({ url: as, router: this })\n      return new Promise(() => {})\n    }\n\n    const isMiddlewareRewrite = !!(\n      parsedAsPathname &&\n      route !== parsedAsPathname &&\n      (!isDynamicRoute(route) ||\n        !getRouteMatcher(getRouteRegex(route))(parsedAsPathname))\n    )\n\n    // we don't attempt resolve asPath when we need to execute\n    // middleware as the resolving will occur server-side\n    const isMiddlewareMatch =\n      !options.shallow &&\n      (await matchesMiddleware({\n        asPath: as,\n        locale: nextState.locale,\n        router: this,\n      }))\n\n    if (isQueryUpdating && isMiddlewareMatch) {\n      shouldResolveHref = false\n    }\n\n    if (shouldResolveHref && pathname !== '/_error') {\n      ;(options as any)._shouldResolveHref = true\n\n      if (process.env.__NEXT_HAS_REWRITES && as.startsWith('/')) {\n        const rewritesResult = resolveRewrites(\n          addBasePath(addLocale(cleanedAs, nextState.locale), true),\n          pages,\n          rewrites,\n          query,\n          (p: string) => resolveDynamicRoute(p, pages),\n          this.locales\n        )\n\n        if (rewritesResult.externalDest) {\n          handleHardNavigation({ url: as, router: this })\n          return true\n        }\n        if (!isMiddlewareMatch) {\n          resolvedAs = rewritesResult.asPath\n        }\n\n        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n          // if this directly matches a page we need to update the href to\n          // allow the correct page chunk to be loaded\n          pathname = rewritesResult.resolvedHref\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      } else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages)\n\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      }\n    }\n\n    if (!isLocalURL(as)) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` +\n            `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`\n        )\n      }\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    resolvedAs = removeLocale(removeBasePath(resolvedAs), nextState.locale)\n\n    route = removeTrailingSlash(pathname)\n    let routeMatch: Params | false = false\n\n    if (isDynamicRoute(route)) {\n      const parsedAs = parseRelativeUrl(resolvedAs)\n      const asPathname = parsedAs.pathname\n\n      const routeRegex = getRouteRegex(route)\n      routeMatch = getRouteMatcher(routeRegex)(asPathname)\n      const shouldInterpolate = route === asPathname\n      const interpolatedAs = shouldInterpolate\n        ? interpolateAs(route, asPathname, query)\n        : ({} as { result: undefined; params: undefined })\n\n      if (!routeMatch || (shouldInterpolate && !interpolatedAs.result)) {\n        const missingParams = Object.keys(routeRegex.groups).filter(\n          (param) => !query[param] && !routeRegex.groups[param].optional\n        )\n\n        if (missingParams.length > 0 && !isMiddlewareMatch) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\n              `${\n                shouldInterpolate\n                  ? `Interpolating href`\n                  : `Mismatching \\`as\\` and \\`href\\``\n              } failed to manually provide ` +\n                `the params: ${missingParams.join(\n                  ', '\n                )} in the \\`href\\`'s \\`query\\``\n            )\n          }\n\n          throw new Error(\n            (shouldInterpolate\n              ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\n                  ', '\n                )}) to be interpolated properly. `\n              : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) +\n              `Read more: https://nextjs.org/docs/messages/${\n                shouldInterpolate\n                  ? 'href-interpolation-failed'\n                  : 'incompatible-href-as'\n              }`\n          )\n        }\n      } else if (shouldInterpolate) {\n        as = formatWithValidation(\n          Object.assign({}, parsedAs, {\n            pathname: interpolatedAs.result,\n            query: omit(query, interpolatedAs.params!),\n          })\n        )\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch)\n      }\n    }\n\n    if (!isQueryUpdating) {\n      Router.events.emit('routeChangeStart', as, routeProps)\n    }\n\n    const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error'\n\n    try {\n      let routeInfo = await this.getRouteInfo({\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps,\n        locale: nextState.locale,\n        isPreview: nextState.isPreview,\n        hasMiddleware: isMiddlewareMatch,\n        unstable_skipClientCache: options.unstable_skipClientCache,\n        isQueryUpdating: isQueryUpdating && !this.isFallback,\n        isMiddlewareRewrite,\n      })\n\n      if (!isQueryUpdating && !options.shallow) {\n        await this._bfl(\n          as,\n          'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined,\n          nextState.locale\n        )\n      }\n\n      if ('route' in routeInfo && isMiddlewareMatch) {\n        pathname = routeInfo.route || route\n        route = pathname\n\n        if (!routeProps.shallow) {\n          query = Object.assign({}, routeInfo.query || {}, query)\n        }\n\n        const cleanedParsedPathname = hasBasePath(parsed.pathname)\n          ? removeBasePath(parsed.pathname)\n          : parsed.pathname\n\n        if (routeMatch && pathname !== cleanedParsedPathname) {\n          Object.keys(routeMatch).forEach((key) => {\n            if (routeMatch && query[key] === routeMatch[key]) {\n              delete query[key]\n            }\n          })\n        }\n\n        if (isDynamicRoute(pathname)) {\n          const prefixedAs =\n            !routeProps.shallow && routeInfo.resolvedAs\n              ? routeInfo.resolvedAs\n              : addBasePath(\n                  addLocale(\n                    new URL(as, location.href).pathname,\n                    nextState.locale\n                  ),\n                  true\n                )\n\n          let rewriteAs = prefixedAs\n\n          if (hasBasePath(rewriteAs)) {\n            rewriteAs = removeBasePath(rewriteAs)\n          }\n\n          if (process.env.__NEXT_I18N_SUPPORT) {\n            const localeResult = normalizeLocalePath(rewriteAs, this.locales)\n            nextState.locale = localeResult.detectedLocale || nextState.locale\n            rewriteAs = localeResult.pathname\n          }\n          const routeRegex = getRouteRegex(pathname)\n          const curRouteMatch = getRouteMatcher(routeRegex)(\n            new URL(rewriteAs, location.href).pathname\n          )\n\n          if (curRouteMatch) {\n            Object.assign(query, curRouteMatch)\n          }\n        }\n      }\n\n      // If the routeInfo brings a redirect we simply apply it.\n      if ('type' in routeInfo) {\n        if (routeInfo.type === 'redirect-internal') {\n          return this.change(method, routeInfo.newUrl, routeInfo.newAs, options)\n        } else {\n          handleHardNavigation({ url: routeInfo.destination, router: this })\n          return new Promise(() => {})\n        }\n      }\n\n      const component: any = routeInfo.Component\n      if (component && component.unstable_scriptLoader) {\n        const scripts = [].concat(component.unstable_scriptLoader())\n\n        scripts.forEach((script: any) => {\n          handleClientScriptLoad(script.props)\n        })\n      }\n\n      // handle redirect on client-transition\n      if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n        if (\n          routeInfo.props.pageProps &&\n          routeInfo.props.pageProps.__N_REDIRECT\n        ) {\n          // Use the destination from redirect without adding locale\n          options.locale = false\n\n          const destination = routeInfo.props.pageProps.__N_REDIRECT\n\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (\n            destination.startsWith('/') &&\n            routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false\n          ) {\n            const parsedHref = parseRelativeUrl(destination)\n            parsedHref.pathname = resolveDynamicRoute(\n              parsedHref.pathname,\n              pages\n            )\n\n            const { url: newUrl, as: newAs } = prepareUrlAs(\n              this,\n              destination,\n              destination\n            )\n            return this.change(method, newUrl, newAs, options)\n          }\n          handleHardNavigation({ url: destination, router: this })\n          return new Promise(() => {})\n        }\n\n        nextState.isPreview = !!routeInfo.props.__N_PREVIEW\n\n        // handle SSG data 404\n        if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute\n\n          try {\n            await this.fetchComponent('/404')\n            notFoundRoute = '/404'\n          } catch (_) {\n            notFoundRoute = '/_error'\n          }\n\n          routeInfo = await this.getRouteInfo({\n            route: notFoundRoute,\n            pathname: notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            routeProps: { shallow: false },\n            locale: nextState.locale,\n            isPreview: nextState.isPreview,\n            isNotFound: true,\n          })\n\n          if ('type' in routeInfo) {\n            throw new Error(`Unexpected middleware effect on /404`)\n          }\n        }\n      }\n\n      if (\n        isQueryUpdating &&\n        this.pathname === '/_error' &&\n        self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n        routeInfo.props?.pageProps\n      ) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        routeInfo.props.pageProps.statusCode = 500\n      }\n\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute =\n        options.shallow && nextState.route === (routeInfo.route ?? route)\n\n      const shouldScroll =\n        options.scroll ?? (!isQueryUpdating && !isValidShallowRoute)\n      const resetScroll = shouldScroll ? { x: 0, y: 0 } : null\n      const upcomingScrollState = forcedScroll ?? resetScroll\n\n      // the new state that the router gonna set\n      const upcomingRouterState = {\n        ...nextState,\n        route,\n        pathname,\n        query,\n        asPath: cleanedAs,\n        isFallback: false,\n      }\n\n      // When the page being rendered is the 404 page, we should only update the\n      // query parameters. Route changes here might add the basePath when it\n      // wasn't originally present. This is also why this block is before the\n      // below `changeState` call which updates the browser's history (changing\n      // the URL).\n      if (isQueryUpdating && isErrorRoute) {\n        routeInfo = await this.getRouteInfo({\n          route: this.pathname,\n          pathname: this.pathname,\n          query,\n          as,\n          resolvedAs,\n          routeProps: { shallow: false },\n          locale: nextState.locale,\n          isPreview: nextState.isPreview,\n          isQueryUpdating: isQueryUpdating && !this.isFallback,\n        })\n\n        if ('type' in routeInfo) {\n          throw new Error(`Unexpected middleware effect on ${this.pathname}`)\n        }\n\n        if (\n          this.pathname === '/_error' &&\n          self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n          routeInfo.props?.pageProps\n        ) {\n          // ensure statusCode is still correct for static 500 page\n          // when updating query information\n          routeInfo.props.pageProps.statusCode = 500\n        }\n\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (err) {\n          if (isError(err) && err.cancelled) {\n            Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n          }\n          throw err\n        }\n\n        return true\n      }\n\n      Router.events.emit('beforeHistoryChange', as, routeProps)\n      this.changeState(method, url, as, options)\n\n      // for query updates we can skip it if the state is unchanged and we don't\n      // need to scroll\n      // https://github.com/vercel/next.js/issues/37139\n      const canSkipUpdating =\n        isQueryUpdating &&\n        !upcomingScrollState &&\n        !readyStateChange &&\n        !localeChange &&\n        compareRouterStates(upcomingRouterState, this.state)\n\n      if (!canSkipUpdating) {\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (e: any) {\n          if (e.cancelled) routeInfo.error = routeInfo.error || e\n          else throw e\n        }\n\n        if (routeInfo.error) {\n          if (!isQueryUpdating) {\n            Router.events.emit(\n              'routeChangeError',\n              routeInfo.error,\n              cleanedAs,\n              routeProps\n            )\n          }\n\n          throw routeInfo.error\n        }\n\n        if (process.env.__NEXT_I18N_SUPPORT) {\n          if (nextState.locale) {\n            document.documentElement.lang = nextState.locale\n          }\n        }\n\n        if (!isQueryUpdating) {\n          Router.events.emit('routeChangeComplete', as, routeProps)\n        }\n\n        // A hash mark # is the optional last part of a URL\n        const hashRegex = /#.+$/\n        if (shouldScroll && hashRegex.test(as)) {\n          this.scrollToHash(as)\n        }\n      }\n\n      return true\n    } catch (err) {\n      if (isError(err) && err.cancelled) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  changeState(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions = {}\n  ): void {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof window.history === 'undefined') {\n        console.error(`Warning: window.history is not available.`)\n        return\n      }\n\n      if (typeof window.history[method] === 'undefined') {\n        console.error(`Warning: window.history.${method} is not available`)\n        return\n      }\n    }\n\n    if (method !== 'pushState' || getURL() !== as) {\n      this._shallow = options.shallow\n      window.history[method](\n        {\n          url,\n          as,\n          options,\n          __N: true,\n          key: (this._key = method !== 'pushState' ? this._key : createKey()),\n        } as HistoryState,\n        // Most browsers currently ignores this parameter, although they may use it in the future.\n        // Passing the empty string here should be safe against future changes to the method.\n        // https://developer.mozilla.org/docs/Web/API/History/replaceState\n        '',\n        as\n      )\n    }\n  }\n\n  async handleRouteInfoError(\n    err: Error & { code?: any; cancelled?: boolean },\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    routeProps: RouteProperties,\n    loadErrorFail?: boolean\n  ): Promise<CompletePrivateRouteInfo> {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err\n    }\n\n    if (isAssetError(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps)\n\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n\n      // So, doing a hard reload is the proper way to deal with this.\n      handleHardNavigation({\n        url: as,\n        router: this,\n      })\n\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError()\n    }\n\n    console.error(err)\n\n    try {\n      let props: Record<string, any> | undefined\n      const { page: Component, styleSheets } =\n        await this.fetchComponent('/_error')\n\n      const routeInfo: CompletePrivateRouteInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err,\n      }\n\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query,\n          } as any)\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr)\n          routeInfo.props = {}\n        }\n      }\n\n      return routeInfo\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError(\n        isError(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + ''),\n        pathname,\n        query,\n        as,\n        routeProps,\n        true\n      )\n    }\n  }\n\n  async getRouteInfo({\n    route: requestedRoute,\n    pathname,\n    query,\n    as,\n    resolvedAs,\n    routeProps,\n    locale,\n    hasMiddleware,\n    isPreview,\n    unstable_skipClientCache,\n    isQueryUpdating,\n    isMiddlewareRewrite,\n    isNotFound,\n  }: {\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    as: string\n    resolvedAs: string\n    hasMiddleware?: boolean\n    routeProps: RouteProperties\n    locale: string | undefined\n    isPreview: boolean\n    unstable_skipClientCache?: boolean\n    isQueryUpdating?: boolean\n    isMiddlewareRewrite?: boolean\n    isNotFound?: boolean\n  }) {\n    /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */\n    let route = requestedRoute\n\n    try {\n      let existingInfo: PrivateRouteInfo | undefined = this.components[route]\n      if (routeProps.shallow && existingInfo && this.route === route) {\n        return existingInfo\n      }\n\n      const handleCancelled = getCancelledHandler({ route, router: this })\n\n      if (hasMiddleware) {\n        existingInfo = undefined\n      }\n\n      let cachedRouteInfo =\n        existingInfo &&\n        !('initial' in existingInfo) &&\n        process.env.NODE_ENV !== 'development'\n          ? existingInfo\n          : undefined\n\n      const isBackground = isQueryUpdating\n      const fetchNextDataParams: FetchNextDataParams = {\n        dataHref: this.pageLoader.getDataHref({\n          href: formatWithValidation({ pathname, query }),\n          skipInterpolation: true,\n          asPath: isNotFound ? '/404' : resolvedAs,\n          locale,\n        }),\n        hasMiddleware: true,\n        isServerRender: this.isSsr,\n        parseJSON: true,\n        inflightCache: isBackground ? this.sbc : this.sdc,\n        persistCache: !isPreview,\n        isPrefetch: false,\n        unstable_skipClientCache,\n        isBackground,\n      }\n\n      let data:\n        | WithMiddlewareEffectsOutput\n        | (Pick<WithMiddlewareEffectsOutput, 'json'> &\n            Omit<Partial<WithMiddlewareEffectsOutput>, 'json'>)\n        | null =\n        isQueryUpdating && !isMiddlewareRewrite\n          ? null\n          : await withMiddlewareEffects({\n              fetchData: () => fetchNextData(fetchNextDataParams),\n              asPath: isNotFound ? '/404' : resolvedAs,\n              locale: locale,\n              router: this,\n            }).catch((err) => {\n              // we don't hard error during query updating\n              // as it's un-necessary and doesn't need to be fatal\n              // unless it is a fallback route and the props can't\n              // be loaded\n              if (isQueryUpdating) {\n                return null\n              }\n              throw err\n            })\n\n      // when rendering error routes we don't apply middleware\n      // effects\n      if (data && (pathname === '/_error' || pathname === '/404')) {\n        data.effect = undefined\n      }\n\n      if (isQueryUpdating) {\n        if (!data) {\n          data = { json: self.__NEXT_DATA__.props }\n        } else {\n          data.json = self.__NEXT_DATA__.props\n        }\n      }\n\n      handleCancelled()\n\n      if (\n        data?.effect?.type === 'redirect-internal' ||\n        data?.effect?.type === 'redirect-external'\n      ) {\n        return data.effect\n      }\n\n      if (data?.effect?.type === 'rewrite') {\n        const resolvedRoute = removeTrailingSlash(data.effect.resolvedHref)\n        const pages = await this.pageLoader.getPageList()\n\n        // during query updating the page must match although during\n        // client-transition a redirect that doesn't match a page\n        // can be returned and this should trigger a hard navigation\n        // which is valid for incremental migration\n        if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n          route = resolvedRoute\n          pathname = data.effect.resolvedHref\n          query = { ...query, ...data.effect.parsedAs.query }\n          resolvedAs = removeBasePath(\n            normalizeLocalePath(data.effect.parsedAs.pathname, this.locales)\n              .pathname\n          )\n\n          // Check again the cache with the new destination.\n          existingInfo = this.components[route]\n          if (\n            routeProps.shallow &&\n            existingInfo &&\n            this.route === route &&\n            !hasMiddleware\n          ) {\n            // If we have a match with the current route due to rewrite,\n            // we can copy the existing information to the rewritten one.\n            // Then, we return the information along with the matched route.\n            return { ...existingInfo, route }\n          }\n        }\n      }\n\n      if (isAPIRoute(route)) {\n        handleHardNavigation({ url: as, router: this })\n        return new Promise<never>(() => {})\n      }\n\n      const routeInfo =\n        cachedRouteInfo ||\n        (await this.fetchComponent(route).then<CompletePrivateRouteInfo>(\n          (res) => ({\n            Component: res.page,\n            styleSheets: res.styleSheets,\n            __N_SSG: res.mod.__N_SSG,\n            __N_SSP: res.mod.__N_SSP,\n          })\n        ))\n\n      if (process.env.NODE_ENV !== 'production') {\n        const { isValidElementType } =\n          require('next/dist/compiled/react-is') as typeof import('next/dist/compiled/react-is')\n        if (!isValidElementType(routeInfo.Component)) {\n          throw new Error(\n            `The default export is not a React Component in page: \"${pathname}\"`\n          )\n        }\n      }\n      const wasBailedPrefetch = data?.response?.headers.get('x-middleware-skip')\n\n      const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP\n\n      // For non-SSG prefetches that bailed before sending data\n      // we clear the cache to fetch full response\n      if (wasBailedPrefetch && data?.dataHref) {\n        delete this.sdc[data.dataHref]\n      }\n\n      const { props, cacheKey } = await this._getData(async () => {\n        if (shouldFetchData) {\n          if (data?.json && !wasBailedPrefetch) {\n            return { cacheKey: data.cacheKey, props: data.json }\n          }\n\n          const dataHref = data?.dataHref\n            ? data.dataHref\n            : this.pageLoader.getDataHref({\n                href: formatWithValidation({ pathname, query }),\n                asPath: resolvedAs,\n                locale,\n              })\n\n          const fetched = await fetchNextData({\n            dataHref,\n            isServerRender: this.isSsr,\n            parseJSON: true,\n            inflightCache: wasBailedPrefetch ? {} : this.sdc,\n            persistCache: !isPreview,\n            isPrefetch: false,\n            unstable_skipClientCache,\n          })\n\n          return {\n            cacheKey: fetched.cacheKey,\n            props: fetched.json || {},\n          }\n        }\n\n        return {\n          headers: {},\n          props: await this.getInitialProps(\n            routeInfo.Component,\n            // we provide AppTree later so this needs to be `any`\n            {\n              pathname,\n              query,\n              asPath: as,\n              locale,\n              locales: this.locales,\n              defaultLocale: this.defaultLocale,\n            } as any\n          ),\n        }\n      })\n\n      // Only bust the data cache for SSP routes although\n      // middleware can skip cache per request with\n      // x-middleware-cache: no-cache as well\n      if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n        delete this.sdc[cacheKey]\n      }\n\n      // we kick off a HEAD request in the background\n      // when a non-prefetch request is made to signal revalidation\n      if (\n        !this.isPreview &&\n        routeInfo.__N_SSG &&\n        process.env.NODE_ENV !== 'development' &&\n        !isQueryUpdating\n      ) {\n        fetchNextData(\n          Object.assign({}, fetchNextDataParams, {\n            isBackground: true,\n            persistCache: false,\n            inflightCache: this.sbc,\n          })\n        ).catch(() => {})\n      }\n\n      props.pageProps = Object.assign({}, props.pageProps)\n      routeInfo.props = props\n      routeInfo.route = route\n      routeInfo.query = query\n      routeInfo.resolvedAs = resolvedAs\n      this.components[route] = routeInfo\n\n      return routeInfo\n    } catch (err) {\n      return this.handleRouteInfoError(\n        getProperError(err),\n        pathname,\n        query,\n        as,\n        routeProps\n      )\n    }\n  }\n\n  private set(\n    state: typeof this.state,\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    this.state = state\n\n    return this.sub(\n      data,\n      this.components['/_app'].Component as AppComponent,\n      resetScroll\n    )\n  }\n\n  /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */\n  beforePopState(cb: BeforePopStateCallback) {\n    this._bps = cb\n  }\n\n  onlyAHashChange(as: string): boolean {\n    if (!this.asPath) return false\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2)\n    const [newUrlNoHash, newHash] = as.split('#', 2)\n\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true\n    }\n\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false\n    }\n\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash\n  }\n\n  scrollToHash(as: string): void {\n    const [, hash = ''] = as.split('#', 2)\n\n    disableSmoothScrollDuringRouteTransition(\n      () => {\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === '' || hash === 'top') {\n          window.scrollTo(0, 0)\n          return\n        }\n\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash)\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash)\n        if (idEl) {\n          idEl.scrollIntoView()\n          return\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0]\n        if (nameEl) {\n          nameEl.scrollIntoView()\n        }\n      },\n      {\n        onlyHashChange: this.onlyAHashChange(as),\n      }\n    )\n  }\n\n  urlIsNew(asPath: string): boolean {\n    return this.asPath !== asPath\n  }\n\n  /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */\n  async prefetch(\n    url: string,\n    asPath: string = url,\n    options: PrefetchOptions = {}\n  ): Promise<void> {\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (process.env.NODE_ENV !== 'production') {\n      return\n    }\n\n    if (typeof window !== 'undefined' && isBot(window.navigator.userAgent)) {\n      // No prefetches for bots that render the link since they are typically navigating\n      // links via the equivalent of a hard navigation and hence never utilize these\n      // prefetches.\n      return\n    }\n    let parsed = parseRelativeUrl(url)\n    const urlPathname = parsed.pathname\n\n    let { pathname, query } = parsed\n    const originalPathname = pathname\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      if (options.locale === false) {\n        pathname = normalizeLocalePath!(pathname, this.locales).pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n\n        let parsedAs = parseRelativeUrl(asPath)\n        const localePathResult = normalizeLocalePath!(\n          parsedAs.pathname,\n          this.locales\n        )\n        parsedAs.pathname = localePathResult.pathname\n        options.locale = localePathResult.detectedLocale || this.defaultLocale\n        asPath = formatWithValidation(parsedAs)\n      }\n    }\n\n    const pages = await this.pageLoader.getPageList()\n    let resolvedAs = asPath\n\n    const locale =\n      typeof options.locale !== 'undefined'\n        ? options.locale || undefined\n        : this.locale\n\n    const isMiddlewareMatch = await matchesMiddleware({\n      asPath: asPath,\n      locale: locale,\n      router: this,\n    })\n\n    if (process.env.__NEXT_HAS_REWRITES && asPath.startsWith('/')) {\n      let rewrites: any\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n\n      const rewritesResult = resolveRewrites(\n        addBasePath(addLocale(asPath, this.locale), true),\n        pages,\n        rewrites,\n        parsed.query,\n        (p: string) => resolveDynamicRoute(p, pages),\n        this.locales\n      )\n\n      if (rewritesResult.externalDest) {\n        return\n      }\n\n      if (!isMiddlewareMatch) {\n        resolvedAs = removeLocale(\n          removeBasePath(rewritesResult.asPath),\n          this.locale\n        )\n      }\n\n      if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n        // if this directly matches a page we need to update the href to\n        // allow the correct page chunk to be loaded\n        pathname = rewritesResult.resolvedHref\n        parsed.pathname = pathname\n\n        if (!isMiddlewareMatch) {\n          url = formatWithValidation(parsed)\n        }\n      }\n    }\n    parsed.pathname = resolveDynamicRoute(parsed.pathname, pages)\n\n    if (isDynamicRoute(parsed.pathname)) {\n      pathname = parsed.pathname\n      parsed.pathname = pathname\n      Object.assign(\n        query,\n        getRouteMatcher(getRouteRegex(parsed.pathname))(\n          parsePath(asPath).pathname\n        ) || {}\n      )\n\n      if (!isMiddlewareMatch) {\n        url = formatWithValidation(parsed)\n      }\n    }\n\n    const data =\n      process.env.__NEXT_MIDDLEWARE_PREFETCH === 'strict'\n        ? null\n        : await withMiddlewareEffects({\n            fetchData: () =>\n              fetchNextData({\n                dataHref: this.pageLoader.getDataHref({\n                  href: formatWithValidation({\n                    pathname: originalPathname,\n                    query,\n                  }),\n                  skipInterpolation: true,\n                  asPath: resolvedAs,\n                  locale,\n                }),\n                hasMiddleware: true,\n                isServerRender: false,\n                parseJSON: true,\n                inflightCache: this.sdc,\n                persistCache: !this.isPreview,\n                isPrefetch: true,\n              }),\n            asPath: asPath,\n            locale: locale,\n            router: this,\n          })\n\n    /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */\n    if (data?.effect.type === 'rewrite') {\n      parsed.pathname = data.effect.resolvedHref\n      pathname = data.effect.resolvedHref\n      query = { ...query, ...data.effect.parsedAs.query }\n      resolvedAs = data.effect.parsedAs.pathname\n      url = formatWithValidation(parsed)\n    }\n\n    /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */\n    if (data?.effect.type === 'redirect-external') {\n      return\n    }\n\n    const route = removeTrailingSlash(pathname)\n\n    if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n      this.components[urlPathname] = { __appRouter: true } as any\n    }\n\n    await Promise.all([\n      this.pageLoader._isSsg(route).then((isSsg) => {\n        return isSsg\n          ? fetchNextData({\n              dataHref: data?.json\n                ? data?.dataHref\n                : this.pageLoader.getDataHref({\n                    href: url,\n                    asPath: resolvedAs,\n                    locale: locale,\n                  }),\n              isServerRender: false,\n              parseJSON: true,\n              inflightCache: this.sdc,\n              persistCache: !this.isPreview,\n              isPrefetch: true,\n              unstable_skipClientCache:\n                options.unstable_skipClientCache ||\n                (options.priority &&\n                  !!process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE),\n            })\n              .then(() => false)\n              .catch(() => false)\n          : false\n      }),\n      this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route),\n    ])\n  }\n\n  async fetchComponent(route: string) {\n    const handleCancelled = getCancelledHandler({ route, router: this })\n\n    try {\n      const componentResult = await this.pageLoader.loadPage(route)\n      handleCancelled()\n\n      return componentResult\n    } catch (err) {\n      handleCancelled()\n      throw err\n    }\n  }\n\n  _getData<T>(fn: () => Promise<T>): Promise<T> {\n    let cancelled = false\n    const cancel = () => {\n      cancelled = true\n    }\n    this.clc = cancel\n    return fn().then((data) => {\n      if (cancel === this.clc) {\n        this.clc = null\n      }\n\n      if (cancelled) {\n        const err: any = new Error('Loading initial props cancelled')\n        err.cancelled = true\n        throw err\n      }\n\n      return data\n    })\n  }\n\n  getInitialProps(\n    Component: ComponentType,\n    ctx: NextPageContext\n  ): Promise<Record<string, any>> {\n    const { Component: App } = this.components['/_app']\n    const AppTree = this._wrapApp(App as AppComponent)\n    ctx.AppTree = AppTree\n    return loadGetInitialProps<AppContextType<Router>>(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx,\n    })\n  }\n\n  get route(): string {\n    return this.state.route\n  }\n\n  get pathname(): string {\n    return this.state.pathname\n  }\n\n  get query(): ParsedUrlQuery {\n    return this.state.query\n  }\n\n  get asPath(): string {\n    return this.state.asPath\n  }\n\n  get locale(): string | undefined {\n    return this.state.locale\n  }\n\n  get isFallback(): boolean {\n    return this.state.isFallback\n  }\n\n  get isPreview(): boolean {\n    return this.state.isPreview\n  }\n}\n"], "names": ["removeTrailingSlash", "getClientBuildManifest", "isAssetError", "<PERSON><PERSON><PERSON><PERSON>", "handleClientScriptLoad", "isError", "getProperError", "denormalizePagePath", "normalizeLocalePath", "mitt", "getLocationOrigin", "getURL", "loadGetInitialProps", "ST", "isDynamicRoute", "parseRelativeUrl", "getRouteMatcher", "getRouteRegex", "formatWithValidation", "detectDomainLocale", "parsePath", "addLocale", "removeLocale", "removeBasePath", "addBasePath", "has<PERSON>ase<PERSON><PERSON>", "resolveHref", "isAPIRoute", "getNextPathnameInfo", "formatNextPathnameInfo", "compareRouterStates", "isLocalURL", "isBot", "omit", "interpolateAs", "disableSmoothScrollDuringRouteTransition", "MATCHED_PATH_HEADER", "resolveRewrites", "process", "env", "__NEXT_HAS_REWRITES", "require", "default", "buildCancellationError", "Object", "assign", "Error", "cancelled", "matchesMiddleware", "options", "matchers", "Promise", "resolve", "router", "page<PERSON><PERSON>der", "getMiddleware", "pathname", "asPathname", "<PERSON><PERSON><PERSON>", "cleanedAs", "asWithBasePathAndLocale", "locale", "some", "m", "RegExp", "regexp", "test", "strip<PERSON><PERSON>in", "url", "origin", "startsWith", "substring", "length", "prepareUrlAs", "as", "resolvedHref", "resolvedAs", "hrefWasAbsolute", "asWasAbsolute", "preparedUrl", "preparedAs", "resolveDynamicRoute", "pages", "cleanPathname", "includes", "page", "re", "getMiddlewareData", "source", "response", "nextConfig", "basePath", "i18n", "locales", "trailingSlash", "Boolean", "__NEXT_TRAILING_SLASH", "rewriteHeader", "headers", "get", "rewriteTarget", "<PERSON><PERSON><PERSON>", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "parsedRewriteTarget", "pathnameInfo", "parseData", "fsPathname", "all", "getPageList", "then", "__rewrites", "rewrites", "parsedSource", "undefined", "result", "query", "path", "matchedPage", "parsedAs", "resolvedPathname", "matches", "type", "src", "defaultLocale", "buildId", "destination", "hash", "redirectTarget", "newAs", "newUrl", "withMiddlewareEffects", "fetchData", "data", "effect", "dataHref", "json", "text", "cache<PERSON>ey", "manualScrollRestoration", "__NEXT_SCROLL_RESTORATION", "window", "history", "v", "sessionStorage", "setItem", "removeItem", "n", "SSG_DATA_NOT_FOUND", "Symbol", "fetchRetry", "attempts", "fetch", "credentials", "method", "ok", "status", "tryToParseAsJSON", "JSON", "parse", "error", "fetchNextData", "inflightCache", "isPrefetch", "hasMiddleware", "isServerRender", "parseJSON", "persistCache", "isBackground", "unstable_skipClientCache", "href", "URL", "location", "getData", "params", "purpose", "NEXT_DEPLOYMENT_ID", "notFound", "NODE_ENV", "catch", "err", "message", "create<PERSON><PERSON>", "Math", "random", "toString", "slice", "handleHardNavigation", "getCancelledHandler", "route", "cancel", "clc", "handleCancelled", "Router", "reload", "back", "forward", "push", "_key", "stringify", "x", "self", "pageXOffset", "y", "pageYOffset", "change", "replace", "_bfl", "skipNavigate", "__NEXT_CLIENT_ROUTER_FILTER_ENABLED", "_bfl_s", "_bfl_d", "<PERSON><PERSON><PERSON><PERSON>", "staticFilterData", "dynamicFilterData", "__routerFilterStatic", "__routerFilterDynamic", "console", "routerFilterSValue", "__NEXT_CLIENT_ROUTER_S_FILTER", "routerFilterDValue", "__NEXT_CLIENT_ROUTER_D_FILTER", "numHashes", "numItems", "errorRate", "import", "matchesBflStatic", "matchesBflDynamic", "pathsToCheck", "curAs", "allowMatchCurrent", "asNoSlash", "asNoSlashLocale", "contains", "normalizedAS", "curAs<PERSON><PERSON>s", "split", "i", "currentPart", "join", "forcedScroll", "isQueryUpdating", "_h", "shallow", "shouldResolveHref", "_shouldResolveHref", "nextState", "state", "readyStateChange", "isReady", "isSsr", "prevLocale", "__NEXT_I18N_SUPPORT", "localePathResult", "detectedLocale", "didNavigate", "detectedDomain", "domainLocales", "isLocaleDomain", "hostname", "domain", "asNoBasePath", "http", "performance", "mark", "scroll", "routeProps", "_inFlightRoute", "events", "emit", "localeChange", "onlyAHashChange", "changeState", "scrollToHash", "set", "components", "parsed", "urlIsNew", "parsedAsPathname", "__appRouter", "isMiddlewareRewrite", "isMiddlewareMatch", "rewritesResult", "p", "externalDest", "routeMatch", "routeRegex", "shouldInterpolate", "interpolatedAs", "missingParams", "keys", "groups", "filter", "param", "optional", "warn", "isErrorRoute", "routeInfo", "getRouteInfo", "isPreview", "<PERSON><PERSON><PERSON><PERSON>", "cleanedParsedPathname", "for<PERSON>ach", "key", "prefixedAs", "rewriteAs", "localeResult", "cur<PERSON><PERSON>eMatch", "component", "Component", "unstable_scriptLoader", "scripts", "concat", "script", "props", "__N_SSG", "__N_SSP", "pageProps", "__N_REDIRECT", "__N_REDIRECT_BASE_PATH", "parsedHref", "__N_PREVIEW", "notFoundRoute", "fetchComponent", "_", "isNotFound", "__NEXT_DATA__", "statusCode", "isValidShallowRoute", "shouldScroll", "resetScroll", "upcomingScrollState", "upcomingRouterState", "canSkipUpdating", "e", "document", "documentElement", "lang", "hashRegex", "_shallow", "__N", "handleRouteInfoError", "loadErrorFail", "styleSheets", "getInitialProps", "gipErr", "routeInfoErr", "requestedRoute", "existingInfo", "cachedRouteInfo", "fetchNextDataParams", "getDataHref", "skipInterpolation", "sbc", "sdc", "resolvedRoute", "res", "mod", "isValidElementType", "wasBailedPrefetch", "shouldFetchData", "_getData", "fetched", "sub", "beforePopState", "cb", "_bps", "oldUrlNoHash", "oldHash", "newUrlNoHash", "newHash", "scrollTo", "rawHash", "decodeURIComponent", "idEl", "getElementById", "scrollIntoView", "nameEl", "getElementsByName", "onlyHashChange", "prefetch", "navigator", "userAgent", "urlPathname", "originalPathname", "__NEXT_MIDDLEWARE_PREFETCH", "_isSsg", "isSsg", "priority", "__NEXT_OPTIMISTIC_CLIENT_CACHE", "componentResult", "loadPage", "fn", "ctx", "App", "AppTree", "_wrapApp", "constructor", "initialProps", "wrapApp", "subscription", "isFirstPopStateEvent", "onPopState", "__NA", "getItem", "initial", "autoExportDynamic", "autoExport", "__NEXT_ROUTER_BASEPATH", "gssp", "gip", "isExperimentalCompile", "appGip", "gsp", "search", "_initialMatchesMiddlewarePromise", "addEventListener", "scrollRestoration"], "mappings": "AAAA,4BAA4B;AAU5B,SAASA,mBAAmB,QAAQ,gCAA+B;AACnE,SACEC,sBAAsB,EACtBC,YAAY,EACZC,cAAc,QACT,+BAA8B;AACrC,SAASC,sBAAsB,QAAQ,yBAAwB;AAC/D,OAAOC,WAAWC,cAAc,QAAQ,wBAAuB;AAC/D,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SAASC,mBAAmB,QAAQ,gCAA+B;AACnE,OAAOC,UAAU,UAAS;AAC1B,SAASC,iBAAiB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,EAAE,QAAQ,WAAU;AAC7E,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,gBAAgB,QAAQ,6BAA4B;AAC7D,SAASC,eAAe,QAAQ,wBAAuB;AACvD,SAASC,aAAa,QAAQ,sBAAqB;AACnD,SAASC,oBAAoB,QAAQ,qBAAoB;AACzD,SAASC,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,SAAS,QAAQ,6BAA4B;AACtD,SAASC,YAAY,QAAQ,gCAA+B;AAC5D,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,WAAW,QAAQ,+BAA8B;AAC1D,SAASC,UAAU,QAAQ,4BAA2B;AACtD,SAASC,mBAAmB,QAAQ,iCAAgC;AACpE,SAASC,sBAAsB,QAAQ,oCAAmC;AAC1E,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,KAAK,QAAQ,iBAAgB;AACtC,SAASC,IAAI,QAAQ,eAAc;AACnC,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,wCAAwC,QAAQ,gCAA+B;AAExF,SAASC,mBAAmB,QAAQ,yBAAwB;AAE5D,IAAIC;AACJ,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;IACnCH,kBAAkB,AAChBI,QAAQ,4BACRC,OAAO;AACX;AAgCA,SAASC;IACP,OAAOC,OAAOC,MAAM,CAAC,qBAA4B,CAA5B,IAAIC,MAAM,oBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2B,IAAG;QACjDC,WAAW;IACb;AACF;AASA,OAAO,eAAeC,kBACpBC,OAAkC;IAElC,MAAMC,WAAW,MAAMC,QAAQC,OAAO,CACpCH,QAAQI,MAAM,CAACC,UAAU,CAACC,aAAa;IAEzC,IAAI,CAACL,UAAU,OAAO;IAEtB,MAAM,EAAEM,UAAUC,UAAU,EAAE,GAAGrC,UAAU6B,QAAQS,MAAM;IACzD,6FAA6F;IAC7F,MAAMC,YAAYlC,YAAYgC,cAC1BlC,eAAekC,cACfA;IACJ,MAAMG,0BAA0BpC,YAC9BH,UAAUsC,WAAWV,QAAQY,MAAM;IAGrC,2EAA2E;IAC3E,uEAAuE;IACvE,OAAOX,SAASY,IAAI,CAAC,CAACC,IACpB,IAAIC,OAAOD,EAAEE,MAAM,EAAEC,IAAI,CAACN;AAE9B;AAEA,SAASO,YAAYC,GAAW;IAC9B,MAAMC,SAAS3D;IAEf,OAAO0D,IAAIE,UAAU,CAACD,UAAUD,IAAIG,SAAS,CAACF,OAAOG,MAAM,IAAIJ;AACjE;AAEA,SAASK,aAAapB,MAAkB,EAAEe,GAAQ,EAAEM,EAAQ;IAC1D,sDAAsD;IACtD,kDAAkD;IAClD,IAAI,CAACC,cAAcC,WAAW,GAAGlD,YAAY2B,QAAQe,KAAK;IAC1D,MAAMC,SAAS3D;IACf,MAAMmE,kBAAkBF,aAAaL,UAAU,CAACD;IAChD,MAAMS,gBAAgBF,cAAcA,WAAWN,UAAU,CAACD;IAE1DM,eAAeR,YAAYQ;IAC3BC,aAAaA,aAAaT,YAAYS,cAAcA;IAEpD,MAAMG,cAAcF,kBAAkBF,eAAenD,YAAYmD;IACjE,MAAMK,aAAaN,KACfP,YAAYzC,YAAY2B,QAAQqB,OAChCE,cAAcD;IAElB,OAAO;QACLP,KAAKW;QACLL,IAAII,gBAAgBE,aAAaxD,YAAYwD;IAC/C;AACF;AAEA,SAASC,oBAAoBzB,QAAgB,EAAE0B,KAAe;IAC5D,MAAMC,gBAAgBnF,oBAAoBO,oBAAoBiD;IAC9D,IAAI2B,kBAAkB,UAAUA,kBAAkB,WAAW;QAC3D,OAAO3B;IACT;IAEA,2CAA2C;IAC3C,IAAI,CAAC0B,MAAME,QAAQ,CAACD,gBAAgB;QAClC,iDAAiD;QACjDD,MAAMpB,IAAI,CAAC,CAACuB;YACV,IAAIvE,eAAeuE,SAASpE,cAAcoE,MAAMC,EAAE,CAACpB,IAAI,CAACiB,gBAAgB;gBACtE3B,WAAW6B;gBACX,OAAO;YACT;QACF;IACF;IACA,OAAOrF,oBAAoBwD;AAC7B;AAEA,SAAS+B,kBACPC,MAAc,EACdC,QAAkB,EAClBxC,OAAkC;IAElC,MAAMyC,aAAa;QACjBC,UAAU1C,QAAQI,MAAM,CAACsC,QAAQ;QACjCC,MAAM;YAAEC,SAAS5C,QAAQI,MAAM,CAACwC,OAAO;QAAC;QACxCC,eAAeC,QAAQzD,QAAQC,GAAG,CAACyD,qBAAqB;IAC1D;IACA,MAAMC,gBAAgBR,SAASS,OAAO,CAACC,GAAG,CAAC;IAE3C,IAAIC,gBACFH,iBAAiBR,SAASS,OAAO,CAACC,GAAG,CAAC;IAExC,MAAME,cAAcZ,SAASS,OAAO,CAACC,GAAG,CAAC/D;IAEzC,IACEiE,eACA,CAACD,iBACD,CAACC,YAAYjB,QAAQ,CAAC,2BACtB,CAACiB,YAAYjB,QAAQ,CAAC,cACtB,CAACiB,YAAYjB,QAAQ,CAAC,SACtB;QACA,4DAA4D;QAC5DgB,gBAAgBC;IAClB;IAEA,IAAID,eAAe;QACjB,IACEA,cAAc9B,UAAU,CAAC,QACzBhC,QAAQC,GAAG,CAAC+D,0CAA0C,EACtD;YACA,MAAMC,sBAAsBxF,iBAAiBqF;YAC7C,MAAMI,eAAe5E,oBAAoB2E,oBAAoB/C,QAAQ,EAAE;gBACrEkC;gBACAe,WAAW;YACb;YAEA,IAAIC,aAAa1G,oBAAoBwG,aAAahD,QAAQ;YAC1D,OAAOL,QAAQwD,GAAG,CAAC;gBACjB1D,QAAQI,MAAM,CAACC,UAAU,CAACsD,WAAW;gBACrC3G;aACD,EAAE4G,IAAI,CAAC;oBAAC,CAAC3B,OAAO,EAAE4B,YAAYC,QAAQ,EAAE,CAAM;gBAC7C,IAAIrC,KAAKrD,UAAUmF,aAAahD,QAAQ,EAAEgD,aAAa3C,MAAM;gBAE7D,IACE/C,eAAe4D,OACd,CAACuB,iBACAf,MAAME,QAAQ,CACZ5E,oBAAoBe,eAAemD,KAAKzB,QAAQI,MAAM,CAACwC,OAAO,EAC3DrC,QAAQ,GAEf;oBACA,MAAMwD,eAAepF,oBACnBb,iBAAiByE,QAAQhC,QAAQ,EACjC;wBACEkC,YAAYpD,QAAQC,GAAG,CAACC,mBAAmB,GACvCyE,YACAvB;wBACJe,WAAW;oBACb;oBAGF/B,KAAKlD,YAAYwF,aAAaxD,QAAQ;oBACtC+C,oBAAoB/C,QAAQ,GAAGkB;gBACjC;gBAEA,IAAIpC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;oBACnC,MAAM0E,SAAS7E,gBACbqC,IACAQ,OACA6B,UACAR,oBAAoBY,KAAK,EACzB,CAACC,OAAiBnC,oBAAoBmC,MAAMlC,QAC5CjC,QAAQI,MAAM,CAACwC,OAAO;oBAGxB,IAAIqB,OAAOG,WAAW,EAAE;wBACtBd,oBAAoB/C,QAAQ,GAAG0D,OAAOI,QAAQ,CAAC9D,QAAQ;wBACvDkB,KAAK6B,oBAAoB/C,QAAQ;wBACjCZ,OAAOC,MAAM,CAAC0D,oBAAoBY,KAAK,EAAED,OAAOI,QAAQ,CAACH,KAAK;oBAChE;gBACF,OAAO,IAAI,CAACjC,MAAME,QAAQ,CAACsB,aAAa;oBACtC,MAAMa,mBAAmBtC,oBAAoByB,YAAYxB;oBAEzD,IAAIqC,qBAAqBb,YAAY;wBACnCA,aAAaa;oBACf;gBACF;gBAEA,MAAM5C,eAAe,CAACO,MAAME,QAAQ,CAACsB,cACjCzB,oBACEzE,oBACEe,eAAegF,oBAAoB/C,QAAQ,GAC3CP,QAAQI,MAAM,CAACwC,OAAO,EACtBrC,QAAQ,EACV0B,SAEFwB;gBAEJ,IAAI5F,eAAe6D,eAAe;oBAChC,MAAM6C,UAAUxG,gBAAgBC,cAAc0D,eAAeD;oBAC7D9B,OAAOC,MAAM,CAAC0D,oBAAoBY,KAAK,EAAEK,WAAW,CAAC;gBACvD;gBAEA,OAAO;oBACLC,MAAM;oBACNH,UAAUf;oBACV5B;gBACF;YACF;QACF;QACA,MAAM+C,MAAMtG,UAAUoE;QACtB,MAAMhC,WAAW3B,uBAAuB;YACtC,GAAGD,oBAAoB8F,IAAIlE,QAAQ,EAAE;gBAAEkC;gBAAYe,WAAW;YAAK,EAAE;YACrEkB,eAAe1E,QAAQI,MAAM,CAACsE,aAAa;YAC3CC,SAAS;QACX;QAEA,OAAOzE,QAAQC,OAAO,CAAC;YACrBqE,MAAM;YACNI,aAAa,AAAC,KAAErE,WAAWkE,IAAIP,KAAK,GAAGO,IAAII,IAAI;QACjD;IACF;IAEA,MAAMC,iBAAiBtC,SAASS,OAAO,CAACC,GAAG,CAAC;IAE5C,IAAI4B,gBAAgB;QAClB,IAAIA,eAAezD,UAAU,CAAC,MAAM;YAClC,MAAMoD,MAAMtG,UAAU2G;YACtB,MAAMvE,WAAW3B,uBAAuB;gBACtC,GAAGD,oBAAoB8F,IAAIlE,QAAQ,EAAE;oBAAEkC;oBAAYe,WAAW;gBAAK,EAAE;gBACrEkB,eAAe1E,QAAQI,MAAM,CAACsE,aAAa;gBAC3CC,SAAS;YACX;YAEA,OAAOzE,QAAQC,OAAO,CAAC;gBACrBqE,MAAM;gBACNO,OAAO,AAAC,KAAExE,WAAWkE,IAAIP,KAAK,GAAGO,IAAII,IAAI;gBACzCG,QAAQ,AAAC,KAAEzE,WAAWkE,IAAIP,KAAK,GAAGO,IAAII,IAAI;YAC5C;QACF;QAEA,OAAO3E,QAAQC,OAAO,CAAC;YACrBqE,MAAM;YACNI,aAAaE;QACf;IACF;IAEA,OAAO5E,QAAQC,OAAO,CAAC;QAAEqE,MAAM;IAAgB;AACjD;AAMA,eAAeS,sBACbjF,OAAkC;IAElC,MAAMuE,UAAU,MAAMxE,kBAAkBC;IACxC,IAAI,CAACuE,WAAW,CAACvE,QAAQkF,SAAS,EAAE;QAClC,OAAO;IACT;IAEA,MAAMC,OAAO,MAAMnF,QAAQkF,SAAS;IAEpC,MAAME,SAAS,MAAM9C,kBAAkB6C,KAAKE,QAAQ,EAAEF,KAAK3C,QAAQ,EAAExC;IAErE,OAAO;QACLqF,UAAUF,KAAKE,QAAQ;QACvBC,MAAMH,KAAKG,IAAI;QACf9C,UAAU2C,KAAK3C,QAAQ;QACvB+C,MAAMJ,KAAKI,IAAI;QACfC,UAAUL,KAAKK,QAAQ;QACvBJ;IACF;AACF;AAyEA,MAAMK,0BACJpG,QAAQC,GAAG,CAACoG,yBAAyB,IACrC,OAAOC,WAAW,eAClB,uBAAuBA,OAAOC,OAAO,IACrC,CAAC,CAAC,AAAC;IACD,IAAI;QACF,IAAIC,IAAI;QACR,wCAAwC;QACxC,OAAOC,eAAeC,OAAO,CAACF,GAAGA,IAAIC,eAAeE,UAAU,CAACH,IAAI;IACrE,EAAE,OAAOI,GAAG,CAAC;AACf;AAEF,MAAMC,qBAAqBC,OAAO;AAElC,SAASC,WACPjF,GAAW,EACXkF,QAAgB,EAChBrG,OAAgD;IAEhD,OAAOsG,MAAMnF,KAAK;QAChB,sEAAsE;QACtE,yDAAyD;QACzD,EAAE;QACF,oEAAoE;QACpE,YAAY;QACZ,mEAAmE;QACnE,EAAE;QACF,iEAAiE;QACjE,sEAAsE;QACtE,8CAA8C;QAC9C,0CAA0C;QAC1CoF,aAAa;QACbC,QAAQxG,QAAQwG,MAAM,IAAI;QAC1BvD,SAAStD,OAAOC,MAAM,CAAC,CAAC,GAAGI,QAAQiD,OAAO,EAAE;YAC1C,iBAAiB;QACnB;IACF,GAAGW,IAAI,CAAC,CAACpB;QACP,OAAO,CAACA,SAASiE,EAAE,IAAIJ,WAAW,KAAK7D,SAASkE,MAAM,IAAI,MACtDN,WAAWjF,KAAKkF,WAAW,GAAGrG,WAC9BwC;IACN;AACF;AAsBA,SAASmE,iBAAiBpB,IAAY;IACpC,IAAI;QACF,OAAOqB,KAAKC,KAAK,CAACtB;IACpB,EAAE,OAAOuB,OAAO;QACd,OAAO;IACT;AACF;AAEA,SAASC,cAAc,KAUD;IAVC,IAAA,EACrB1B,QAAQ,EACR2B,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,SAAS,EACTC,YAAY,EACZC,YAAY,EACZC,wBAAwB,EACJ,GAVC;IAWrB,MAAM,EAAEC,MAAMhC,QAAQ,EAAE,GAAG,IAAIiC,IAAIpC,UAAUM,OAAO+B,QAAQ,CAACF,IAAI;IACjE,MAAMG,UAAU,CAACC;YAULA;eATVxB,WAAWf,UAAU8B,iBAAiB,IAAI,GAAG;YAC3ClE,SAAStD,OAAOC,MAAM,CACpB,CAAC,GACDqH,aAAa;gBAAEY,SAAS;YAAW,IAAI,CAAC,GACxCZ,cAAcC,gBAAgB;gBAAE,yBAAyB;YAAI,IAAI,CAAC,GAClE7H,QAAQC,GAAG,CAACwI,kBAAkB,GAC1B;gBAAE,mBAAmBzI,QAAQC,GAAG,CAACwI,kBAAkB;YAAC,IACpD,CAAC;YAEPtB,QAAQoB,CAAAA,iBAAAA,0BAAAA,OAAQpB,MAAM,YAAdoB,iBAAkB;QAC5B,GACGhE,IAAI,CAAC,CAACpB;YACL,IAAIA,SAASiE,EAAE,IAAImB,CAAAA,0BAAAA,OAAQpB,MAAM,MAAK,QAAQ;gBAC5C,OAAO;oBAAEnB;oBAAU7C;oBAAU+C,MAAM;oBAAID,MAAM,CAAC;oBAAGE;gBAAS;YAC5D;YAEA,OAAOhD,SAAS+C,IAAI,GAAG3B,IAAI,CAAC,CAAC2B;gBAC3B,IAAI,CAAC/C,SAASiE,EAAE,EAAE;oBAChB;;;;;aAKC,GACD,IACES,iBACA;wBAAC;wBAAK;wBAAK;wBAAK;qBAAI,CAAC/E,QAAQ,CAACK,SAASkE,MAAM,GAC7C;wBACA,OAAO;4BAAErB;4BAAU7C;4BAAU+C;4BAAMD,MAAM,CAAC;4BAAGE;wBAAS;oBACxD;oBAEA,IAAIhD,SAASkE,MAAM,KAAK,KAAK;4BACvBC;wBAAJ,KAAIA,oBAAAA,iBAAiBpB,0BAAjBoB,kBAAwBoB,QAAQ,EAAE;4BACpC,OAAO;gCACL1C;gCACAC,MAAM;oCAAEyC,UAAU7B;gCAAmB;gCACrC1D;gCACA+C;gCACAC;4BACF;wBACF;oBACF;oBAEA,MAAMsB,QAAQ,qBAAwC,CAAxC,IAAIjH,MAAO,gCAAX,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuC;oBAErD;;;;aAIC,GACD,IAAI,CAACsH,gBAAgB;wBACnBjK,eAAe4J;oBACjB;oBAEA,MAAMA;gBACR;gBAEA,OAAO;oBACLzB;oBACAC,MAAM8B,YAAYT,iBAAiBpB,QAAQ;oBAC3C/C;oBACA+C;oBACAC;gBACF;YACF;QACF,GACC5B,IAAI,CAAC,CAACuB;YACL,IACE,CAACkC,gBACDhI,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,gBACzB7C,KAAK3C,QAAQ,CAACS,OAAO,CAACC,GAAG,CAAC,0BAA0B,YACpD;gBACA,OAAO8D,aAAa,CAACxB,SAAS;YAChC;YACA,OAAOL;QACT,GACC8C,KAAK,CAAC,CAACC;YACN,IAAI,CAACX,0BAA0B;gBAC7B,OAAOP,aAAa,CAACxB,SAAS;YAChC;YACA,IACE,SAAS;YACT0C,IAAIC,OAAO,KAAK,qBAChB,UAAU;YACVD,IAAIC,OAAO,KAAK,qDAChB,SAAS;YACTD,IAAIC,OAAO,KAAK,eAChB;gBACAjL,eAAegL;YACjB;YACA,MAAMA;QACR;;IAEJ,+CAA+C;IAC/C,gDAAgD;IAChD,0DAA0D;IAC1D,2DAA2D;IAC3D,IAAIX,4BAA4BF,cAAc;QAC5C,OAAOM,QAAQ,CAAC,GAAG/D,IAAI,CAAC,CAACuB;YACvB,IAAIA,KAAK3C,QAAQ,CAACS,OAAO,CAACC,GAAG,CAAC,0BAA0B,YAAY;gBAClE,8CAA8C;gBAC9C8D,aAAa,CAACxB,SAAS,GAAGtF,QAAQC,OAAO,CAACgF;YAC5C;YAEA,OAAOA;QACT;IACF;IAEA,IAAI6B,aAAa,CAACxB,SAAS,KAAKxB,WAAW;QACzC,OAAOgD,aAAa,CAACxB,SAAS;IAChC;IACA,OAAQwB,aAAa,CAACxB,SAAS,GAAGmC,QAChCL,eAAe;QAAEd,QAAQ;IAAO,IAAI,CAAC;AAEzC;AAMA,OAAO,SAAS4B;IACd,OAAOC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C;AAEA,SAASC,qBAAqB,KAM7B;IAN6B,IAAA,EAC5BtH,GAAG,EACHf,MAAM,EAIP,GAN6B;IAO5B,wDAAwD;IACxD,kDAAkD;IAClD,IAAIe,QAAQ5C,YAAYH,UAAUgC,OAAOK,MAAM,EAAEL,OAAOQ,MAAM,IAAI;QAChE,MAAM,qBAEL,CAFK,IAAIf,MACR,AAAC,2DAAwDsB,MAAI,MAAGuG,SAASF,IAAI,GADzE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA7B,OAAO+B,QAAQ,CAACF,IAAI,GAAGrG;AACzB;AAEA,MAAMuH,sBAAsB;QAAC,EAC3BC,KAAK,EACLvI,MAAM,EAIP;IACC,IAAIN,YAAY;IAChB,MAAM8I,SAAUxI,OAAOyI,GAAG,GAAG;QAC3B/I,YAAY;IACd;IAEA,MAAMgJ,kBAAkB;QACtB,IAAIhJ,WAAW;YACb,MAAMgH,QAAa,qBAElB,CAFkB,IAAIjH,MACrB,AAAC,0CAAuC8I,QAAM,MAD7B,qBAAA;uBAAA;4BAAA;8BAAA;YAEnB;YACA7B,MAAMhH,SAAS,GAAG;YAClB,MAAMgH;QACR;QAEA,IAAI8B,WAAWxI,OAAOyI,GAAG,EAAE;YACzBzI,OAAOyI,GAAG,GAAG;QACf;IACF;IACA,OAAOC;AACT;AAEe,MAAMC;IA+SnBC,SAAe;QACbrD,OAAO+B,QAAQ,CAACsB,MAAM;IACxB;IAEA;;GAEC,GACDC,OAAO;QACLtD,OAAOC,OAAO,CAACqD,IAAI;IACrB;IAEA;;GAEC,GACDC,UAAU;QACRvD,OAAOC,OAAO,CAACsD,OAAO;IACxB;IAEA;;;;;GAKC,GACDC,KAAKhI,GAAQ,EAAEM,EAAQ,EAAEzB,OAA+B,EAAE;QAAjCA,IAAAA,oBAAAA,UAA6B,CAAC;QACrD,IAAIX,QAAQC,GAAG,CAACoG,yBAAyB,EAAE;YACzC,wEAAwE;YACxE,iEAAiE;YACjE,IAAID,yBAAyB;gBAC3B,IAAI;oBACF,kEAAkE;oBAClEK,eAAeC,OAAO,CACpB,mBAAmB,IAAI,CAACqD,IAAI,EAC5BxC,KAAKyC,SAAS,CAAC;wBAAEC,GAAGC,KAAKC,WAAW;wBAAEC,GAAGF,KAAKG,WAAW;oBAAC;gBAE9D,EAAE,UAAM,CAAC;YACX;QACF;;QACE,CAAA,EAAEvI,GAAG,EAAEM,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEL,KAAKM,GAAE;QAC1C,OAAO,IAAI,CAACkI,MAAM,CAAC,aAAaxI,KAAKM,IAAIzB;IAC3C;IAEA;;;;;GAKC,GACD4J,QAAQzI,GAAQ,EAAEM,EAAQ,EAAEzB,OAA+B,EAAE;QAAjCA,IAAAA,oBAAAA,UAA6B,CAAC;;QACtD,CAAA,EAAEmB,GAAG,EAAEM,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEL,KAAKM,GAAE;QAC1C,OAAO,IAAI,CAACkI,MAAM,CAAC,gBAAgBxI,KAAKM,IAAIzB;IAC9C;IAEA,MAAM6J,KACJpI,EAAU,EACVE,UAAmB,EACnBf,MAAuB,EACvBkJ,YAAsB,EACtB;QACA,IAAIzK,QAAQC,GAAG,CAACyK,mCAAmC,EAAE;YACnD,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;gBAChC,MAAM,EAAEC,WAAW,EAAE,GACnB1K,QAAQ;gBAKV,IAAI2K;gBACJ,IAAIC;gBAEJ,IAAI;;oBACA,CAAA,EACAC,sBAAsBF,gBAAgB,EACtCG,uBAAuBF,iBAAiB,EACzC,GAAI,MAAMpN,wBAGX;gBACF,EAAE,OAAOkL,KAAK;oBACZ,8CAA8C;oBAC9C,aAAa;oBACbqC,QAAQzD,KAAK,CAACoB;oBACd,IAAI4B,cAAc;wBAChB,OAAO;oBACT;oBACArB,qBAAqB;wBACnBtH,KAAK5C,YACHH,UAAUqD,IAAIb,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC8D,aAAa;wBAEzDtE,QAAQ,IAAI;oBACd;oBACA,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEA,MAAMsK,qBAAqCnL,QAAQC,GAAG,CACnDmL,6BAA6B;gBAEhC,IAAI,CAACN,oBAAoBK,oBAAoB;oBAC3CL,mBAAmBK,qBAAqBA,qBAAqBxG;gBAC/D;gBAEA,MAAM0G,qBAAqCrL,QAAQC,GAAG,CACnDqL,6BAA6B;gBAEhC,IAAI,CAACP,qBAAqBM,oBAAoB;oBAC5CN,oBAAoBM,qBAChBA,qBACA1G;gBACN;gBAEA,IAAImG,oCAAAA,iBAAkBS,SAAS,EAAE;oBAC/B,IAAI,CAACZ,MAAM,GAAG,IAAIE,YAChBC,iBAAiBU,QAAQ,EACzBV,iBAAiBW,SAAS;oBAE5B,IAAI,CAACd,MAAM,CAACe,MAAM,CAACZ;gBACrB;gBAEA,IAAIC,qCAAAA,kBAAmBQ,SAAS,EAAE;oBAChC,IAAI,CAACX,MAAM,GAAG,IAAIC,YAChBE,kBAAkBS,QAAQ,EAC1BT,kBAAkBU,SAAS;oBAE7B,IAAI,CAACb,MAAM,CAACc,MAAM,CAACX;gBACrB;YACF;YAEA,IAAIY,mBAAmB;YACvB,IAAIC,oBAAoB;YACxB,MAAMC,eACJ;gBAAC;oBAAEzJ;gBAAG;gBAAG;oBAAEA,IAAIE;gBAAW;aAAE;YAE9B,KAAK,MAAM,EAAEF,IAAI0J,KAAK,EAAEC,iBAAiB,EAAE,IAAIF,aAAc;gBAC3D,IAAIC,OAAO;oBACT,MAAME,YAAYtO,oBAChB,IAAI0K,IAAI0D,OAAO,YAAY5K,QAAQ;oBAErC,MAAM+K,kBAAkB/M,YACtBH,UAAUiN,WAAWzK,UAAU,IAAI,CAACA,MAAM;oBAG5C,IACEwK,qBACAC,cACEtO,oBAAoB,IAAI0K,IAAI,IAAI,CAAChH,MAAM,EAAE,YAAYF,QAAQ,GAC/D;4BAGI,cACA;wBAHJyK,mBACEA,oBACA,CAAC,GAAC,eAAA,IAAI,CAAChB,MAAM,qBAAX,aAAauB,QAAQ,CAACF,eACxB,CAAC,GAAC,gBAAA,IAAI,CAACrB,MAAM,qBAAX,cAAauB,QAAQ,CAACD;wBAE1B,KAAK,MAAME,gBAAgB;4BAACH;4BAAWC;yBAAgB,CAAE;4BACvD,sDAAsD;4BACtD,8BAA8B;4BAC9B,MAAMG,aAAaD,aAAaE,KAAK,CAAC;4BACtC,IACE,IAAIC,IAAI,GACR,CAACV,qBAAqBU,IAAIF,WAAWlK,MAAM,GAAG,GAC9CoK,IACA;oCAEmB;gCADnB,MAAMC,cAAcH,WAAWjD,KAAK,CAAC,GAAGmD,GAAGE,IAAI,CAAC;gCAChD,IAAID,iBAAe,eAAA,IAAI,CAAC3B,MAAM,qBAAX,aAAasB,QAAQ,CAACK,eAAc;oCACrDX,oBAAoB;oCACpB;gCACF;4BACF;wBACF;wBAEA,yDAAyD;wBACzD,oBAAoB;wBACpB,IAAID,oBAAoBC,mBAAmB;4BACzC,IAAInB,cAAc;gCAChB,OAAO;4BACT;4BACArB,qBAAqB;gCACnBtH,KAAK5C,YACHH,UAAUqD,IAAIb,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC8D,aAAa;gCAEzDtE,QAAQ,IAAI;4BACd;4BACA,OAAO,IAAIF,QAAQ,KAAO;wBAC5B;oBACF;gBACF;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAcyJ,OACZnD,MAAqB,EACrBrF,GAAW,EACXM,EAAU,EACVzB,OAA0B,EAC1B8L,YAAuC,EACrB;YA8Ob;QA7OL,IAAI,CAAChN,WAAWqC,MAAM;YACpBsH,qBAAqB;gBAAEtH;gBAAKf,QAAQ,IAAI;YAAC;YACzC,OAAO;QACT;QACA,sEAAsE;QACtE,yEAAyE;QACzE,2BAA2B;QAC3B,MAAM2L,kBAAkB,AAAC/L,QAAgBgM,EAAE,KAAK;QAEhD,IAAI,CAACD,mBAAmB,CAAC/L,QAAQiM,OAAO,EAAE;YACxC,MAAM,IAAI,CAACpC,IAAI,CAACpI,IAAIuC,WAAWhE,QAAQY,MAAM;QAC/C;QAEA,IAAIsL,oBACFH,mBACA,AAAC/L,QAAgBmM,kBAAkB,IACnChO,UAAUgD,KAAKZ,QAAQ,KAAKpC,UAAUsD,IAAIlB,QAAQ;QAEpD,MAAM6L,YAAY;YAChB,GAAG,IAAI,CAACC,KAAK;QACf;QAEA,yDAAyD;QACzD,4DAA4D;QAC5D,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACC,OAAO,KAAK;QAC1C,IAAI,CAACA,OAAO,GAAG;QACf,MAAMC,QAAQ,IAAI,CAACA,KAAK;QAExB,IAAI,CAACT,iBAAiB;YACpB,IAAI,CAACS,KAAK,GAAG;QACf;QAEA,sDAAsD;QACtD,wDAAwD;QACxD,IAAIT,mBAAmB,IAAI,CAAClD,GAAG,EAAE;YAC/B,OAAO;QACT;QAEA,MAAM4D,aAAaL,UAAUxL,MAAM;QAEnC,IAAIvB,QAAQC,GAAG,CAACoN,mBAAmB,EAAE;YACnCN,UAAUxL,MAAM,GACdZ,QAAQY,MAAM,KAAK,QACf,IAAI,CAAC8D,aAAa,GAClB1E,QAAQY,MAAM,IAAIwL,UAAUxL,MAAM;YAExC,IAAI,OAAOZ,QAAQY,MAAM,KAAK,aAAa;gBACzCZ,QAAQY,MAAM,GAAGwL,UAAUxL,MAAM;YACnC;YAEA,MAAMyD,WAAWvG,iBACfU,YAAYiD,MAAMnD,eAAemD,MAAMA;YAEzC,MAAMkL,mBAAmBpP,oBACvB8G,SAAS9D,QAAQ,EACjB,IAAI,CAACqC,OAAO;YAGd,IAAI+J,iBAAiBC,cAAc,EAAE;gBACnCR,UAAUxL,MAAM,GAAG+L,iBAAiBC,cAAc;gBAClDvI,SAAS9D,QAAQ,GAAGhC,YAAY8F,SAAS9D,QAAQ;gBACjDkB,KAAKxD,qBAAqBoG;gBAC1BlD,MAAM5C,YACJhB,oBACEiB,YAAY2C,OAAO7C,eAAe6C,OAAOA,KACzC,IAAI,CAACyB,OAAO,EACZrC,QAAQ;YAEd;YACA,IAAIsM,cAAc;YAElB,wEAAwE;YACxE,0CAA0C;YAC1C,IAAIxN,QAAQC,GAAG,CAACoN,mBAAmB,EAAE;oBAE9B;gBADL,gEAAgE;gBAChE,IAAI,GAAC,gBAAA,IAAI,CAAC9J,OAAO,qBAAZ,cAAcT,QAAQ,CAACiK,UAAUxL,MAAM,IAAI;oBAC9CyD,SAAS9D,QAAQ,GAAGnC,UAAUiG,SAAS9D,QAAQ,EAAE6L,UAAUxL,MAAM;oBACjE6H,qBAAqB;wBACnBtH,KAAKlD,qBAAqBoG;wBAC1BjE,QAAQ,IAAI;oBACd;oBACA,wDAAwD;oBACxD,2DAA2D;oBAC3DyM,cAAc;gBAChB;YACF;YAEA,MAAMC,iBAAiB5O,mBACrB,IAAI,CAAC6O,aAAa,EAClB/I,WACAoI,UAAUxL,MAAM;YAGlB,wEAAwE;YACxE,0CAA0C;YAC1C,IAAIvB,QAAQC,GAAG,CAACoN,mBAAmB,EAAE;gBACnC,oEAAoE;gBACpE,iBAAiB;gBACjB,IACE,CAACG,eACDC,kBACA,IAAI,CAACE,cAAc,IACnBzD,KAAK7B,QAAQ,CAACuF,QAAQ,KAAKH,eAAeI,MAAM,EAChD;oBACA,MAAMC,eAAe7O,eAAemD;oBACpCgH,qBAAqB;wBACnBtH,KAAK,AAAC,SAAM2L,CAAAA,eAAeM,IAAI,GAAG,KAAK,GAAE,IAAE,QACzCN,eAAeI,MAAM,GACpB3O,YACD,AAAC,KACC6N,CAAAA,UAAUxL,MAAM,KAAKkM,eAAepI,aAAa,GAC7C,KACA,AAAC,MAAG0H,UAAUxL,MAAM,AAAC,IACxBuM,CAAAA,iBAAiB,MAAM,KAAKA,YAAW,KAAO;wBAEnD/M,QAAQ,IAAI;oBACd;oBACA,wDAAwD;oBACxD,2DAA2D;oBAC3DyM,cAAc;gBAChB;YACF;YAEA,IAAIA,aAAa;gBACf,OAAO,IAAI3M,QAAQ,KAAO;YAC5B;QACF;QAEA,oDAAoD;QACpD,IAAItC,IAAI;YACNyP,YAAYC,IAAI,CAAC;QACnB;QAEA,MAAM,EAAErB,UAAU,KAAK,EAAEsB,SAAS,IAAI,EAAE,GAAGvN;QAC3C,MAAMwN,aAAa;YAAEvB;QAAQ;QAE7B,IAAI,IAAI,CAACwB,cAAc,IAAI,IAAI,CAAC5E,GAAG,EAAE;YACnC,IAAI,CAAC2D,OAAO;gBACVzD,OAAO2E,MAAM,CAACC,IAAI,CAChB,oBACAjO,0BACA,IAAI,CAAC+N,cAAc,EACnBD;YAEJ;YACA,IAAI,CAAC3E,GAAG;YACR,IAAI,CAACA,GAAG,GAAG;QACb;QAEApH,KAAKlD,YACHH,UACEI,YAAYiD,MAAMnD,eAAemD,MAAMA,IACvCzB,QAAQY,MAAM,EACd,IAAI,CAAC8D,aAAa;QAGtB,MAAMhE,YAAYrC,aAChBG,YAAYiD,MAAMnD,eAAemD,MAAMA,IACvC2K,UAAUxL,MAAM;QAElB,IAAI,CAAC6M,cAAc,GAAGhM;QAEtB,MAAMmM,eAAenB,eAAeL,UAAUxL,MAAM;QAEpD,qDAAqD;QACrD,0DAA0D;QAE1D,IAAI,CAACmL,mBAAmB,IAAI,CAAC8B,eAAe,CAACnN,cAAc,CAACkN,cAAc;YACxExB,UAAU3L,MAAM,GAAGC;YACnBqI,OAAO2E,MAAM,CAACC,IAAI,CAAC,mBAAmBlM,IAAI+L;YAC1C,8DAA8D;YAC9D,IAAI,CAACM,WAAW,CAACtH,QAAQrF,KAAKM,IAAI;gBAChC,GAAGzB,OAAO;gBACVuN,QAAQ;YACV;YACA,IAAIA,QAAQ;gBACV,IAAI,CAACQ,YAAY,CAACrN;YACpB;YACA,IAAI;gBACF,MAAM,IAAI,CAACsN,GAAG,CAAC5B,WAAW,IAAI,CAAC6B,UAAU,CAAC7B,UAAUzD,KAAK,CAAC,EAAE;YAC9D,EAAE,OAAOT,KAAK;gBACZ,IAAI9K,QAAQ8K,QAAQA,IAAIpI,SAAS,EAAE;oBACjCiJ,OAAO2E,MAAM,CAACC,IAAI,CAAC,oBAAoBzF,KAAKxH,WAAW8M;gBACzD;gBACA,MAAMtF;YACR;YAEAa,OAAO2E,MAAM,CAACC,IAAI,CAAC,sBAAsBlM,IAAI+L;YAC7C,OAAO;QACT;QAEA,IAAIU,SAASpQ,iBAAiBqD;QAC9B,IAAI,EAAEZ,QAAQ,EAAE2D,KAAK,EAAE,GAAGgK;QAE1B,yEAAyE;QACzE,2EAA2E;QAC3E,oBAAoB;QACpB,IAAIjM,OAAiB6B;QACrB,IAAI;;YACD,CAAC7B,OAAO,EAAE4B,YAAYC,QAAQ,EAAE,CAAC,GAAG,MAAM5D,QAAQwD,GAAG,CAAC;gBACrD,IAAI,CAACrD,UAAU,CAACsD,WAAW;gBAC3B3G;gBACA,IAAI,CAACqD,UAAU,CAACC,aAAa;aAC9B;QACH,EAAE,OAAO4H,KAAK;YACZ,wEAAwE;YACxE,+BAA+B;YAC/BO,qBAAqB;gBAAEtH,KAAKM;gBAAIrB,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEA,uEAAuE;QACvE,8EAA8E;QAC9E,uDAAuD;QACvD,oEAAoE;QACpE,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAAC+N,QAAQ,CAACzN,cAAc,CAACkN,cAAc;YAC9CpH,SAAS;QACX;QAEA,iEAAiE;QACjE,iDAAiD;QACjD,IAAI7E,aAAaF;QAEjB,6DAA6D;QAC7D,gEAAgE;QAChE,2DAA2D;QAC3DlB,WAAWA,WACPxD,oBAAoBuB,eAAeiC,aACnCA;QAEJ,IAAIoI,QAAQ5L,oBAAoBwD;QAChC,MAAM6N,mBAAmB3M,GAAGJ,UAAU,CAAC,QAAQvD,iBAAiB2D,IAAIlB,QAAQ;QAE5E,0DAA0D;QAC1D,0BAA0B;QAC1B,KAAK,4BAAA,IAAI,CAAC0N,UAAU,CAAC1N,SAAS,qBAA1B,AAAC,0BAAmC8N,WAAW,EAAE;YACnD5F,qBAAqB;gBAAEtH,KAAKM;gBAAIrB,QAAQ,IAAI;YAAC;YAC7C,OAAO,IAAIF,QAAQ,KAAO;QAC5B;QAEA,MAAMoO,sBAAsB,CAAC,CAC3BF,CAAAA,oBACAzF,UAAUyF,oBACT,CAAA,CAACvQ,eAAe8K,UACf,CAAC5K,gBAAgBC,cAAc2K,QAAQyF,iBAAgB,CAAC;QAG5D,0DAA0D;QAC1D,qDAAqD;QACrD,MAAMG,oBACJ,CAACvO,QAAQiM,OAAO,IACf,MAAMlM,kBAAkB;YACvBU,QAAQgB;YACRb,QAAQwL,UAAUxL,MAAM;YACxBR,QAAQ,IAAI;QACd;QAEF,IAAI2L,mBAAmBwC,mBAAmB;YACxCrC,oBAAoB;QACtB;QAEA,IAAIA,qBAAqB3L,aAAa,WAAW;;YAC7CP,QAAgBmM,kBAAkB,GAAG;YAEvC,IAAI9M,QAAQC,GAAG,CAACC,mBAAmB,IAAIkC,GAAGJ,UAAU,CAAC,MAAM;gBACzD,MAAMmN,iBAAiBpP,gBACrBb,YAAYH,UAAUsC,WAAW0L,UAAUxL,MAAM,GAAG,OACpDqB,OACA6B,UACAI,OACA,CAACuK,IAAczM,oBAAoByM,GAAGxM,QACtC,IAAI,CAACW,OAAO;gBAGd,IAAI4L,eAAeE,YAAY,EAAE;oBAC/BjG,qBAAqB;wBAAEtH,KAAKM;wBAAIrB,QAAQ,IAAI;oBAAC;oBAC7C,OAAO;gBACT;gBACA,IAAI,CAACmO,mBAAmB;oBACtB5M,aAAa6M,eAAe/N,MAAM;gBACpC;gBAEA,IAAI+N,eAAepK,WAAW,IAAIoK,eAAe9M,YAAY,EAAE;oBAC7D,gEAAgE;oBAChE,4CAA4C;oBAC5CnB,WAAWiO,eAAe9M,YAAY;oBACtCwM,OAAO3N,QAAQ,GAAGhC,YAAYgC;oBAE9B,IAAI,CAACgO,mBAAmB;wBACtBpN,MAAMlD,qBAAqBiQ;oBAC7B;gBACF;YACF,OAAO;gBACLA,OAAO3N,QAAQ,GAAGyB,oBAAoBzB,UAAU0B;gBAEhD,IAAIiM,OAAO3N,QAAQ,KAAKA,UAAU;oBAChCA,WAAW2N,OAAO3N,QAAQ;oBAC1B2N,OAAO3N,QAAQ,GAAGhC,YAAYgC;oBAE9B,IAAI,CAACgO,mBAAmB;wBACtBpN,MAAMlD,qBAAqBiQ;oBAC7B;gBACF;YACF;QACF;QAEA,IAAI,CAACpP,WAAW2C,KAAK;YACnB,IAAIpC,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,cAAc;gBACzC,MAAM,qBAGL,CAHK,IAAInI,MACR,AAAC,oBAAiBsB,MAAI,gBAAaM,KAAG,8CACnC,uFAFC,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;YACAgH,qBAAqB;gBAAEtH,KAAKM;gBAAIrB,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEAuB,aAAatD,aAAaC,eAAeqD,aAAayK,UAAUxL,MAAM;QAEtE+H,QAAQ5L,oBAAoBwD;QAC5B,IAAIoO,aAA6B;QAEjC,IAAI9Q,eAAe8K,QAAQ;YACzB,MAAMtE,WAAWvG,iBAAiB6D;YAClC,MAAMnB,aAAa6D,SAAS9D,QAAQ;YAEpC,MAAMqO,aAAa5Q,cAAc2K;YACjCgG,aAAa5Q,gBAAgB6Q,YAAYpO;YACzC,MAAMqO,oBAAoBlG,UAAUnI;YACpC,MAAMsO,iBAAiBD,oBACnB5P,cAAc0J,OAAOnI,YAAY0D,SAChC,CAAC;YAEN,IAAI,CAACyK,cAAeE,qBAAqB,CAACC,eAAe7K,MAAM,EAAG;gBAChE,MAAM8K,gBAAgBpP,OAAOqP,IAAI,CAACJ,WAAWK,MAAM,EAAEC,MAAM,CACzD,CAACC,QAAU,CAACjL,KAAK,CAACiL,MAAM,IAAI,CAACP,WAAWK,MAAM,CAACE,MAAM,CAACC,QAAQ;gBAGhE,IAAIL,cAAcxN,MAAM,GAAG,KAAK,CAACgN,mBAAmB;oBAClD,IAAIlP,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,cAAc;wBACzCuC,QAAQ8E,IAAI,CACV,AAAC,KACCR,CAAAA,oBACK,uBACA,6BAA+B,IACrC,iCACC,CAAA,AAAC,iBAAcE,cAAclD,IAAI,CAC/B,QACA,0BAA4B;oBAEpC;oBAEA,MAAM,qBAWL,CAXK,IAAIhM,MACR,AAACgP,CAAAA,oBACG,AAAC,0BAAyB1N,MAAI,sCAAmC4N,cAAclD,IAAI,CACjF,QACA,oCACF,AAAC,8BAA6BrL,aAAW,8CAA6CmI,QAAM,KAAG,IACjG,CAAA,AAAC,iDACCkG,CAAAA,oBACI,8BACA,sBAAqB,CAC1B,IAVC,qBAAA;+BAAA;oCAAA;sCAAA;oBAWN;gBACF;YACF,OAAO,IAAIA,mBAAmB;gBAC5BpN,KAAKxD,qBACH0B,OAAOC,MAAM,CAAC,CAAC,GAAGyE,UAAU;oBAC1B9D,UAAUuO,eAAe7K,MAAM;oBAC/BC,OAAOlF,KAAKkF,OAAO4K,eAAelH,MAAM;gBAC1C;YAEJ,OAAO;gBACL,iEAAiE;gBACjEjI,OAAOC,MAAM,CAACsE,OAAOyK;YACvB;QACF;QAEA,IAAI,CAAC5C,iBAAiB;YACpBhD,OAAO2E,MAAM,CAACC,IAAI,CAAC,oBAAoBlM,IAAI+L;QAC7C;QAEA,MAAM8B,eAAe,IAAI,CAAC/O,QAAQ,KAAK,UAAU,IAAI,CAACA,QAAQ,KAAK;QAEnE,IAAI;gBAsKAgJ,qCAAAA,2BACAgG;YAtKF,IAAIA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;gBACtC7G;gBACApI;gBACA2D;gBACAzC;gBACAE;gBACA6L;gBACA5M,QAAQwL,UAAUxL,MAAM;gBACxB6O,WAAWrD,UAAUqD,SAAS;gBAC9BvI,eAAeqH;gBACfhH,0BAA0BvH,QAAQuH,wBAAwB;gBAC1DwE,iBAAiBA,mBAAmB,CAAC,IAAI,CAAC2D,UAAU;gBACpDpB;YACF;YAEA,IAAI,CAACvC,mBAAmB,CAAC/L,QAAQiM,OAAO,EAAE;gBACxC,MAAM,IAAI,CAACpC,IAAI,CACbpI,IACA,gBAAgB8N,YAAYA,UAAU5N,UAAU,GAAGqC,WACnDoI,UAAUxL,MAAM;YAEpB;YAEA,IAAI,WAAW2O,aAAahB,mBAAmB;gBAC7ChO,WAAWgP,UAAU5G,KAAK,IAAIA;gBAC9BA,QAAQpI;gBAER,IAAI,CAACiN,WAAWvB,OAAO,EAAE;oBACvB/H,QAAQvE,OAAOC,MAAM,CAAC,CAAC,GAAG2P,UAAUrL,KAAK,IAAI,CAAC,GAAGA;gBACnD;gBAEA,MAAMyL,wBAAwBnR,YAAY0P,OAAO3N,QAAQ,IACrDjC,eAAe4P,OAAO3N,QAAQ,IAC9B2N,OAAO3N,QAAQ;gBAEnB,IAAIoO,cAAcpO,aAAaoP,uBAAuB;oBACpDhQ,OAAOqP,IAAI,CAACL,YAAYiB,OAAO,CAAC,CAACC;wBAC/B,IAAIlB,cAAczK,KAAK,CAAC2L,IAAI,KAAKlB,UAAU,CAACkB,IAAI,EAAE;4BAChD,OAAO3L,KAAK,CAAC2L,IAAI;wBACnB;oBACF;gBACF;gBAEA,IAAIhS,eAAe0C,WAAW;oBAC5B,MAAMuP,aACJ,CAACtC,WAAWvB,OAAO,IAAIsD,UAAU5N,UAAU,GACvC4N,UAAU5N,UAAU,GACpBpD,YACEH,UACE,IAAIqJ,IAAIhG,IAAIiG,SAASF,IAAI,EAAEjH,QAAQ,EACnC6L,UAAUxL,MAAM,GAElB;oBAGR,IAAImP,YAAYD;oBAEhB,IAAItR,YAAYuR,YAAY;wBAC1BA,YAAYzR,eAAeyR;oBAC7B;oBAEA,IAAI1Q,QAAQC,GAAG,CAACoN,mBAAmB,EAAE;wBACnC,MAAMsD,eAAezS,oBAAoBwS,WAAW,IAAI,CAACnN,OAAO;wBAChEwJ,UAAUxL,MAAM,GAAGoP,aAAapD,cAAc,IAAIR,UAAUxL,MAAM;wBAClEmP,YAAYC,aAAazP,QAAQ;oBACnC;oBACA,MAAMqO,aAAa5Q,cAAcuC;oBACjC,MAAM0P,gBAAgBlS,gBAAgB6Q,YACpC,IAAInH,IAAIsI,WAAWrI,SAASF,IAAI,EAAEjH,QAAQ;oBAG5C,IAAI0P,eAAe;wBACjBtQ,OAAOC,MAAM,CAACsE,OAAO+L;oBACvB;gBACF;YACF;YAEA,yDAAyD;YACzD,IAAI,UAAUV,WAAW;gBACvB,IAAIA,UAAU/K,IAAI,KAAK,qBAAqB;oBAC1C,OAAO,IAAI,CAACmF,MAAM,CAACnD,QAAQ+I,UAAUvK,MAAM,EAAEuK,UAAUxK,KAAK,EAAE/E;gBAChE,OAAO;oBACLyI,qBAAqB;wBAAEtH,KAAKoO,UAAU3K,WAAW;wBAAExE,QAAQ,IAAI;oBAAC;oBAChE,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;YACF;YAEA,MAAMgQ,YAAiBX,UAAUY,SAAS;YAC1C,IAAID,aAAaA,UAAUE,qBAAqB,EAAE;gBAChD,MAAMC,UAAU,EAAE,CAACC,MAAM,CAACJ,UAAUE,qBAAqB;gBAEzDC,QAAQT,OAAO,CAAC,CAACW;oBACfpT,uBAAuBoT,OAAOC,KAAK;gBACrC;YACF;YAEA,uCAAuC;YACvC,IAAI,AAACjB,CAAAA,UAAUkB,OAAO,IAAIlB,UAAUmB,OAAO,AAAD,KAAMnB,UAAUiB,KAAK,EAAE;gBAC/D,IACEjB,UAAUiB,KAAK,CAACG,SAAS,IACzBpB,UAAUiB,KAAK,CAACG,SAAS,CAACC,YAAY,EACtC;oBACA,0DAA0D;oBAC1D5Q,QAAQY,MAAM,GAAG;oBAEjB,MAAMgE,cAAc2K,UAAUiB,KAAK,CAACG,SAAS,CAACC,YAAY;oBAE1D,oEAAoE;oBACpE,gEAAgE;oBAChE,WAAW;oBACX,IACEhM,YAAYvD,UAAU,CAAC,QACvBkO,UAAUiB,KAAK,CAACG,SAAS,CAACE,sBAAsB,KAAK,OACrD;wBACA,MAAMC,aAAahT,iBAAiB8G;wBACpCkM,WAAWvQ,QAAQ,GAAGyB,oBACpB8O,WAAWvQ,QAAQ,EACnB0B;wBAGF,MAAM,EAAEd,KAAK6D,MAAM,EAAEvD,IAAIsD,KAAK,EAAE,GAAGvD,aACjC,IAAI,EACJoD,aACAA;wBAEF,OAAO,IAAI,CAAC+E,MAAM,CAACnD,QAAQxB,QAAQD,OAAO/E;oBAC5C;oBACAyI,qBAAqB;wBAAEtH,KAAKyD;wBAAaxE,QAAQ,IAAI;oBAAC;oBACtD,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEAkM,UAAUqD,SAAS,GAAG,CAAC,CAACF,UAAUiB,KAAK,CAACO,WAAW;gBAEnD,sBAAsB;gBACtB,IAAIxB,UAAUiB,KAAK,CAACzI,QAAQ,KAAK7B,oBAAoB;oBACnD,IAAI8K;oBAEJ,IAAI;wBACF,MAAM,IAAI,CAACC,cAAc,CAAC;wBAC1BD,gBAAgB;oBAClB,EAAE,OAAOE,GAAG;wBACVF,gBAAgB;oBAClB;oBAEAzB,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;wBAClC7G,OAAOqI;wBACPzQ,UAAUyQ;wBACV9M;wBACAzC;wBACAE;wBACA6L,YAAY;4BAAEvB,SAAS;wBAAM;wBAC7BrL,QAAQwL,UAAUxL,MAAM;wBACxB6O,WAAWrD,UAAUqD,SAAS;wBAC9B0B,YAAY;oBACd;oBAEA,IAAI,UAAU5B,WAAW;wBACvB,MAAM,qBAAiD,CAAjD,IAAI1P,MAAO,yCAAX,qBAAA;mCAAA;wCAAA;0CAAA;wBAAgD;oBACxD;gBACF;YACF;YAEA,IACEkM,mBACA,IAAI,CAACxL,QAAQ,KAAK,aAClBgJ,EAAAA,4BAAAA,KAAK6H,aAAa,CAACZ,KAAK,sBAAxBjH,sCAAAA,0BAA0BoH,SAAS,qBAAnCpH,oCAAqC8H,UAAU,MAAK,SACpD9B,mBAAAA,UAAUiB,KAAK,qBAAfjB,iBAAiBoB,SAAS,GAC1B;gBACA,yDAAyD;gBACzD,kCAAkC;gBAClCpB,UAAUiB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;YACzC;gBAI0C9B;YAF1C,6DAA6D;YAC7D,MAAM+B,sBACJtR,QAAQiM,OAAO,IAAIG,UAAUzD,KAAK,KAAM4G,CAAAA,CAAAA,mBAAAA,UAAU5G,KAAK,YAAf4G,mBAAmB5G,KAAI;gBAG/D3I;YADF,MAAMuR,eACJvR,CAAAA,kBAAAA,QAAQuN,MAAM,YAAdvN,kBAAmB,CAAC+L,mBAAmB,CAACuF;YAC1C,MAAME,cAAcD,eAAe;gBAAEjI,GAAG;gBAAGG,GAAG;YAAE,IAAI;YACpD,MAAMgI,sBAAsB3F,uBAAAA,eAAgB0F;YAE5C,0CAA0C;YAC1C,MAAME,sBAAsB;gBAC1B,GAAGtF,SAAS;gBACZzD;gBACApI;gBACA2D;gBACAzD,QAAQC;gBACRgP,YAAY;YACd;YAEA,0EAA0E;YAC1E,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,YAAY;YACZ,IAAI3D,mBAAmBuD,cAAc;oBAmBjC/F,sCAAAA,4BACAgG;gBAnBFA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;oBAClC7G,OAAO,IAAI,CAACpI,QAAQ;oBACpBA,UAAU,IAAI,CAACA,QAAQ;oBACvB2D;oBACAzC;oBACAE;oBACA6L,YAAY;wBAAEvB,SAAS;oBAAM;oBAC7BrL,QAAQwL,UAAUxL,MAAM;oBACxB6O,WAAWrD,UAAUqD,SAAS;oBAC9B1D,iBAAiBA,mBAAmB,CAAC,IAAI,CAAC2D,UAAU;gBACtD;gBAEA,IAAI,UAAUH,WAAW;oBACvB,MAAM,qBAA6D,CAA7D,IAAI1P,MAAM,AAAC,qCAAkC,IAAI,CAACU,QAAQ,GAA1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4D;gBACpE;gBAEA,IACE,IAAI,CAACA,QAAQ,KAAK,aAClBgJ,EAAAA,6BAAAA,KAAK6H,aAAa,CAACZ,KAAK,sBAAxBjH,uCAAAA,2BAA0BoH,SAAS,qBAAnCpH,qCAAqC8H,UAAU,MAAK,SACpD9B,oBAAAA,UAAUiB,KAAK,qBAAfjB,kBAAiBoB,SAAS,GAC1B;oBACA,yDAAyD;oBACzD,kCAAkC;oBAClCpB,UAAUiB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;gBACzC;gBAEA,IAAI;oBACF,MAAM,IAAI,CAACrD,GAAG,CAAC0D,qBAAqBnC,WAAWkC;gBACjD,EAAE,OAAOvJ,KAAK;oBACZ,IAAI9K,QAAQ8K,QAAQA,IAAIpI,SAAS,EAAE;wBACjCiJ,OAAO2E,MAAM,CAACC,IAAI,CAAC,oBAAoBzF,KAAKxH,WAAW8M;oBACzD;oBACA,MAAMtF;gBACR;gBAEA,OAAO;YACT;YAEAa,OAAO2E,MAAM,CAACC,IAAI,CAAC,uBAAuBlM,IAAI+L;YAC9C,IAAI,CAACM,WAAW,CAACtH,QAAQrF,KAAKM,IAAIzB;YAElC,0EAA0E;YAC1E,iBAAiB;YACjB,iDAAiD;YACjD,MAAM2R,kBACJ5F,mBACA,CAAC0F,uBACD,CAACnF,oBACD,CAACsB,gBACD/O,oBAAoB6S,qBAAqB,IAAI,CAACrF,KAAK;YAErD,IAAI,CAACsF,iBAAiB;gBACpB,IAAI;oBACF,MAAM,IAAI,CAAC3D,GAAG,CAAC0D,qBAAqBnC,WAAWkC;gBACjD,EAAE,OAAOG,GAAQ;oBACf,IAAIA,EAAE9R,SAAS,EAAEyP,UAAUzI,KAAK,GAAGyI,UAAUzI,KAAK,IAAI8K;yBACjD,MAAMA;gBACb;gBAEA,IAAIrC,UAAUzI,KAAK,EAAE;oBACnB,IAAI,CAACiF,iBAAiB;wBACpBhD,OAAO2E,MAAM,CAACC,IAAI,CAChB,oBACA4B,UAAUzI,KAAK,EACfpG,WACA8M;oBAEJ;oBAEA,MAAM+B,UAAUzI,KAAK;gBACvB;gBAEA,IAAIzH,QAAQC,GAAG,CAACoN,mBAAmB,EAAE;oBACnC,IAAIN,UAAUxL,MAAM,EAAE;wBACpBiR,SAASC,eAAe,CAACC,IAAI,GAAG3F,UAAUxL,MAAM;oBAClD;gBACF;gBAEA,IAAI,CAACmL,iBAAiB;oBACpBhD,OAAO2E,MAAM,CAACC,IAAI,CAAC,uBAAuBlM,IAAI+L;gBAChD;gBAEA,mDAAmD;gBACnD,MAAMwE,YAAY;gBAClB,IAAIT,gBAAgBS,UAAU/Q,IAAI,CAACQ,KAAK;oBACtC,IAAI,CAACsM,YAAY,CAACtM;gBACpB;YACF;YAEA,OAAO;QACT,EAAE,OAAOyG,KAAK;YACZ,IAAI9K,QAAQ8K,QAAQA,IAAIpI,SAAS,EAAE;gBACjC,OAAO;YACT;YACA,MAAMoI;QACR;IACF;IAEA4F,YACEtH,MAAqB,EACrBrF,GAAW,EACXM,EAAU,EACVzB,OAA+B,EACzB;QADNA,IAAAA,oBAAAA,UAA6B,CAAC;QAE9B,IAAIX,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,cAAc;YACzC,IAAI,OAAOrC,OAAOC,OAAO,KAAK,aAAa;gBACzC2E,QAAQzD,KAAK,CAAE;gBACf;YACF;YAEA,IAAI,OAAOnB,OAAOC,OAAO,CAACY,OAAO,KAAK,aAAa;gBACjD+D,QAAQzD,KAAK,CAAC,AAAC,6BAA0BN,SAAO;gBAChD;YACF;QACF;QAEA,IAAIA,WAAW,eAAe9I,aAAa+D,IAAI;YAC7C,IAAI,CAACwQ,QAAQ,GAAGjS,QAAQiM,OAAO;YAC/BtG,OAAOC,OAAO,CAACY,OAAO,CACpB;gBACErF;gBACAM;gBACAzB;gBACAkS,KAAK;gBACLrC,KAAM,IAAI,CAACzG,IAAI,GAAG5C,WAAW,cAAc,IAAI,CAAC4C,IAAI,GAAGhB;YACzD,GACA,0FAA0F;YAC1F,qFAAqF;YACrF,kEAAkE;YAClE,IACA3G;QAEJ;IACF;IAEA,MAAM0Q,qBACJjK,GAAgD,EAChD3H,QAAgB,EAChB2D,KAAqB,EACrBzC,EAAU,EACV+L,UAA2B,EAC3B4E,aAAuB,EACY;QACnC,IAAIlK,IAAIpI,SAAS,EAAE;YACjB,gCAAgC;YAChC,MAAMoI;QACR;QAEA,IAAIjL,aAAaiL,QAAQkK,eAAe;YACtCrJ,OAAO2E,MAAM,CAACC,IAAI,CAAC,oBAAoBzF,KAAKzG,IAAI+L;YAEhD,iEAAiE;YACjE,0BAA0B;YAC1B,0CAA0C;YAC1C,4CAA4C;YAE5C,+DAA+D;YAC/D/E,qBAAqB;gBACnBtH,KAAKM;gBACLrB,QAAQ,IAAI;YACd;YAEA,kEAAkE;YAClE,8DAA8D;YAC9D,MAAMV;QACR;QAEA6K,QAAQzD,KAAK,CAACoB;QAEd,IAAI;YACF,IAAIsI;YACJ,MAAM,EAAEpO,MAAM+N,SAAS,EAAEkC,WAAW,EAAE,GACpC,MAAM,IAAI,CAACpB,cAAc,CAAC;YAE5B,MAAM1B,YAAsC;gBAC1CiB;gBACAL;gBACAkC;gBACAnK;gBACApB,OAAOoB;YACT;YAEA,IAAI,CAACqH,UAAUiB,KAAK,EAAE;gBACpB,IAAI;oBACFjB,UAAUiB,KAAK,GAAG,MAAM,IAAI,CAAC8B,eAAe,CAACnC,WAAW;wBACtDjI;wBACA3H;wBACA2D;oBACF;gBACF,EAAE,OAAOqO,QAAQ;oBACfhI,QAAQzD,KAAK,CAAC,2CAA2CyL;oBACzDhD,UAAUiB,KAAK,GAAG,CAAC;gBACrB;YACF;YAEA,OAAOjB;QACT,EAAE,OAAOiD,cAAc;YACrB,OAAO,IAAI,CAACL,oBAAoB,CAC9B/U,QAAQoV,gBAAgBA,eAAe,qBAA4B,CAA5B,IAAI3S,MAAM2S,eAAe,KAAzB,qBAAA;uBAAA;4BAAA;8BAAA;YAA2B,IAClEjS,UACA2D,OACAzC,IACA+L,YACA;QAEJ;IACF;IAEA,MAAMgC,aAAa,KA4BlB,EAAE;QA5BgB,IAAA,EACjB7G,OAAO8J,cAAc,EACrBlS,QAAQ,EACR2D,KAAK,EACLzC,EAAE,EACFE,UAAU,EACV6L,UAAU,EACV5M,MAAM,EACNsG,aAAa,EACbuI,SAAS,EACTlI,wBAAwB,EACxBwE,eAAe,EACfuC,mBAAmB,EACnB6C,UAAU,EAeX,GA5BkB;QA6BjB;;;;;KAKC,GACD,IAAIxI,QAAQ8J;QAEZ,IAAI;gBA6EAtN,cACAA,eAKEA,eA0DsBA;YA5I1B,IAAIuN,eAA6C,IAAI,CAACzE,UAAU,CAACtF,MAAM;YACvE,IAAI6E,WAAWvB,OAAO,IAAIyG,gBAAgB,IAAI,CAAC/J,KAAK,KAAKA,OAAO;gBAC9D,OAAO+J;YACT;YAEA,MAAM5J,kBAAkBJ,oBAAoB;gBAAEC;gBAAOvI,QAAQ,IAAI;YAAC;YAElE,IAAI8G,eAAe;gBACjBwL,eAAe1O;YACjB;YAEA,IAAI2O,kBACFD,gBACA,CAAE,CAAA,aAAaA,YAAW,KAC1BrT,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,gBACrB0K,eACA1O;YAEN,MAAMsD,eAAeyE;YACrB,MAAM6G,sBAA2C;gBAC/CvN,UAAU,IAAI,CAAChF,UAAU,CAACwS,WAAW,CAAC;oBACpCrL,MAAMvJ,qBAAqB;wBAAEsC;wBAAU2D;oBAAM;oBAC7C4O,mBAAmB;oBACnBrS,QAAQ0Q,aAAa,SAASxP;oBAC9Bf;gBACF;gBACAsG,eAAe;gBACfC,gBAAgB,IAAI,CAACqF,KAAK;gBAC1BpF,WAAW;gBACXJ,eAAeM,eAAe,IAAI,CAACyL,GAAG,GAAG,IAAI,CAACC,GAAG;gBACjD3L,cAAc,CAACoI;gBACfxI,YAAY;gBACZM;gBACAD;YACF;YAEA,IAAInC,OAKF4G,mBAAmB,CAACuC,sBAChB,OACA,MAAMrJ,sBAAsB;gBAC1BC,WAAW,IAAM6B,cAAc6L;gBAC/BnS,QAAQ0Q,aAAa,SAASxP;gBAC9Bf,QAAQA;gBACRR,QAAQ,IAAI;YACd,GAAG6H,KAAK,CAAC,CAACC;gBACR,4CAA4C;gBAC5C,oDAAoD;gBACpD,oDAAoD;gBACpD,YAAY;gBACZ,IAAI6D,iBAAiB;oBACnB,OAAO;gBACT;gBACA,MAAM7D;YACR;YAEN,wDAAwD;YACxD,UAAU;YACV,IAAI/C,QAAS5E,CAAAA,aAAa,aAAaA,aAAa,MAAK,GAAI;gBAC3D4E,KAAKC,MAAM,GAAGpB;YAChB;YAEA,IAAI+H,iBAAiB;gBACnB,IAAI,CAAC5G,MAAM;oBACTA,OAAO;wBAAEG,MAAMiE,KAAK6H,aAAa,CAACZ,KAAK;oBAAC;gBAC1C,OAAO;oBACLrL,KAAKG,IAAI,GAAGiE,KAAK6H,aAAa,CAACZ,KAAK;gBACtC;YACF;YAEA1H;YAEA,IACE3D,CAAAA,yBAAAA,eAAAA,KAAMC,MAAM,qBAAZD,aAAcX,IAAI,MAAK,uBACvBW,CAAAA,yBAAAA,gBAAAA,KAAMC,MAAM,qBAAZD,cAAcX,IAAI,MAAK,qBACvB;gBACA,OAAOW,KAAKC,MAAM;YACpB;YAEA,IAAID,CAAAA,yBAAAA,gBAAAA,KAAMC,MAAM,qBAAZD,cAAcX,IAAI,MAAK,WAAW;gBACpC,MAAMyO,gBAAgBlW,oBAAoBoI,KAAKC,MAAM,CAAC1D,YAAY;gBAClE,MAAMO,QAAQ,MAAM,IAAI,CAAC5B,UAAU,CAACsD,WAAW;gBAE/C,4DAA4D;gBAC5D,yDAAyD;gBACzD,4DAA4D;gBAC5D,2CAA2C;gBAC3C,IAAI,CAACoI,mBAAmB9J,MAAME,QAAQ,CAAC8Q,gBAAgB;oBACrDtK,QAAQsK;oBACR1S,WAAW4E,KAAKC,MAAM,CAAC1D,YAAY;oBACnCwC,QAAQ;wBAAE,GAAGA,KAAK;wBAAE,GAAGiB,KAAKC,MAAM,CAACf,QAAQ,CAACH,KAAK;oBAAC;oBAClDvC,aAAarD,eACXf,oBAAoB4H,KAAKC,MAAM,CAACf,QAAQ,CAAC9D,QAAQ,EAAE,IAAI,CAACqC,OAAO,EAC5DrC,QAAQ;oBAGb,kDAAkD;oBAClDmS,eAAe,IAAI,CAACzE,UAAU,CAACtF,MAAM;oBACrC,IACE6E,WAAWvB,OAAO,IAClByG,gBACA,IAAI,CAAC/J,KAAK,KAAKA,SACf,CAACzB,eACD;wBACA,4DAA4D;wBAC5D,6DAA6D;wBAC7D,gEAAgE;wBAChE,OAAO;4BAAE,GAAGwL,YAAY;4BAAE/J;wBAAM;oBAClC;gBACF;YACF;YAEA,IAAIjK,WAAWiK,QAAQ;gBACrBF,qBAAqB;oBAAEtH,KAAKM;oBAAIrB,QAAQ,IAAI;gBAAC;gBAC7C,OAAO,IAAIF,QAAe,KAAO;YACnC;YAEA,MAAMqP,YACJoD,mBACC,MAAM,IAAI,CAAC1B,cAAc,CAACtI,OAAO/E,IAAI,CACpC,CAACsP,MAAS,CAAA;oBACR/C,WAAW+C,IAAI9Q,IAAI;oBACnBiQ,aAAaa,IAAIb,WAAW;oBAC5B5B,SAASyC,IAAIC,GAAG,CAAC1C,OAAO;oBACxBC,SAASwC,IAAIC,GAAG,CAACzC,OAAO;gBAC1B,CAAA;YAGJ,IAAIrR,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,cAAc;gBACzC,MAAM,EAAEoL,kBAAkB,EAAE,GAC1B5T,QAAQ;gBACV,IAAI,CAAC4T,mBAAmB7D,UAAUY,SAAS,GAAG;oBAC5C,MAAM,qBAEL,CAFK,IAAItQ,MACR,AAAC,2DAAwDU,WAAS,MAD9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAM8S,oBAAoBlO,yBAAAA,iBAAAA,KAAM3C,QAAQ,qBAAd2C,eAAgBlC,OAAO,CAACC,GAAG,CAAC;YAEtD,MAAMoQ,kBAAkB/D,UAAUkB,OAAO,IAAIlB,UAAUmB,OAAO;YAE9D,yDAAyD;YACzD,4CAA4C;YAC5C,IAAI2C,sBAAqBlO,wBAAAA,KAAME,QAAQ,GAAE;gBACvC,OAAO,IAAI,CAAC2N,GAAG,CAAC7N,KAAKE,QAAQ,CAAC;YAChC;YAEA,MAAM,EAAEmL,KAAK,EAAEhL,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC+N,QAAQ,CAAC;gBAC9C,IAAID,iBAAiB;oBACnB,IAAInO,CAAAA,wBAAAA,KAAMG,IAAI,KAAI,CAAC+N,mBAAmB;wBACpC,OAAO;4BAAE7N,UAAUL,KAAKK,QAAQ;4BAAEgL,OAAOrL,KAAKG,IAAI;wBAAC;oBACrD;oBAEA,MAAMD,WAAWF,CAAAA,wBAAAA,KAAME,QAAQ,IAC3BF,KAAKE,QAAQ,GACb,IAAI,CAAChF,UAAU,CAACwS,WAAW,CAAC;wBAC1BrL,MAAMvJ,qBAAqB;4BAAEsC;4BAAU2D;wBAAM;wBAC7CzD,QAAQkB;wBACRf;oBACF;oBAEJ,MAAM4S,UAAU,MAAMzM,cAAc;wBAClC1B;wBACA8B,gBAAgB,IAAI,CAACqF,KAAK;wBAC1BpF,WAAW;wBACXJ,eAAeqM,oBAAoB,CAAC,IAAI,IAAI,CAACL,GAAG;wBAChD3L,cAAc,CAACoI;wBACfxI,YAAY;wBACZM;oBACF;oBAEA,OAAO;wBACL/B,UAAUgO,QAAQhO,QAAQ;wBAC1BgL,OAAOgD,QAAQlO,IAAI,IAAI,CAAC;oBAC1B;gBACF;gBAEA,OAAO;oBACLrC,SAAS,CAAC;oBACVuN,OAAO,MAAM,IAAI,CAAC8B,eAAe,CAC/B/C,UAAUY,SAAS,EACnB,qDAAqD;oBACrD;wBACE5P;wBACA2D;wBACAzD,QAAQgB;wBACRb;wBACAgC,SAAS,IAAI,CAACA,OAAO;wBACrB8B,eAAe,IAAI,CAACA,aAAa;oBACnC;gBAEJ;YACF;YAEA,mDAAmD;YACnD,6CAA6C;YAC7C,uCAAuC;YACvC,IAAI6K,UAAUmB,OAAO,IAAIkC,oBAAoBvN,QAAQ,IAAIG,UAAU;gBACjE,OAAO,IAAI,CAACwN,GAAG,CAACxN,SAAS;YAC3B;YAEA,+CAA+C;YAC/C,6DAA6D;YAC7D,IACE,CAAC,IAAI,CAACiK,SAAS,IACfF,UAAUkB,OAAO,IACjBpR,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,iBACzB,CAAC+D,iBACD;gBACAhF,cACEpH,OAAOC,MAAM,CAAC,CAAC,GAAGgT,qBAAqB;oBACrCtL,cAAc;oBACdD,cAAc;oBACdL,eAAe,IAAI,CAAC+L,GAAG;gBACzB,IACA9K,KAAK,CAAC,KAAO;YACjB;YAEAuI,MAAMG,SAAS,GAAGhR,OAAOC,MAAM,CAAC,CAAC,GAAG4Q,MAAMG,SAAS;YACnDpB,UAAUiB,KAAK,GAAGA;YAClBjB,UAAU5G,KAAK,GAAGA;YAClB4G,UAAUrL,KAAK,GAAGA;YAClBqL,UAAU5N,UAAU,GAAGA;YACvB,IAAI,CAACsM,UAAU,CAACtF,MAAM,GAAG4G;YAEzB,OAAOA;QACT,EAAE,OAAOrH,KAAK;YACZ,OAAO,IAAI,CAACiK,oBAAoB,CAC9B9U,eAAe6K,MACf3H,UACA2D,OACAzC,IACA+L;QAEJ;IACF;IAEQQ,IACN3B,KAAwB,EACxBlH,IAAsB,EACtBqM,WAA4C,EAC7B;QACf,IAAI,CAACnF,KAAK,GAAGA;QAEb,OAAO,IAAI,CAACoH,GAAG,CACbtO,MACA,IAAI,CAAC8I,UAAU,CAAC,QAAQ,CAACkC,SAAS,EAClCqB;IAEJ;IAEA;;;GAGC,GACDkC,eAAeC,EAA0B,EAAE;QACzC,IAAI,CAACC,IAAI,GAAGD;IACd;IAEA9F,gBAAgBpM,EAAU,EAAW;QACnC,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE,OAAO;QACzB,MAAM,CAACoT,cAAcC,QAAQ,GAAG,IAAI,CAACrT,MAAM,CAACiL,KAAK,CAAC,KAAK;QACvD,MAAM,CAACqI,cAAcC,QAAQ,GAAGvS,GAAGiK,KAAK,CAAC,KAAK;QAE9C,yEAAyE;QACzE,IAAIsI,WAAWH,iBAAiBE,gBAAgBD,YAAYE,SAAS;YACnE,OAAO;QACT;QAEA,0DAA0D;QAC1D,IAAIH,iBAAiBE,cAAc;YACjC,OAAO;QACT;QAEA,yDAAyD;QACzD,uDAAuD;QACvD,2DAA2D;QAC3D,mCAAmC;QACnC,OAAOD,YAAYE;IACrB;IAEAjG,aAAatM,EAAU,EAAQ;QAC7B,MAAM,GAAGoD,OAAO,EAAE,CAAC,GAAGpD,GAAGiK,KAAK,CAAC,KAAK;QAEpCxM,yCACE;YACE,gEAAgE;YAChE,qBAAqB;YACrB,IAAI2F,SAAS,MAAMA,SAAS,OAAO;gBACjCc,OAAOsO,QAAQ,CAAC,GAAG;gBACnB;YACF;YAEA,8CAA8C;YAC9C,MAAMC,UAAUC,mBAAmBtP;YACnC,+CAA+C;YAC/C,MAAMuP,OAAOvC,SAASwC,cAAc,CAACH;YACrC,IAAIE,MAAM;gBACRA,KAAKE,cAAc;gBACnB;YACF;YACA,kEAAkE;YAClE,qBAAqB;YACrB,MAAMC,SAAS1C,SAAS2C,iBAAiB,CAACN,QAAQ,CAAC,EAAE;YACrD,IAAIK,QAAQ;gBACVA,OAAOD,cAAc;YACvB;QACF,GACA;YACEG,gBAAgB,IAAI,CAAC5G,eAAe,CAACpM;QACvC;IAEJ;IAEA0M,SAAS1N,MAAc,EAAW;QAChC,OAAO,IAAI,CAACA,MAAM,KAAKA;IACzB;IAEA;;;;;GAKC,GACD,MAAMiU,SACJvT,GAAW,EACXV,MAAoB,EACpBT,OAA6B,EACd;QAFfS,IAAAA,mBAAAA,SAAiBU;QACjBnB,IAAAA,oBAAAA,UAA2B,CAAC;QAE5B,2FAA2F;QAC3F,IAAIX,QAAQC,GAAG,CAAC0I,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,OAAOrC,WAAW,eAAe5G,MAAM4G,OAAOgP,SAAS,CAACC,SAAS,GAAG;YACtE,kFAAkF;YAClF,8EAA8E;YAC9E,cAAc;YACd;QACF;QACA,IAAI1G,SAASpQ,iBAAiBqD;QAC9B,MAAM0T,cAAc3G,OAAO3N,QAAQ;QAEnC,IAAI,EAAEA,QAAQ,EAAE2D,KAAK,EAAE,GAAGgK;QAC1B,MAAM4G,mBAAmBvU;QAEzB,IAAIlB,QAAQC,GAAG,CAACoN,mBAAmB,EAAE;YACnC,IAAI1M,QAAQY,MAAM,KAAK,OAAO;gBAC5BL,WAAWhD,oBAAqBgD,UAAU,IAAI,CAACqC,OAAO,EAAErC,QAAQ;gBAChE2N,OAAO3N,QAAQ,GAAGA;gBAClBY,MAAMlD,qBAAqBiQ;gBAE3B,IAAI7J,WAAWvG,iBAAiB2C;gBAChC,MAAMkM,mBAAmBpP,oBACvB8G,SAAS9D,QAAQ,EACjB,IAAI,CAACqC,OAAO;gBAEdyB,SAAS9D,QAAQ,GAAGoM,iBAAiBpM,QAAQ;gBAC7CP,QAAQY,MAAM,GAAG+L,iBAAiBC,cAAc,IAAI,IAAI,CAAClI,aAAa;gBACtEjE,SAASxC,qBAAqBoG;YAChC;QACF;QAEA,MAAMpC,QAAQ,MAAM,IAAI,CAAC5B,UAAU,CAACsD,WAAW;QAC/C,IAAIhC,aAAalB;QAEjB,MAAMG,SACJ,OAAOZ,QAAQY,MAAM,KAAK,cACtBZ,QAAQY,MAAM,IAAIoD,YAClB,IAAI,CAACpD,MAAM;QAEjB,MAAM2N,oBAAoB,MAAMxO,kBAAkB;YAChDU,QAAQA;YACRG,QAAQA;YACRR,QAAQ,IAAI;QACd;QAEA,IAAIf,QAAQC,GAAG,CAACC,mBAAmB,IAAIkB,OAAOY,UAAU,CAAC,MAAM;YAC7D,IAAIyC;YACF,CAAA,EAAED,YAAYC,QAAQ,EAAE,GAAG,MAAM9G,wBAAuB;YAE1D,MAAMwR,iBAAiBpP,gBACrBb,YAAYH,UAAUqC,QAAQ,IAAI,CAACG,MAAM,GAAG,OAC5CqB,OACA6B,UACAoK,OAAOhK,KAAK,EACZ,CAACuK,IAAczM,oBAAoByM,GAAGxM,QACtC,IAAI,CAACW,OAAO;YAGd,IAAI4L,eAAeE,YAAY,EAAE;gBAC/B;YACF;YAEA,IAAI,CAACH,mBAAmB;gBACtB5M,aAAatD,aACXC,eAAekQ,eAAe/N,MAAM,GACpC,IAAI,CAACG,MAAM;YAEf;YAEA,IAAI4N,eAAepK,WAAW,IAAIoK,eAAe9M,YAAY,EAAE;gBAC7D,gEAAgE;gBAChE,4CAA4C;gBAC5CnB,WAAWiO,eAAe9M,YAAY;gBACtCwM,OAAO3N,QAAQ,GAAGA;gBAElB,IAAI,CAACgO,mBAAmB;oBACtBpN,MAAMlD,qBAAqBiQ;gBAC7B;YACF;QACF;QACAA,OAAO3N,QAAQ,GAAGyB,oBAAoBkM,OAAO3N,QAAQ,EAAE0B;QAEvD,IAAIpE,eAAeqQ,OAAO3N,QAAQ,GAAG;YACnCA,WAAW2N,OAAO3N,QAAQ;YAC1B2N,OAAO3N,QAAQ,GAAGA;YAClBZ,OAAOC,MAAM,CACXsE,OACAnG,gBAAgBC,cAAckQ,OAAO3N,QAAQ,GAC3CpC,UAAUsC,QAAQF,QAAQ,KACvB,CAAC;YAGR,IAAI,CAACgO,mBAAmB;gBACtBpN,MAAMlD,qBAAqBiQ;YAC7B;QACF;QAEA,MAAM/I,OACJ9F,QAAQC,GAAG,CAACyV,0BAA0B,KAAK,WACvC,OACA,MAAM9P,sBAAsB;YAC1BC,WAAW,IACT6B,cAAc;oBACZ1B,UAAU,IAAI,CAAChF,UAAU,CAACwS,WAAW,CAAC;wBACpCrL,MAAMvJ,qBAAqB;4BACzBsC,UAAUuU;4BACV5Q;wBACF;wBACA4O,mBAAmB;wBACnBrS,QAAQkB;wBACRf;oBACF;oBACAsG,eAAe;oBACfC,gBAAgB;oBAChBC,WAAW;oBACXJ,eAAe,IAAI,CAACgM,GAAG;oBACvB3L,cAAc,CAAC,IAAI,CAACoI,SAAS;oBAC7BxI,YAAY;gBACd;YACFxG,QAAQA;YACRG,QAAQA;YACRR,QAAQ,IAAI;QACd;QAEN;;;KAGC,GACD,IAAI+E,CAAAA,wBAAAA,KAAMC,MAAM,CAACZ,IAAI,MAAK,WAAW;YACnC0J,OAAO3N,QAAQ,GAAG4E,KAAKC,MAAM,CAAC1D,YAAY;YAC1CnB,WAAW4E,KAAKC,MAAM,CAAC1D,YAAY;YACnCwC,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGiB,KAAKC,MAAM,CAACf,QAAQ,CAACH,KAAK;YAAC;YAClDvC,aAAawD,KAAKC,MAAM,CAACf,QAAQ,CAAC9D,QAAQ;YAC1CY,MAAMlD,qBAAqBiQ;QAC7B;QAEA;;;KAGC,GACD,IAAI/I,CAAAA,wBAAAA,KAAMC,MAAM,CAACZ,IAAI,MAAK,qBAAqB;YAC7C;QACF;QAEA,MAAMmE,QAAQ5L,oBAAoBwD;QAElC,IAAI,MAAM,IAAI,CAACsJ,IAAI,CAACpJ,QAAQkB,YAAY3B,QAAQY,MAAM,EAAE,OAAO;YAC7D,IAAI,CAACqN,UAAU,CAAC4G,YAAY,GAAG;gBAAExG,aAAa;YAAK;QACrD;QAEA,MAAMnO,QAAQwD,GAAG,CAAC;YAChB,IAAI,CAACrD,UAAU,CAAC2U,MAAM,CAACrM,OAAO/E,IAAI,CAAC,CAACqR;gBAClC,OAAOA,QACHlO,cAAc;oBACZ1B,UAAUF,CAAAA,wBAAAA,KAAMG,IAAI,IAChBH,wBAAAA,KAAME,QAAQ,GACd,IAAI,CAAChF,UAAU,CAACwS,WAAW,CAAC;wBAC1BrL,MAAMrG;wBACNV,QAAQkB;wBACRf,QAAQA;oBACV;oBACJuG,gBAAgB;oBAChBC,WAAW;oBACXJ,eAAe,IAAI,CAACgM,GAAG;oBACvB3L,cAAc,CAAC,IAAI,CAACoI,SAAS;oBAC7BxI,YAAY;oBACZM,0BACEvH,QAAQuH,wBAAwB,IAC/BvH,QAAQkV,QAAQ,IACf,CAAC,CAAC7V,QAAQC,GAAG,CAAC6V,8BAA8B;gBAClD,GACGvR,IAAI,CAAC,IAAM,OACXqE,KAAK,CAAC,IAAM,SACf;YACN;YACA,IAAI,CAAC5H,UAAU,CAACL,QAAQkV,QAAQ,GAAG,aAAa,WAAW,CAACvM;SAC7D;IACH;IAEA,MAAMsI,eAAetI,KAAa,EAAE;QAClC,MAAMG,kBAAkBJ,oBAAoB;YAAEC;YAAOvI,QAAQ,IAAI;QAAC;QAElE,IAAI;YACF,MAAMgV,kBAAkB,MAAM,IAAI,CAAC/U,UAAU,CAACgV,QAAQ,CAAC1M;YACvDG;YAEA,OAAOsM;QACT,EAAE,OAAOlN,KAAK;YACZY;YACA,MAAMZ;QACR;IACF;IAEAqL,SAAY+B,EAAoB,EAAc;QAC5C,IAAIxV,YAAY;QAChB,MAAM8I,SAAS;YACb9I,YAAY;QACd;QACA,IAAI,CAAC+I,GAAG,GAAGD;QACX,OAAO0M,KAAK1R,IAAI,CAAC,CAACuB;YAChB,IAAIyD,WAAW,IAAI,CAACC,GAAG,EAAE;gBACvB,IAAI,CAACA,GAAG,GAAG;YACb;YAEA,IAAI/I,WAAW;gBACb,MAAMoI,MAAW,qBAA4C,CAA5C,IAAIrI,MAAM,oCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA2C;gBAC5DqI,IAAIpI,SAAS,GAAG;gBAChB,MAAMoI;YACR;YAEA,OAAO/C;QACT;IACF;IAEAmN,gBACEnC,SAAwB,EACxBoF,GAAoB,EACU;QAC9B,MAAM,EAAEpF,WAAWqF,GAAG,EAAE,GAAG,IAAI,CAACvH,UAAU,CAAC,QAAQ;QACnD,MAAMwH,UAAU,IAAI,CAACC,QAAQ,CAACF;QAC9BD,IAAIE,OAAO,GAAGA;QACd,OAAO9X,oBAA4C6X,KAAK;YACtDC;YACAtF;YACA/P,QAAQ,IAAI;YACZmV;QACF;IACF;IAEA,IAAI5M,QAAgB;QAClB,OAAO,IAAI,CAAC0D,KAAK,CAAC1D,KAAK;IACzB;IAEA,IAAIpI,WAAmB;QACrB,OAAO,IAAI,CAAC8L,KAAK,CAAC9L,QAAQ;IAC5B;IAEA,IAAI2D,QAAwB;QAC1B,OAAO,IAAI,CAACmI,KAAK,CAACnI,KAAK;IACzB;IAEA,IAAIzD,SAAiB;QACnB,OAAO,IAAI,CAAC4L,KAAK,CAAC5L,MAAM;IAC1B;IAEA,IAAIG,SAA6B;QAC/B,OAAO,IAAI,CAACyL,KAAK,CAACzL,MAAM;IAC1B;IAEA,IAAI8O,aAAsB;QACxB,OAAO,IAAI,CAACrD,KAAK,CAACqD,UAAU;IAC9B;IAEA,IAAID,YAAqB;QACvB,OAAO,IAAI,CAACpD,KAAK,CAACoD,SAAS;IAC7B;IAj1DAkG,YACEpV,QAAgB,EAChB2D,KAAqB,EACrBzC,EAAU,EACV,EACEmU,YAAY,EACZvV,UAAU,EACVmV,GAAG,EACHK,OAAO,EACP1F,SAAS,EACTjI,GAAG,EACH4N,YAAY,EACZpG,UAAU,EACV9O,MAAM,EACNgC,OAAO,EACP8B,aAAa,EACbqI,aAAa,EACb0C,SAAS,EAeV,CACD;QAzEF,yCAAyC;aACzCuD,MAAqB,CAAC;QACtB,0CAA0C;aAC1CD,MAAqB,CAAC;aAgBtBgD,uBAAuB;aAiBf3M,OAAehB;aA+JvB4N,aAAa,CAACpE;YACZ,MAAM,EAAEmE,oBAAoB,EAAE,GAAG,IAAI;YACrC,IAAI,CAACA,oBAAoB,GAAG;YAE5B,MAAM1J,QAAQuF,EAAEvF,KAAK;YAErB,IAAI,CAACA,OAAO;gBACV,6CAA6C;gBAC7C,sDAAsD;gBACtD,kCAAkC;gBAClC,EAAE;gBACF,oEAAoE;gBACpE,4BAA4B;gBAC5B,4DAA4D;gBAC5D,kFAAkF;gBAClF,gDAAgD;gBAChD,MAAM,EAAE9L,QAAQ,EAAE2D,KAAK,EAAE,GAAG,IAAI;gBAChC,IAAI,CAAC4J,WAAW,CACd,gBACA7P,qBAAqB;oBAAEsC,UAAUhC,YAAYgC;oBAAW2D;gBAAM,IAC9DxG;gBAEF;YACF;YAEA,kFAAkF;YAClF,IAAI2O,MAAM4J,IAAI,EAAE;gBACdtQ,OAAO+B,QAAQ,CAACsB,MAAM;gBACtB;YACF;YAEA,IAAI,CAACqD,MAAM6F,GAAG,EAAE;gBACd;YACF;YAEA,yDAAyD;YACzD,IACE6D,wBACA,IAAI,CAACnV,MAAM,KAAKyL,MAAMrM,OAAO,CAACY,MAAM,IACpCyL,MAAM5K,EAAE,KAAK,IAAI,CAAChB,MAAM,EACxB;gBACA;YACF;YAEA,IAAIqL;YACJ,MAAM,EAAE3K,GAAG,EAAEM,EAAE,EAAEzB,OAAO,EAAE6P,GAAG,EAAE,GAAGxD;YAClC,IAAIhN,QAAQC,GAAG,CAACoG,yBAAyB,EAAE;gBACzC,IAAID,yBAAyB;oBAC3B,IAAI,IAAI,CAAC2D,IAAI,KAAKyG,KAAK;wBACrB,oCAAoC;wBACpC,IAAI;4BACF/J,eAAeC,OAAO,CACpB,mBAAmB,IAAI,CAACqD,IAAI,EAC5BxC,KAAKyC,SAAS,CAAC;gCAAEC,GAAGC,KAAKC,WAAW;gCAAEC,GAAGF,KAAKG,WAAW;4BAAC;wBAE9D,EAAE,UAAM,CAAC;wBAET,+BAA+B;wBAC/B,IAAI;4BACF,MAAM7D,IAAIC,eAAeoQ,OAAO,CAAC,mBAAmBrG;4BACpD/D,eAAelF,KAAKC,KAAK,CAAChB;wBAC5B,EAAE,UAAM;4BACNiG,eAAe;gCAAExC,GAAG;gCAAGG,GAAG;4BAAE;wBAC9B;oBACF;gBACF;YACF;YACA,IAAI,CAACL,IAAI,GAAGyG;YAEZ,MAAM,EAAEtP,QAAQ,EAAE,GAAGzC,iBAAiBqD;YAEtC,gDAAgD;YAChD,yDAAyD;YACzD,IACE,IAAI,CAACqL,KAAK,IACV/K,OAAOlD,YAAY,IAAI,CAACkC,MAAM,KAC9BF,aAAahC,YAAY,IAAI,CAACgC,QAAQ,GACtC;gBACA;YACF;YAEA,uDAAuD;YACvD,wDAAwD;YACxD,IAAI,IAAI,CAACqT,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACvH,QAAQ;gBAClC;YACF;YAEA,IAAI,CAAC1C,MAAM,CACT,gBACAxI,KACAM,IACA9B,OAAOC,MAAM,CAA2C,CAAC,GAAGI,SAAS;gBACnEiM,SAASjM,QAAQiM,OAAO,IAAI,IAAI,CAACgG,QAAQ;gBACzCrR,QAAQZ,QAAQY,MAAM,IAAI,IAAI,CAAC8D,aAAa;gBAC5C,iDAAiD;gBACjDsH,IAAI;YACN,IACAF;QAEJ;QA5NE,uCAAuC;QACvC,MAAMnD,QAAQ5L,oBAAoBwD;QAElC,6CAA6C;QAC7C,IAAI,CAAC0N,UAAU,GAAG,CAAC;QACnB,oDAAoD;QACpD,wDAAwD;QACxD,kCAAkC;QAClC,IAAI1N,aAAa,WAAW;YAC1B,IAAI,CAAC0N,UAAU,CAACtF,MAAM,GAAG;gBACvBwH;gBACAgG,SAAS;gBACT3F,OAAOoF;gBACP1N;gBACAuI,SAASmF,gBAAgBA,aAAanF,OAAO;gBAC7CC,SAASkF,gBAAgBA,aAAalF,OAAO;YAC/C;QACF;QAEA,IAAI,CAACzC,UAAU,CAAC,QAAQ,GAAG;YACzBkC,WAAWqF;YACXnD,aAAa,EAEZ;QACH;QAEA,4CAA4C;QAC5C,gFAAgF;QAChF,IAAI,CAAC3E,MAAM,GAAG3E,OAAO2E,MAAM;QAE3B,IAAI,CAACrN,UAAU,GAAGA;QAClB,8DAA8D;QAC9D,kDAAkD;QAClD,MAAM+V,oBACJvY,eAAe0C,aAAagJ,KAAK6H,aAAa,CAACiF,UAAU;QAE3D,IAAI,CAAC3T,QAAQ,GAAGrD,QAAQC,GAAG,CAACgX,sBAAsB,IAAI;QACtD,IAAI,CAAC7C,GAAG,GAAGqC;QACX,IAAI,CAACjN,GAAG,GAAG;QACX,IAAI,CAAC6M,QAAQ,GAAGG;QAChB,6DAA6D;QAC7D,0BAA0B;QAC1B,IAAI,CAACrJ,KAAK,GAAG;QACb,IAAI,CAACQ,cAAc,GAAG;QACtB,IAAI,CAACT,OAAO,GAAG,CAAC,CACdhD,CAAAA,KAAK6H,aAAa,CAACmF,IAAI,IACvBhN,KAAK6H,aAAa,CAACoF,GAAG,IACtBjN,KAAK6H,aAAa,CAACqF,qBAAqB,IACvClN,KAAK6H,aAAa,CAACsF,MAAM,IAAI,CAACnN,KAAK6H,aAAa,CAACuF,GAAG,IACpD,CAACP,qBACA,CAAC7M,KAAK7B,QAAQ,CAACkP,MAAM,IACrB,CAACvX,QAAQC,GAAG,CAACC,mBAAmB;QAGpC,IAAIF,QAAQC,GAAG,CAACoN,mBAAmB,EAAE;YACnC,IAAI,CAAC9J,OAAO,GAAGA;YACf,IAAI,CAAC8B,aAAa,GAAGA;YACrB,IAAI,CAACqI,aAAa,GAAGA;YACrB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC9O,mBACtB6O,eACAxD,KAAK7B,QAAQ,CAACuF,QAAQ;QAE1B;QAEA,IAAI,CAACZ,KAAK,GAAG;YACX1D;YACApI;YACA2D;YACAzD,QAAQ2V,oBAAoB7V,WAAWkB;YACvCgO,WAAW,CAAC,CAACA;YACb7O,QAAQvB,QAAQC,GAAG,CAACoN,mBAAmB,GAAG9L,SAASoD;YACnD0L;QACF;QAEA,IAAI,CAACmH,gCAAgC,GAAG3W,QAAQC,OAAO,CAAC;QAExD,IAAI,OAAOwF,WAAW,aAAa;YACjC,kEAAkE;YAClE,4CAA4C;YAC5C,IAAI,CAAClE,GAAGJ,UAAU,CAAC,OAAO;gBACxB,2DAA2D;gBAC3D,4DAA4D;gBAC5D,MAAMrB,UAA6B;oBAAEY;gBAAO;gBAC5C,MAAMH,SAAS/C;gBAEf,IAAI,CAACmZ,gCAAgC,GAAG9W,kBAAkB;oBACxDK,QAAQ,IAAI;oBACZQ;oBACAH;gBACF,GAAGmD,IAAI,CAAC,CAACW;oBACP,kEAAkE;oBAClE,sDAAsD;;oBACpDvE,QAAgBmM,kBAAkB,GAAG1K,OAAOlB;oBAE9C,IAAI,CAACuN,WAAW,CACd,gBACAvJ,UACI9D,SACAxC,qBAAqB;wBACnBsC,UAAUhC,YAAYgC;wBACtB2D;oBACF,IACJzD,QACAT;oBAEF,OAAOuE;gBACT;YACF;YAEAoB,OAAOmR,gBAAgB,CAAC,YAAY,IAAI,CAACd,UAAU;YAEnD,2DAA2D;YAC3D,mDAAmD;YACnD,IAAI3W,QAAQC,GAAG,CAACoG,yBAAyB,EAAE;gBACzC,IAAID,yBAAyB;oBAC3BE,OAAOC,OAAO,CAACmR,iBAAiB,GAAG;gBACrC;YACF;QACF;IACF;AAyrDF;AAj4DqBhO,OA6CZ2E,SAAmClQ;AA7C5C,SAAqBuL,oBAi4DpB", "ignoreList": [0]}