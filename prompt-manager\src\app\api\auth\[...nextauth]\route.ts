/**
 * NextAuth.js API 路由处理器
 * 暂时注释掉，等CSS问题解决后恢复
 */

// import NextAuth from 'next-auth';
// import { authOptions } from '~/lib/auth';

// const handler = NextAuth(authOptions);

// export { handler as GET, handler as POST };

// 暂时返回简单响应
export async function GET() {
  return new Response('NextAuth temporarily disabled', { status: 200 });
}

export async function POST() {
  return new Response('NextAuth temporarily disabled', { status: 200 });
}
