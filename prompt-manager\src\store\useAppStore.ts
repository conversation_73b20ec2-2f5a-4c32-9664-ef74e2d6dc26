/**
 * 全局应用状态管理
 * 使用Zustand管理应用的全局状态
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 类型定义
interface Tag {
  id: string;
  name: string;
  color: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: Category;
  tags: Tag[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface SearchFilters {
  query: string;
  categoryId: string | null;
  tags: string[];
  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title';
  sortOrder: 'asc' | 'desc';
}

interface AppState {
  // 用户状态
  user: User | null;
  isAuthenticated: boolean;
  
  // 数据状态
  prompts: Prompt[];
  categories: Category[];
  tags: Tag[];
  
  // UI状态
  searchFilters: SearchFilters;
  sidebarOpen: boolean;
  selectedCategoryId: string | null;
  
  // 缓存状态
  lastFetchTime: number;
  isLoading: boolean;
  error: string | null;
}

interface AppActions {
  // 用户操作
  setUser: (user: User | null) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  
  // 数据操作
  setPrompts: (prompts: Prompt[]) => void;
  addPrompt: (prompt: Prompt) => void;
  updatePrompt: (id: string, updates: Partial<Prompt>) => void;
  deletePrompt: (id: string) => void;
  
  setCategories: (categories: Category[]) => void;
  addCategory: (category: Category) => void;
  updateCategory: (id: string, updates: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  
  setTags: (tags: Tag[]) => void;
  addTag: (tag: Tag) => void;
  updateTag: (id: string, updates: Partial<Tag>) => void;
  deleteTag: (id: string) => void;
  
  // UI操作
  setSearchFilters: (filters: Partial<SearchFilters>) => void;
  resetSearchFilters: () => void;
  setSidebarOpen: (open: boolean) => void;
  setSelectedCategoryId: (categoryId: string | null) => void;
  
  // 缓存操作
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateLastFetchTime: () => void;
  
  // 重置操作
  reset: () => void;
}

type AppStore = AppState & AppActions;

// 初始状态
const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  prompts: [],
  categories: [],
  tags: [],
  searchFilters: {
    query: '',
    categoryId: null,
    tags: [],
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  },
  sidebarOpen: true,
  selectedCategoryId: null,
  lastFetchTime: 0,
  isLoading: false,
  error: null,
};

// 创建store
export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // 用户操作
      setUser: (user) => set({ user }),
      setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
      
      // 数据操作
      setPrompts: (prompts) => set({ prompts }),
      addPrompt: (prompt) => set((state) => ({ 
        prompts: [prompt, ...state.prompts] 
      })),
      updatePrompt: (id, updates) => set((state) => ({
        prompts: state.prompts.map(prompt => 
          prompt.id === id ? { ...prompt, ...updates } : prompt
        )
      })),
      deletePrompt: (id) => set((state) => ({
        prompts: state.prompts.filter(prompt => prompt.id !== id)
      })),
      
      setCategories: (categories) => set({ categories }),
      addCategory: (category) => set((state) => ({ 
        categories: [...state.categories, category] 
      })),
      updateCategory: (id, updates) => set((state) => ({
        categories: state.categories.map(category => 
          category.id === id ? { ...category, ...updates } : category
        )
      })),
      deleteCategory: (id) => set((state) => ({
        categories: state.categories.filter(category => category.id !== id)
      })),
      
      setTags: (tags) => set({ tags }),
      addTag: (tag) => set((state) => ({ 
        tags: [...state.tags, tag] 
      })),
      updateTag: (id, updates) => set((state) => ({
        tags: state.tags.map(tag => 
          tag.id === id ? { ...tag, ...updates } : tag
        )
      })),
      deleteTag: (id) => set((state) => ({
        tags: state.tags.filter(tag => tag.id !== id)
      })),
      
      // UI操作
      setSearchFilters: (filters) => set((state) => ({
        searchFilters: { ...state.searchFilters, ...filters }
      })),
      resetSearchFilters: () => set({
        searchFilters: initialState.searchFilters
      }),
      setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }),
      setSelectedCategoryId: (selectedCategoryId) => set({ selectedCategoryId }),
      
      // 缓存操作
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      updateLastFetchTime: () => set({ lastFetchTime: Date.now() }),
      
      // 重置操作
      reset: () => set(initialState),
    }),
    {
      name: 'prompt-manager-storage',
      storage: createJSONStorage(() => localStorage),
      // 只持久化部分状态
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        searchFilters: state.searchFilters,
        sidebarOpen: state.sidebarOpen,
        selectedCategoryId: state.selectedCategoryId,
      }),
    }
  )
);

// 选择器hooks
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const usePrompts = () => useAppStore((state) => state.prompts);
export const useCategories = () => useAppStore((state) => state.categories);
export const useTags = () => useAppStore((state) => state.tags);
export const useSearchFilters = () => useAppStore((state) => state.searchFilters);
export const useSidebarOpen = () => useAppStore((state) => state.sidebarOpen);
export const useSelectedCategoryId = () => useAppStore((state) => state.selectedCategoryId);
export const useIsLoading = () => useAppStore((state) => state.isLoading);
export const useError = () => useAppStore((state) => state.error);

// 操作hooks
export const useAppActions = () => useAppStore((state) => ({
  setUser: state.setUser,
  setAuthenticated: state.setAuthenticated,
  setPrompts: state.setPrompts,
  addPrompt: state.addPrompt,
  updatePrompt: state.updatePrompt,
  deletePrompt: state.deletePrompt,
  setCategories: state.setCategories,
  addCategory: state.addCategory,
  updateCategory: state.updateCategory,
  deleteCategory: state.deleteCategory,
  setTags: state.setTags,
  addTag: state.addTag,
  updateTag: state.updateTag,
  deleteTag: state.deleteTag,
  setSearchFilters: state.setSearchFilters,
  resetSearchFilters: state.resetSearchFilters,
  setSidebarOpen: state.setSidebarOpen,
  setSelectedCategoryId: state.setSelectedCategoryId,
  setLoading: state.setLoading,
  setError: state.setError,
  updateLastFetchTime: state.updateLastFetchTime,
  reset: state.reset,
}));
