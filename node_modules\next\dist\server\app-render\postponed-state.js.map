{"version": 3, "sources": ["../../../src/server/app-render/postponed-state.ts"], "sourcesContent": ["import type { FallbackRouteParams } from '../../server/request/fallback-params'\nimport type { Params } from '../request/params'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n  type PrerenderResumeDataCache,\n  type RenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport { stringifyResumeDataCache } from '../resume-data-cache/resume-data-cache'\n\nexport enum DynamicState {\n  /**\n   * The dynamic access occurred during the RSC render phase.\n   */\n  DATA = 1,\n\n  /**\n   * The dynamic access occurred during the HTML shell render phase.\n   */\n  HTML = 2,\n}\n\n/**\n * The postponed state for dynamic data.\n */\nexport type DynamicDataPostponedState = {\n  /**\n   * The type of dynamic state.\n   */\n  readonly type: DynamicState.DATA\n\n  /**\n   * The immutable resume data cache.\n   */\n  readonly renderResumeDataCache: RenderResumeDataCache\n}\n\n/**\n * The postponed state for dynamic HTML.\n */\nexport type DynamicHTMLPostponedState = {\n  /**\n   * The type of dynamic state.\n   */\n  readonly type: DynamicState.HTML\n\n  /**\n   * The postponed data used by React.\n   */\n  readonly data: object\n\n  /**\n   * The immutable resume data cache.\n   */\n  readonly renderResumeDataCache: RenderResumeDataCache\n}\n\nexport type PostponedState =\n  | DynamicDataPostponedState\n  | DynamicHTMLPostponedState\n\nexport async function getDynamicHTMLPostponedState(\n  data: object,\n  fallbackRouteParams: FallbackRouteParams | null,\n  resumeDataCache: PrerenderResumeDataCache | RenderResumeDataCache\n): Promise<string> {\n  if (!fallbackRouteParams || fallbackRouteParams.size === 0) {\n    const postponedString = JSON.stringify(data)\n\n    // Serialized as `<postponedString.length>:<postponedString><renderResumeDataCache>`\n    return `${postponedString.length}:${postponedString}${await stringifyResumeDataCache(\n      createRenderResumeDataCache(resumeDataCache)\n    )}`\n  }\n\n  const replacements: Array<[string, string]> = Array.from(fallbackRouteParams)\n  const replacementsString = JSON.stringify(replacements)\n  const dataString = JSON.stringify(data)\n\n  // Serialized as `<replacements.length><replacements><data>`\n  const postponedString = `${replacementsString.length}${replacementsString}${dataString}`\n\n  // Serialized as `<postponedString.length>:<postponedString><renderResumeDataCache>`\n  return `${postponedString.length}:${postponedString}${await stringifyResumeDataCache(resumeDataCache)}`\n}\n\nexport async function getDynamicDataPostponedState(\n  resumeDataCache: PrerenderResumeDataCache | RenderResumeDataCache\n): Promise<string> {\n  return `4:null${await stringifyResumeDataCache(createRenderResumeDataCache(resumeDataCache))}`\n}\n\nexport function parsePostponedState(\n  state: string,\n  params: Params | undefined\n): PostponedState {\n  try {\n    const postponedStringLengthMatch = state.match(/^([0-9]*):/)?.[1]\n    if (!postponedStringLengthMatch) {\n      throw new Error(`Invariant: invalid postponed state ${state}`)\n    }\n\n    const postponedStringLength = parseInt(postponedStringLengthMatch)\n\n    // We add a `:` to the end of the length as the first character of the\n    // postponed string is the length of the replacement entries.\n    const postponedString = state.slice(\n      postponedStringLengthMatch.length + 1,\n      postponedStringLengthMatch.length + postponedStringLength + 1\n    )\n\n    const renderResumeDataCache = createRenderResumeDataCache(\n      state.slice(postponedStringLengthMatch.length + postponedStringLength + 1)\n    )\n\n    try {\n      if (postponedString === 'null') {\n        return { type: DynamicState.DATA, renderResumeDataCache }\n      }\n\n      if (/^[0-9]/.test(postponedString)) {\n        const match = postponedString.match(/^([0-9]*)/)?.[1]\n        if (!match) {\n          throw new Error(\n            `Invariant: invalid postponed state ${JSON.stringify(postponedString)}`\n          )\n        }\n\n        // This is the length of the replacements entries.\n        const length = parseInt(match)\n        const replacements = JSON.parse(\n          postponedString.slice(\n            match.length,\n            // We then go to the end of the string.\n            match.length + length\n          )\n        ) as ReadonlyArray<[string, string]>\n\n        let postponed = postponedString.slice(match.length + length)\n        for (const [key, searchValue] of replacements) {\n          const value = params?.[key] ?? ''\n          const replaceValue = Array.isArray(value) ? value.join('/') : value\n          postponed = postponed.replaceAll(searchValue, replaceValue)\n        }\n\n        return {\n          type: DynamicState.HTML,\n          data: JSON.parse(postponed),\n          renderResumeDataCache,\n        }\n      }\n\n      return {\n        type: DynamicState.HTML,\n        data: JSON.parse(postponedString),\n        renderResumeDataCache,\n      }\n    } catch (err) {\n      console.error('Failed to parse postponed state', err)\n      return { type: DynamicState.DATA, renderResumeDataCache }\n    }\n  } catch (err) {\n    console.error('Failed to parse postponed state', err)\n    return {\n      type: DynamicState.DATA,\n      renderResumeDataCache: createPrerenderResumeDataCache(),\n    }\n  }\n}\n\nexport function getPostponedFromState(state: PostponedState): any {\n  if (state.type === DynamicState.DATA) {\n    return null\n  }\n\n  return state.data\n}\n"], "names": ["DynamicState", "getDynamicDataPostponedState", "getDynamicHTMLPostponedState", "getPostponedFromState", "parsePostponedState", "data", "fallbackRouteParams", "resumeDataCache", "size", "postponedString", "JSON", "stringify", "length", "stringifyResumeDataCache", "createRenderResumeDataCache", "replacements", "Array", "from", "replacementsString", "dataString", "state", "params", "postponedStringLengthMatch", "match", "Error", "postponedStringLength", "parseInt", "slice", "renderResumeDataCache", "type", "test", "parse", "postponed", "key", "searchValue", "value", "replaceValue", "isArray", "join", "replaceAll", "err", "console", "error", "createPrerenderResumeDataCache"], "mappings": ";;;;;;;;;;;;;;;;;;IAUYA,YAAY;eAAZA;;IA4EUC,4BAA4B;eAA5BA;;IAzBAC,4BAA4B;eAA5BA;;IA6GNC,qBAAqB;eAArBA;;IA9EAC,mBAAmB;eAAnBA;;;iCArFT;AAGA,IAAA,AAAKJ,sCAAAA;IACV;;GAEC;IAGD;;GAEC;WARSA;;AAmDL,eAAeE,6BACpBG,IAAY,EACZC,mBAA+C,EAC/CC,eAAiE;IAEjE,IAAI,CAACD,uBAAuBA,oBAAoBE,IAAI,KAAK,GAAG;QAC1D,MAAMC,kBAAkBC,KAAKC,SAAS,CAACN;QAEvC,oFAAoF;QACpF,OAAO,GAAGI,gBAAgBG,MAAM,CAAC,CAAC,EAAEH,kBAAkB,MAAMI,IAAAA,yCAAwB,EAClFC,IAAAA,4CAA2B,EAACP,mBAC3B;IACL;IAEA,MAAMQ,eAAwCC,MAAMC,IAAI,CAACX;IACzD,MAAMY,qBAAqBR,KAAKC,SAAS,CAACI;IAC1C,MAAMI,aAAaT,KAAKC,SAAS,CAACN;IAElC,4DAA4D;IAC5D,MAAMI,kBAAkB,GAAGS,mBAAmBN,MAAM,GAAGM,qBAAqBC,YAAY;IAExF,oFAAoF;IACpF,OAAO,GAAGV,gBAAgBG,MAAM,CAAC,CAAC,EAAEH,kBAAkB,MAAMI,IAAAA,yCAAwB,EAACN,kBAAkB;AACzG;AAEO,eAAeN,6BACpBM,eAAiE;IAEjE,OAAO,CAAC,MAAM,EAAE,MAAMM,IAAAA,yCAAwB,EAACC,IAAAA,4CAA2B,EAACP,mBAAmB;AAChG;AAEO,SAASH,oBACdgB,KAAa,EACbC,MAA0B;IAE1B,IAAI;YACiCD;QAAnC,MAAME,8BAA6BF,eAAAA,MAAMG,KAAK,CAAC,kCAAZH,YAA2B,CAAC,EAAE;QACjE,IAAI,CAACE,4BAA4B;YAC/B,MAAM,qBAAwD,CAAxD,IAAIE,MAAM,CAAC,mCAAmC,EAAEJ,OAAO,GAAvD,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,MAAMK,wBAAwBC,SAASJ;QAEvC,sEAAsE;QACtE,6DAA6D;QAC7D,MAAMb,kBAAkBW,MAAMO,KAAK,CACjCL,2BAA2BV,MAAM,GAAG,GACpCU,2BAA2BV,MAAM,GAAGa,wBAAwB;QAG9D,MAAMG,wBAAwBd,IAAAA,4CAA2B,EACvDM,MAAMO,KAAK,CAACL,2BAA2BV,MAAM,GAAGa,wBAAwB;QAG1E,IAAI;YACF,IAAIhB,oBAAoB,QAAQ;gBAC9B,OAAO;oBAAEoB,IAAI;oBAAqBD;gBAAsB;YAC1D;YAEA,IAAI,SAASE,IAAI,CAACrB,kBAAkB;oBACpBA;gBAAd,MAAMc,SAAQd,yBAAAA,gBAAgBc,KAAK,CAAC,iCAAtBd,sBAAoC,CAAC,EAAE;gBACrD,IAAI,CAACc,OAAO;oBACV,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,mCAAmC,EAAEd,KAAKC,SAAS,CAACF,kBAAkB,GADnE,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,kDAAkD;gBAClD,MAAMG,SAASc,SAASH;gBACxB,MAAMR,eAAeL,KAAKqB,KAAK,CAC7BtB,gBAAgBkB,KAAK,CACnBJ,MAAMX,MAAM,EACZ,uCAAuC;gBACvCW,MAAMX,MAAM,GAAGA;gBAInB,IAAIoB,YAAYvB,gBAAgBkB,KAAK,CAACJ,MAAMX,MAAM,GAAGA;gBACrD,KAAK,MAAM,CAACqB,KAAKC,YAAY,IAAInB,aAAc;oBAC7C,MAAMoB,QAAQd,CAAAA,0BAAAA,MAAQ,CAACY,IAAI,KAAI;oBAC/B,MAAMG,eAAepB,MAAMqB,OAAO,CAACF,SAASA,MAAMG,IAAI,CAAC,OAAOH;oBAC9DH,YAAYA,UAAUO,UAAU,CAACL,aAAaE;gBAChD;gBAEA,OAAO;oBACLP,IAAI;oBACJxB,MAAMK,KAAKqB,KAAK,CAACC;oBACjBJ;gBACF;YACF;YAEA,OAAO;gBACLC,IAAI;gBACJxB,MAAMK,KAAKqB,KAAK,CAACtB;gBACjBmB;YACF;QACF,EAAE,OAAOY,KAAK;YACZC,QAAQC,KAAK,CAAC,mCAAmCF;YACjD,OAAO;gBAAEX,IAAI;gBAAqBD;YAAsB;QAC1D;IACF,EAAE,OAAOY,KAAK;QACZC,QAAQC,KAAK,CAAC,mCAAmCF;QACjD,OAAO;YACLX,IAAI;YACJD,uBAAuBe,IAAAA,+CAA8B;QACvD;IACF;AACF;AAEO,SAASxC,sBAAsBiB,KAAqB;IACzD,IAAIA,MAAMS,IAAI,QAAwB;QACpC,OAAO;IACT;IAEA,OAAOT,MAAMf,IAAI;AACnB", "ignoreList": [0]}