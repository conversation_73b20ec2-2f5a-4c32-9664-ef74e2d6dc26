{"name": "prompt-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.2", "@trpc/server": "^11.0.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/next": "^11.0.0", "@tanstack/react-query": "^5.0.0", "zod": "^3.22.0", "superjson": "^2.2.0", "next-auth": "^4.24.0", "@next-auth/prisma-adapter": "^1.0.7", "prisma": "^5.0.0", "@prisma/client": "^5.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.2", "@eslint/eslintrc": "^3", "daisyui": "^5.0.46", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9"}}