{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api(?!/auth)|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api(?!/auth)|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "G0rvZwDjitE6aMxnjTLk6LX63W71j0ht0ONrGDcB0pc=", "__NEXT_PREVIEW_MODE_ID": "e16647324e4c6329d84432af5d0f796b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7bc75999d6c053f8b08919f2ead9a3982ac65f3386a74d120cc70c14ea2536d5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "447318df10bc3caae3be337fe609812e7ad10e1d373b323ebe5a3f258bccc269"}}}, "instrumentation": null, "functions": {}}