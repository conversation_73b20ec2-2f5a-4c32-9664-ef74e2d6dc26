"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(ssr)/../node_modules/next-auth/client/_utils.js":
/*!**************************************************!*\
  !*** ../node_modules/next-auth/client/_utils.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/../node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.BroadcastChannel = BroadcastChannel;\nexports.apiBaseUrl = apiBaseUrl;\nexports.fetchData = fetchData;\nexports.now = now;\n\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/../node_modules/@babel/runtime/regenerator/index.js\"));\n\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/defineProperty.js\"));\n\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/../node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction fetchData(_x, _x2, _x3) {\n  return _fetchData.apply(this, arguments);\n}\n\nfunction _fetchData() {\n  _fetchData = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(path, __NEXTAUTH, logger) {\n    var _ref,\n        ctx,\n        _ref$req,\n        req,\n        url,\n        _req$headers,\n        options,\n        res,\n        data,\n        _args = arguments;\n\n    return _regenerator.default.wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            _ref = _args.length > 3 && _args[3] !== undefined ? _args[3] : {}, ctx = _ref.ctx, _ref$req = _ref.req, req = _ref$req === void 0 ? ctx === null || ctx === void 0 ? void 0 : ctx.req : _ref$req;\n            url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n            _context.prev = 2;\n            options = {\n              headers: _objectSpread({\n                \"Content-Type\": \"application/json\"\n              }, req !== null && req !== void 0 && (_req$headers = req.headers) !== null && _req$headers !== void 0 && _req$headers.cookie ? {\n                cookie: req.headers.cookie\n              } : {})\n            };\n\n            if (req !== null && req !== void 0 && req.body) {\n              options.body = JSON.stringify(req.body);\n              options.method = \"POST\";\n            }\n\n            _context.next = 7;\n            return fetch(url, options);\n\n          case 7:\n            res = _context.sent;\n            _context.next = 10;\n            return res.json();\n\n          case 10:\n            data = _context.sent;\n\n            if (res.ok) {\n              _context.next = 13;\n              break;\n            }\n\n            throw data;\n\n          case 13:\n            return _context.abrupt(\"return\", Object.keys(data).length > 0 ? data : null);\n\n          case 16:\n            _context.prev = 16;\n            _context.t0 = _context[\"catch\"](2);\n            logger.error(\"CLIENT_FETCH_ERROR\", {\n              error: _context.t0,\n              url: url\n            });\n            return _context.abrupt(\"return\", null);\n\n          case 20:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee, null, [[2, 16]]);\n  }));\n  return _fetchData.apply(this, arguments);\n}\n\nfunction apiBaseUrl(__NEXTAUTH) {\n  if (typeof window === \"undefined\") {\n    return \"\".concat(__NEXTAUTH.baseUrlServer).concat(__NEXTAUTH.basePathServer);\n  }\n\n  return __NEXTAUTH.basePath;\n}\n\nfunction now() {\n  return Math.floor(Date.now() / 1000);\n}\n\nfunction BroadcastChannel() {\n  var name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"nextauth.message\";\n  return {\n    receive: function receive(onReceive) {\n      var handler = function handler(event) {\n        var _event$newValue;\n\n        if (event.key !== name) return;\n        var message = JSON.parse((_event$newValue = event.newValue) !== null && _event$newValue !== void 0 ? _event$newValue : \"{}\");\n        if ((message === null || message === void 0 ? void 0 : message.event) !== \"session\" || !(message !== null && message !== void 0 && message.data)) return;\n        onReceive(message);\n      };\n\n      window.addEventListener(\"storage\", handler);\n      return function () {\n        return window.removeEventListener(\"storage\", handler);\n      };\n    },\n    post: function post(message) {\n      if (typeof window === \"undefined\") return;\n\n      try {\n        localStorage.setItem(name, JSON.stringify(_objectSpread(_objectSpread({}, message), {}, {\n          timestamp: now()\n        })));\n      } catch (_unused) {}\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQtYXV0aC9jbGllbnQvX3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDZCQUE2QixtQkFBTyxDQUFDLDJIQUE4Qzs7QUFFbkYsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysd0JBQXdCO0FBQ3hCLGtCQUFrQjtBQUNsQixpQkFBaUI7QUFDakIsV0FBVzs7QUFFWCwwQ0FBMEMsbUJBQU8sQ0FBQyw2RkFBNEI7O0FBRTlFLDhDQUE4QyxtQkFBTyxDQUFDLDZHQUF1Qzs7QUFFN0YsZ0RBQWdELG1CQUFPLENBQUMsaUhBQXlDOztBQUVqRywyQ0FBMkMsZ0NBQWdDLG9DQUFvQyxvREFBb0QsNkRBQTZELGlFQUFpRSxzQ0FBc0M7O0FBRXZVLGlDQUFpQyxnQkFBZ0Isc0JBQXNCLE9BQU8sdURBQXVELDZEQUE2RCwwREFBMEQsb0tBQW9LLG1GQUFtRixLQUFLOztBQUV4ZjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkVBQTZFO0FBQzdFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQSxnQkFBZ0IsSUFBSTtBQUNwQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrSUFBa0k7QUFDbEk7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQSxnRkFBZ0YsY0FBYztBQUM5RjtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXEF1Z21lbnRcXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxjbGllbnRcXF91dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIik7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLkJyb2FkY2FzdENoYW5uZWwgPSBCcm9hZGNhc3RDaGFubmVsO1xuZXhwb3J0cy5hcGlCYXNlVXJsID0gYXBpQmFzZVVybDtcbmV4cG9ydHMuZmV0Y2hEYXRhID0gZmV0Y2hEYXRhO1xuZXhwb3J0cy5ub3cgPSBub3c7XG5cbnZhciBfcmVnZW5lcmF0b3IgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9yZWdlbmVyYXRvclwiKSk7XG5cbnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eVwiKSk7XG5cbnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3JcIikpO1xuXG5mdW5jdGlvbiBvd25LZXlzKG9iamVjdCwgZW51bWVyYWJsZU9ubHkpIHsgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhvYmplY3QpOyBpZiAoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scykgeyB2YXIgc3ltYm9scyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMob2JqZWN0KTsgZW51bWVyYWJsZU9ubHkgJiYgKHN5bWJvbHMgPSBzeW1ib2xzLmZpbHRlcihmdW5jdGlvbiAoc3ltKSB7IHJldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iamVjdCwgc3ltKS5lbnVtZXJhYmxlOyB9KSksIGtleXMucHVzaC5hcHBseShrZXlzLCBzeW1ib2xzKTsgfSByZXR1cm4ga2V5czsgfVxuXG5mdW5jdGlvbiBfb2JqZWN0U3ByZWFkKHRhcmdldCkgeyBmb3IgKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKykgeyB2YXIgc291cmNlID0gbnVsbCAhPSBhcmd1bWVudHNbaV0gPyBhcmd1bWVudHNbaV0gOiB7fTsgaSAlIDIgPyBvd25LZXlzKE9iamVjdChzb3VyY2UpLCAhMCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7ICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKHRhcmdldCwga2V5LCBzb3VyY2Vba2V5XSk7IH0pIDogT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnMgPyBPYmplY3QuZGVmaW5lUHJvcGVydGllcyh0YXJnZXQsIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKHNvdXJjZSkpIDogb3duS2V5cyhPYmplY3Qoc291cmNlKSkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihzb3VyY2UsIGtleSkpOyB9KTsgfSByZXR1cm4gdGFyZ2V0OyB9XG5cbmZ1bmN0aW9uIGZldGNoRGF0YShfeCwgX3gyLCBfeDMpIHtcbiAgcmV0dXJuIF9mZXRjaERhdGEuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cblxuZnVuY3Rpb24gX2ZldGNoRGF0YSgpIHtcbiAgX2ZldGNoRGF0YSA9ICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoX3JlZ2VuZXJhdG9yLmRlZmF1bHQubWFyayhmdW5jdGlvbiBfY2FsbGVlKHBhdGgsIF9fTkVYVEFVVEgsIGxvZ2dlcikge1xuICAgIHZhciBfcmVmLFxuICAgICAgICBjdHgsXG4gICAgICAgIF9yZWYkcmVxLFxuICAgICAgICByZXEsXG4gICAgICAgIHVybCxcbiAgICAgICAgX3JlcSRoZWFkZXJzLFxuICAgICAgICBvcHRpb25zLFxuICAgICAgICByZXMsXG4gICAgICAgIGRhdGEsXG4gICAgICAgIF9hcmdzID0gYXJndW1lbnRzO1xuXG4gICAgcmV0dXJuIF9yZWdlbmVyYXRvci5kZWZhdWx0LndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHtcbiAgICAgIHdoaWxlICgxKSB7XG4gICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHtcbiAgICAgICAgICBjYXNlIDA6XG4gICAgICAgICAgICBfcmVmID0gX2FyZ3MubGVuZ3RoID4gMyAmJiBfYXJnc1szXSAhPT0gdW5kZWZpbmVkID8gX2FyZ3NbM10gOiB7fSwgY3R4ID0gX3JlZi5jdHgsIF9yZWYkcmVxID0gX3JlZi5yZXEsIHJlcSA9IF9yZWYkcmVxID09PSB2b2lkIDAgPyBjdHggPT09IG51bGwgfHwgY3R4ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjdHgucmVxIDogX3JlZiRyZXE7XG4gICAgICAgICAgICB1cmwgPSBcIlwiLmNvbmNhdChhcGlCYXNlVXJsKF9fTkVYVEFVVEgpLCBcIi9cIikuY29uY2F0KHBhdGgpO1xuICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDI7XG4gICAgICAgICAgICBvcHRpb25zID0ge1xuICAgICAgICAgICAgICBoZWFkZXJzOiBfb2JqZWN0U3ByZWFkKHtcbiAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIlxuICAgICAgICAgICAgICB9LCByZXEgIT09IG51bGwgJiYgcmVxICE9PSB2b2lkIDAgJiYgKF9yZXEkaGVhZGVycyA9IHJlcS5oZWFkZXJzKSAhPT0gbnVsbCAmJiBfcmVxJGhlYWRlcnMgIT09IHZvaWQgMCAmJiBfcmVxJGhlYWRlcnMuY29va2llID8ge1xuICAgICAgICAgICAgICAgIGNvb2tpZTogcmVxLmhlYWRlcnMuY29va2llXG4gICAgICAgICAgICAgIH0gOiB7fSlcbiAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgIGlmIChyZXEgIT09IG51bGwgJiYgcmVxICE9PSB2b2lkIDAgJiYgcmVxLmJvZHkpIHtcbiAgICAgICAgICAgICAgb3B0aW9ucy5ib2R5ID0gSlNPTi5zdHJpbmdpZnkocmVxLmJvZHkpO1xuICAgICAgICAgICAgICBvcHRpb25zLm1ldGhvZCA9IFwiUE9TVFwiO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNztcbiAgICAgICAgICAgIHJldHVybiBmZXRjaCh1cmwsIG9wdGlvbnMpO1xuXG4gICAgICAgICAgY2FzZSA3OlxuICAgICAgICAgICAgcmVzID0gX2NvbnRleHQuc2VudDtcbiAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMDtcbiAgICAgICAgICAgIHJldHVybiByZXMuanNvbigpO1xuXG4gICAgICAgICAgY2FzZSAxMDpcbiAgICAgICAgICAgIGRhdGEgPSBfY29udGV4dC5zZW50O1xuXG4gICAgICAgICAgICBpZiAocmVzLm9rKSB7XG4gICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMztcbiAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHRocm93IGRhdGE7XG5cbiAgICAgICAgICBjYXNlIDEzOlxuICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmFicnVwdChcInJldHVyblwiLCBPYmplY3Qua2V5cyhkYXRhKS5sZW5ndGggPiAwID8gZGF0YSA6IG51bGwpO1xuXG4gICAgICAgICAgY2FzZSAxNjpcbiAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxNjtcbiAgICAgICAgICAgIF9jb250ZXh0LnQwID0gX2NvbnRleHRbXCJjYXRjaFwiXSgyKTtcbiAgICAgICAgICAgIGxvZ2dlci5lcnJvcihcIkNMSUVOVF9GRVRDSF9FUlJPUlwiLCB7XG4gICAgICAgICAgICAgIGVycm9yOiBfY29udGV4dC50MCxcbiAgICAgICAgICAgICAgdXJsOiB1cmxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmFicnVwdChcInJldHVyblwiLCBudWxsKTtcblxuICAgICAgICAgIGNhc2UgMjA6XG4gICAgICAgICAgY2FzZSBcImVuZFwiOlxuICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sIF9jYWxsZWUsIG51bGwsIFtbMiwgMTZdXSk7XG4gIH0pKTtcbiAgcmV0dXJuIF9mZXRjaERhdGEuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cblxuZnVuY3Rpb24gYXBpQmFzZVVybChfX05FWFRBVVRIKSB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgcmV0dXJuIFwiXCIuY29uY2F0KF9fTkVYVEFVVEguYmFzZVVybFNlcnZlcikuY29uY2F0KF9fTkVYVEFVVEguYmFzZVBhdGhTZXJ2ZXIpO1xuICB9XG5cbiAgcmV0dXJuIF9fTkVYVEFVVEguYmFzZVBhdGg7XG59XG5cbmZ1bmN0aW9uIG5vdygpIHtcbiAgcmV0dXJuIE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xufVxuXG5mdW5jdGlvbiBCcm9hZGNhc3RDaGFubmVsKCkge1xuICB2YXIgbmFtZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogXCJuZXh0YXV0aC5tZXNzYWdlXCI7XG4gIHJldHVybiB7XG4gICAgcmVjZWl2ZTogZnVuY3Rpb24gcmVjZWl2ZShvblJlY2VpdmUpIHtcbiAgICAgIHZhciBoYW5kbGVyID0gZnVuY3Rpb24gaGFuZGxlcihldmVudCkge1xuICAgICAgICB2YXIgX2V2ZW50JG5ld1ZhbHVlO1xuXG4gICAgICAgIGlmIChldmVudC5rZXkgIT09IG5hbWUpIHJldHVybjtcbiAgICAgICAgdmFyIG1lc3NhZ2UgPSBKU09OLnBhcnNlKChfZXZlbnQkbmV3VmFsdWUgPSBldmVudC5uZXdWYWx1ZSkgIT09IG51bGwgJiYgX2V2ZW50JG5ld1ZhbHVlICE9PSB2b2lkIDAgPyBfZXZlbnQkbmV3VmFsdWUgOiBcInt9XCIpO1xuICAgICAgICBpZiAoKG1lc3NhZ2UgPT09IG51bGwgfHwgbWVzc2FnZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogbWVzc2FnZS5ldmVudCkgIT09IFwic2Vzc2lvblwiIHx8ICEobWVzc2FnZSAhPT0gbnVsbCAmJiBtZXNzYWdlICE9PSB2b2lkIDAgJiYgbWVzc2FnZS5kYXRhKSkgcmV0dXJuO1xuICAgICAgICBvblJlY2VpdmUobWVzc2FnZSk7XG4gICAgICB9O1xuXG4gICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInN0b3JhZ2VcIiwgaGFuZGxlcik7XG4gICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzdG9yYWdlXCIsIGhhbmRsZXIpO1xuICAgICAgfTtcbiAgICB9LFxuICAgIHBvc3Q6IGZ1bmN0aW9uIHBvc3QobWVzc2FnZSkge1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpIHJldHVybjtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0obmFtZSwgSlNPTi5zdHJpbmdpZnkoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBtZXNzYWdlKSwge30sIHtcbiAgICAgICAgICB0aW1lc3RhbXA6IG5vdygpXG4gICAgICAgIH0pKSk7XG4gICAgICB9IGNhdGNoIChfdW51c2VkKSB7fVxuICAgIH1cbiAgfTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next-auth/client/_utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/next-auth/core/errors.js":
/*!************************************************!*\
  !*** ../node_modules/next-auth/core/errors.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/../node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\n\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/../node_modules/@babel/runtime/regenerator/index.js\"));\n\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/../node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/assertThisInitialized */ \"(ssr)/../node_modules/@babel/runtime/helpers/assertThisInitialized.js\"));\n\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/defineProperty.js\"));\n\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(ssr)/../node_modules/@babel/runtime/helpers/classCallCheck.js\"));\n\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(ssr)/../node_modules/@babel/runtime/helpers/createClass.js\"));\n\nvar _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(ssr)/../node_modules/@babel/runtime/helpers/inherits.js\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"(ssr)/../node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"(ssr)/../node_modules/@babel/runtime/helpers/getPrototypeOf.js\"));\n\nvar _wrapNativeSuper2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"(ssr)/../node_modules/@babel/runtime/helpers/wrapNativeSuper.js\"));\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = (0, _getPrototypeOf2.default)(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = (0, _getPrototypeOf2.default)(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return (0, _possibleConstructorReturn2.default)(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nvar UnknownError = function (_Error) {\n  (0, _inherits2.default)(UnknownError, _Error);\n\n  var _super = _createSuper(UnknownError);\n\n  function UnknownError(error) {\n    var _message;\n\n    var _this;\n\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _super.call(this, (_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n\n    return _this;\n  }\n\n  (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n  return UnknownError;\n}((0, _wrapNativeSuper2.default)(Error));\n\nexports.UnknownError = UnknownError;\n\nvar OAuthCallbackError = function (_UnknownError) {\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n\n  var _super2 = _createSuper(OAuthCallbackError);\n\n  function OAuthCallbackError() {\n    var _this2;\n\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this2 = _super2.call.apply(_super2, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this2), \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\n\nexports.OAuthCallbackError = OAuthCallbackError;\n\nvar AccountNotLinkedError = function (_UnknownError2) {\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n\n  var _super3 = _createSuper(AccountNotLinkedError);\n\n  function AccountNotLinkedError() {\n    var _this3;\n\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    _this3 = _super3.call.apply(_super3, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this3), \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\n\nexports.AccountNotLinkedError = AccountNotLinkedError;\n\nvar MissingAPIRoute = function (_UnknownError3) {\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n\n  var _super4 = _createSuper(MissingAPIRoute);\n\n  function MissingAPIRoute() {\n    var _this4;\n\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n\n    _this4 = _super4.call.apply(_super4, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this4), \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this4), \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\n\nexports.MissingAPIRoute = MissingAPIRoute;\n\nvar MissingSecret = function (_UnknownError4) {\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n\n  var _super5 = _createSuper(MissingSecret);\n\n  function MissingSecret() {\n    var _this5;\n\n    (0, _classCallCheck2.default)(this, MissingSecret);\n\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n\n    _this5 = _super5.call.apply(_super5, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this5), \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this5), \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\n\nexports.MissingSecret = MissingSecret;\n\nvar MissingAuthorize = function (_UnknownError5) {\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n\n  var _super6 = _createSuper(MissingAuthorize);\n\n  function MissingAuthorize() {\n    var _this6;\n\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n\n    _this6 = _super6.call.apply(_super6, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this6), \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this6), \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\n\nexports.MissingAuthorize = MissingAuthorize;\n\nvar MissingAdapter = function (_UnknownError6) {\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n\n  var _super7 = _createSuper(MissingAdapter);\n\n  function MissingAdapter() {\n    var _this7;\n\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n\n    _this7 = _super7.call.apply(_super7, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this7), \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this7), \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\n\nexports.MissingAdapter = MissingAdapter;\n\nvar MissingAdapterMethods = function (_UnknownError7) {\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n\n  var _super8 = _createSuper(MissingAdapterMethods);\n\n  function MissingAdapterMethods() {\n    var _this8;\n\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n\n    _this8 = _super8.call.apply(_super8, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this8), \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this8), \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\n\nexports.MissingAdapterMethods = MissingAdapterMethods;\n\nvar UnsupportedStrategy = function (_UnknownError8) {\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n\n  var _super9 = _createSuper(UnsupportedStrategy);\n\n  function UnsupportedStrategy() {\n    var _this9;\n\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n\n    _this9 = _super9.call.apply(_super9, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this9), \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this9), \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\n\nexports.UnsupportedStrategy = UnsupportedStrategy;\n\nvar InvalidCallbackUrl = function (_UnknownError9) {\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n\n  var _super10 = _createSuper(InvalidCallbackUrl);\n\n  function InvalidCallbackUrl() {\n    var _this10;\n\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n\n    _this10 = _super10.call.apply(_super10, [this].concat(args));\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this10), \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this10), \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\n\nexports.InvalidCallbackUrl = InvalidCallbackUrl;\n\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\n\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\n\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n          _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              method = methods[name];\n              _context.next = 4;\n              return method.apply(void 0, _args);\n\n            case 4:\n              return _context.abrupt(\"return\", _context.sent);\n\n            case 7:\n              _context.prev = 7;\n              _context.t0 = _context[\"catch\"](0);\n              logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n\n            case 10:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\n\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n          args,\n          _key10,\n          method,\n          e,\n          _args2 = arguments;\n\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) {\n          switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n\n              for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n                args[_key10] = _args2[_key10];\n              }\n\n              logger.debug(\"adapter_\".concat(name), {\n                args: args\n              });\n              method = adapter[name];\n              _context2.next = 6;\n              return method.apply(void 0, args);\n\n            case 6:\n              return _context2.abrupt(\"return\", _context2.sent);\n\n            case 9:\n              _context2.prev = 9;\n              _context2.t0 = _context2[\"catch\"](0);\n              logger.error(\"adapter_error_\".concat(name), _context2.t0);\n              e = new UnknownError(_context2.t0);\n              e.name = \"\".concat(capitalize(name), \"Error\");\n              throw e;\n\n            case 15:\n            case \"end\":\n              return _context2.stop();\n          }\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next-auth/core/errors.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/next-auth/react/index.js":
/*!************************************************!*\
  !*** ../node_modules/next-auth/react/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/../node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\n\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"(ssr)/../node_modules/@babel/runtime/helpers/typeof.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {\n  SessionContext: true,\n  useSession: true,\n  getSession: true,\n  getCsrfToken: true,\n  getProviders: true,\n  signIn: true,\n  signOut: true,\n  SessionProvider: true\n};\nexports.SessionContext = void 0;\nexports.SessionProvider = SessionProvider;\nexports.getCsrfToken = getCsrfToken;\nexports.getProviders = getProviders;\nexports.getSession = getSession;\nexports.signIn = signIn;\nexports.signOut = signOut;\nexports.useSession = useSession;\n\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/../node_modules/@babel/runtime/regenerator/index.js\"));\n\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/defineProperty.js\"));\n\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/../node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\n\nvar _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"(ssr)/../node_modules/@babel/runtime/helpers/slicedToArray.js\"));\n\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"));\n\nvar _logger2 = _interopRequireWildcard(__webpack_require__(/*! ../utils/logger */ \"(ssr)/../node_modules/next-auth/utils/logger.js\"));\n\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../utils/parse-url */ \"(ssr)/../node_modules/next-auth/utils/parse-url.js\"));\n\nvar _utils = __webpack_require__(/*! ../client/_utils */ \"(ssr)/../node_modules/next-auth/client/_utils.js\");\n\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\nvar _types = __webpack_require__(/*! ./types */ \"(ssr)/../node_modules/next-auth/react/types.js\");\n\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function get() {\n      return _types[key];\n    }\n  });\n});\n\nvar _process$env$NEXTAUTH, _ref, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _React$createContext;\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nvar __NEXTAUTH = {\n  baseUrl: (0, _parseUrl.default)((_process$env$NEXTAUTH = process.env.NEXTAUTH_URL) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.VERCEL_URL).origin,\n  basePath: (0, _parseUrl.default)(process.env.NEXTAUTH_URL).path,\n  baseUrlServer: (0, _parseUrl.default)((_ref = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n  basePathServer: (0, _parseUrl.default)((_process$env$NEXTAUTH3 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.NEXTAUTH_URL).path,\n  _lastSync: 0,\n  _session: undefined,\n  _getSession: function _getSession() {}\n};\nvar broadcast = (0, _utils.BroadcastChannel)();\nvar logger = (0, _logger2.proxyLogger)(_logger2.default, __NEXTAUTH.basePath);\n\nfunction useOnline() {\n  var _React$useState = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      isOnline = _React$useState2[0],\n      setIsOnline = _React$useState2[1];\n\n  var setOnline = function setOnline() {\n    return setIsOnline(true);\n  };\n\n  var setOffline = function setOffline() {\n    return setIsOnline(false);\n  };\n\n  React.useEffect(function () {\n    window.addEventListener(\"online\", setOnline);\n    window.addEventListener(\"offline\", setOffline);\n    return function () {\n      window.removeEventListener(\"online\", setOnline);\n      window.removeEventListener(\"offline\", setOffline);\n    };\n  }, []);\n  return isOnline;\n}\n\nvar SessionContext = (_React$createContext = React.createContext) === null || _React$createContext === void 0 ? void 0 : _React$createContext.call(React, undefined);\nexports.SessionContext = SessionContext;\n\nfunction useSession(options) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n\n  var value = React.useContext(SessionContext);\n\n  if (!value && \"development\" !== \"production\") {\n    throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n  }\n\n  var _ref2 = options !== null && options !== void 0 ? options : {},\n      required = _ref2.required,\n      onUnauthenticated = _ref2.onUnauthenticated;\n\n  var requiredAndNotLoading = required && value.status === \"unauthenticated\";\n  React.useEffect(function () {\n    if (requiredAndNotLoading) {\n      var url = \"/api/auth/signin?\".concat(new URLSearchParams({\n        error: \"SessionRequired\",\n        callbackUrl: window.location.href\n      }));\n      if (onUnauthenticated) onUnauthenticated();else window.location.href = url;\n    }\n  }, [requiredAndNotLoading, onUnauthenticated]);\n\n  if (requiredAndNotLoading) {\n    return {\n      data: value.data,\n      update: value.update,\n      status: \"loading\"\n    };\n  }\n\n  return value;\n}\n\nfunction getSession(_x) {\n  return _getSession2.apply(this, arguments);\n}\n\nfunction _getSession2() {\n  _getSession2 = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee3(params) {\n    var _params$broadcast;\n\n    var session;\n    return _regenerator.default.wrap(function _callee3$(_context3) {\n      while (1) {\n        switch (_context3.prev = _context3.next) {\n          case 0:\n            _context3.next = 2;\n            return (0, _utils.fetchData)(\"session\", __NEXTAUTH, logger, params);\n\n          case 2:\n            session = _context3.sent;\n\n            if ((_params$broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params$broadcast !== void 0 ? _params$broadcast : true) {\n              broadcast.post({\n                event: \"session\",\n                data: {\n                  trigger: \"getSession\"\n                }\n              });\n            }\n\n            return _context3.abrupt(\"return\", session);\n\n          case 5:\n          case \"end\":\n            return _context3.stop();\n        }\n      }\n    }, _callee3);\n  }));\n  return _getSession2.apply(this, arguments);\n}\n\nfunction getCsrfToken(_x2) {\n  return _getCsrfToken.apply(this, arguments);\n}\n\nfunction _getCsrfToken() {\n  _getCsrfToken = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee4(params) {\n    var response;\n    return _regenerator.default.wrap(function _callee4$(_context4) {\n      while (1) {\n        switch (_context4.prev = _context4.next) {\n          case 0:\n            _context4.next = 2;\n            return (0, _utils.fetchData)(\"csrf\", __NEXTAUTH, logger, params);\n\n          case 2:\n            response = _context4.sent;\n            return _context4.abrupt(\"return\", response === null || response === void 0 ? void 0 : response.csrfToken);\n\n          case 4:\n          case \"end\":\n            return _context4.stop();\n        }\n      }\n    }, _callee4);\n  }));\n  return _getCsrfToken.apply(this, arguments);\n}\n\nfunction getProviders() {\n  return _getProviders.apply(this, arguments);\n}\n\nfunction _getProviders() {\n  _getProviders = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee5() {\n    return _regenerator.default.wrap(function _callee5$(_context5) {\n      while (1) {\n        switch (_context5.prev = _context5.next) {\n          case 0:\n            _context5.next = 2;\n            return (0, _utils.fetchData)(\"providers\", __NEXTAUTH, logger);\n\n          case 2:\n            return _context5.abrupt(\"return\", _context5.sent);\n\n          case 3:\n          case \"end\":\n            return _context5.stop();\n        }\n      }\n    }, _callee5);\n  }));\n  return _getProviders.apply(this, arguments);\n}\n\nfunction signIn(_x3, _x4, _x5) {\n  return _signIn.apply(this, arguments);\n}\n\nfunction _signIn() {\n  _signIn = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee6(provider, options, authorizationParams) {\n    var _ref5, _ref5$callbackUrl, callbackUrl, _ref5$redirect, redirect, baseUrl, providers, isCredentials, isEmail, isSupportingReturn, signInUrl, _signInUrl, res, data, _data$url, url, error;\n\n    return _regenerator.default.wrap(function _callee6$(_context6) {\n      while (1) {\n        switch (_context6.prev = _context6.next) {\n          case 0:\n            _ref5 = options !== null && options !== void 0 ? options : {}, _ref5$callbackUrl = _ref5.callbackUrl, callbackUrl = _ref5$callbackUrl === void 0 ? window.location.href : _ref5$callbackUrl, _ref5$redirect = _ref5.redirect, redirect = _ref5$redirect === void 0 ? true : _ref5$redirect;\n            baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n            _context6.next = 4;\n            return getProviders();\n\n          case 4:\n            providers = _context6.sent;\n\n            if (providers) {\n              _context6.next = 8;\n              break;\n            }\n\n            window.location.href = \"\".concat(baseUrl, \"/error\");\n            return _context6.abrupt(\"return\");\n\n          case 8:\n            if (!(!provider || !(provider in providers))) {\n              _context6.next = 11;\n              break;\n            }\n\n            window.location.href = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n              callbackUrl: callbackUrl\n            }));\n            return _context6.abrupt(\"return\");\n\n          case 11:\n            isCredentials = providers[provider].type === \"credentials\";\n            isEmail = providers[provider].type === \"email\";\n            isSupportingReturn = isCredentials || isEmail;\n            signInUrl = \"\".concat(baseUrl, \"/\").concat(isCredentials ? \"callback\" : \"signin\", \"/\").concat(provider);\n            _signInUrl = \"\".concat(signInUrl).concat(authorizationParams ? \"?\".concat(new URLSearchParams(authorizationParams)) : \"\");\n            _context6.t0 = fetch;\n            _context6.t1 = _signInUrl;\n            _context6.t2 = {\n              \"Content-Type\": \"application/x-www-form-urlencoded\"\n            };\n            _context6.t3 = URLSearchParams;\n            _context6.t4 = _objectSpread;\n            _context6.t5 = _objectSpread({}, options);\n            _context6.t6 = {};\n            _context6.next = 25;\n            return getCsrfToken();\n\n          case 25:\n            _context6.t7 = _context6.sent;\n            _context6.t8 = callbackUrl;\n            _context6.t9 = {\n              csrfToken: _context6.t7,\n              callbackUrl: _context6.t8,\n              json: true\n            };\n            _context6.t10 = (0, _context6.t4)(_context6.t5, _context6.t6, _context6.t9);\n            _context6.t11 = new _context6.t3(_context6.t10);\n            _context6.t12 = {\n              method: \"post\",\n              headers: _context6.t2,\n              body: _context6.t11\n            };\n            _context6.next = 33;\n            return (0, _context6.t0)(_context6.t1, _context6.t12);\n\n          case 33:\n            res = _context6.sent;\n            _context6.next = 36;\n            return res.json();\n\n          case 36:\n            data = _context6.sent;\n\n            if (!(redirect || !isSupportingReturn)) {\n              _context6.next = 42;\n              break;\n            }\n\n            url = (_data$url = data.url) !== null && _data$url !== void 0 ? _data$url : callbackUrl;\n            window.location.href = url;\n            if (url.includes(\"#\")) window.location.reload();\n            return _context6.abrupt(\"return\");\n\n          case 42:\n            error = new URL(data.url).searchParams.get(\"error\");\n\n            if (!res.ok) {\n              _context6.next = 46;\n              break;\n            }\n\n            _context6.next = 46;\n            return __NEXTAUTH._getSession({\n              event: \"storage\"\n            });\n\n          case 46:\n            return _context6.abrupt(\"return\", {\n              error: error,\n              status: res.status,\n              ok: res.ok,\n              url: error ? null : data.url\n            });\n\n          case 47:\n          case \"end\":\n            return _context6.stop();\n        }\n      }\n    }, _callee6);\n  }));\n  return _signIn.apply(this, arguments);\n}\n\nfunction signOut(_x6) {\n  return _signOut.apply(this, arguments);\n}\n\nfunction _signOut() {\n  _signOut = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee7(options) {\n    var _options$redirect;\n\n    var _ref6, _ref6$callbackUrl, callbackUrl, baseUrl, fetchOptions, res, data, _data$url2, url;\n\n    return _regenerator.default.wrap(function _callee7$(_context7) {\n      while (1) {\n        switch (_context7.prev = _context7.next) {\n          case 0:\n            _ref6 = options !== null && options !== void 0 ? options : {}, _ref6$callbackUrl = _ref6.callbackUrl, callbackUrl = _ref6$callbackUrl === void 0 ? window.location.href : _ref6$callbackUrl;\n            baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n            _context7.t0 = {\n              \"Content-Type\": \"application/x-www-form-urlencoded\"\n            };\n            _context7.t1 = URLSearchParams;\n            _context7.next = 6;\n            return getCsrfToken();\n\n          case 6:\n            _context7.t2 = _context7.sent;\n            _context7.t3 = callbackUrl;\n            _context7.t4 = {\n              csrfToken: _context7.t2,\n              callbackUrl: _context7.t3,\n              json: true\n            };\n            _context7.t5 = new _context7.t1(_context7.t4);\n            fetchOptions = {\n              method: \"post\",\n              headers: _context7.t0,\n              body: _context7.t5\n            };\n            _context7.next = 13;\n            return fetch(\"\".concat(baseUrl, \"/signout\"), fetchOptions);\n\n          case 13:\n            res = _context7.sent;\n            _context7.next = 16;\n            return res.json();\n\n          case 16:\n            data = _context7.sent;\n            broadcast.post({\n              event: \"session\",\n              data: {\n                trigger: \"signout\"\n              }\n            });\n\n            if (!((_options$redirect = options === null || options === void 0 ? void 0 : options.redirect) !== null && _options$redirect !== void 0 ? _options$redirect : true)) {\n              _context7.next = 23;\n              break;\n            }\n\n            url = (_data$url2 = data.url) !== null && _data$url2 !== void 0 ? _data$url2 : callbackUrl;\n            window.location.href = url;\n            if (url.includes(\"#\")) window.location.reload();\n            return _context7.abrupt(\"return\");\n\n          case 23:\n            _context7.next = 25;\n            return __NEXTAUTH._getSession({\n              event: \"storage\"\n            });\n\n          case 25:\n            return _context7.abrupt(\"return\", data);\n\n          case 26:\n          case \"end\":\n            return _context7.stop();\n        }\n      }\n    }, _callee7);\n  }));\n  return _signOut.apply(this, arguments);\n}\n\nfunction SessionProvider(props) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n\n  var children = props.children,\n      basePath = props.basePath,\n      refetchInterval = props.refetchInterval,\n      refetchWhenOffline = props.refetchWhenOffline;\n  if (basePath) __NEXTAUTH.basePath = basePath;\n  var hasInitialSession = props.session !== undefined;\n  __NEXTAUTH._lastSync = hasInitialSession ? (0, _utils.now)() : 0;\n\n  var _React$useState3 = React.useState(function () {\n    if (hasInitialSession) __NEXTAUTH._session = props.session;\n    return props.session;\n  }),\n      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n      session = _React$useState4[0],\n      setSession = _React$useState4[1];\n\n  var _React$useState5 = React.useState(!hasInitialSession),\n      _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),\n      loading = _React$useState6[0],\n      setLoading = _React$useState6[1];\n\n  React.useEffect(function () {\n    __NEXTAUTH._getSession = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var _ref4,\n          event,\n          storageEvent,\n          _args = arguments;\n\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _ref4 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, event = _ref4.event;\n              _context.prev = 1;\n              storageEvent = event === \"storage\";\n\n              if (!(storageEvent || __NEXTAUTH._session === undefined)) {\n                _context.next = 10;\n                break;\n              }\n\n              __NEXTAUTH._lastSync = (0, _utils.now)();\n              _context.next = 7;\n              return getSession({\n                broadcast: !storageEvent\n              });\n\n            case 7:\n              __NEXTAUTH._session = _context.sent;\n              setSession(__NEXTAUTH._session);\n              return _context.abrupt(\"return\");\n\n            case 10:\n              if (!(!event || __NEXTAUTH._session === null || (0, _utils.now)() < __NEXTAUTH._lastSync)) {\n                _context.next = 12;\n                break;\n              }\n\n              return _context.abrupt(\"return\");\n\n            case 12:\n              __NEXTAUTH._lastSync = (0, _utils.now)();\n              _context.next = 15;\n              return getSession();\n\n            case 15:\n              __NEXTAUTH._session = _context.sent;\n              setSession(__NEXTAUTH._session);\n              _context.next = 22;\n              break;\n\n            case 19:\n              _context.prev = 19;\n              _context.t0 = _context[\"catch\"](1);\n              logger.error(\"CLIENT_SESSION_ERROR\", _context.t0);\n\n            case 22:\n              _context.prev = 22;\n              setLoading(false);\n              return _context.finish(22);\n\n            case 25:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, null, [[1, 19, 22, 25]]);\n    }));\n\n    __NEXTAUTH._getSession();\n\n    return function () {\n      __NEXTAUTH._lastSync = 0;\n      __NEXTAUTH._session = undefined;\n\n      __NEXTAUTH._getSession = function () {};\n    };\n  }, []);\n  React.useEffect(function () {\n    var unsubscribe = broadcast.receive(function () {\n      return __NEXTAUTH._getSession({\n        event: \"storage\"\n      });\n    });\n    return function () {\n      return unsubscribe();\n    };\n  }, []);\n  React.useEffect(function () {\n    var _props$refetchOnWindo = props.refetchOnWindowFocus,\n        refetchOnWindowFocus = _props$refetchOnWindo === void 0 ? true : _props$refetchOnWindo;\n\n    var visibilityHandler = function visibilityHandler() {\n      if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n        event: \"visibilitychange\"\n      });\n    };\n\n    document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n    return function () {\n      return document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    };\n  }, [props.refetchOnWindowFocus]);\n  var isOnline = useOnline();\n  var shouldRefetch = refetchWhenOffline !== false || isOnline;\n  React.useEffect(function () {\n    if (refetchInterval && shouldRefetch) {\n      var refetchIntervalTimer = setInterval(function () {\n        if (__NEXTAUTH._session) {\n          __NEXTAUTH._getSession({\n            event: \"poll\"\n          });\n        }\n      }, refetchInterval * 1000);\n      return function () {\n        return clearInterval(refetchIntervalTimer);\n      };\n    }\n  }, [refetchInterval, shouldRefetch]);\n  var value = React.useMemo(function () {\n    return {\n      data: session,\n      status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n      update: function update(data) {\n        return (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n          var newSession;\n          return _regenerator.default.wrap(function _callee2$(_context2) {\n            while (1) {\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  if (!(loading || !session)) {\n                    _context2.next = 2;\n                    break;\n                  }\n\n                  return _context2.abrupt(\"return\");\n\n                case 2:\n                  setLoading(true);\n                  _context2.t0 = _utils.fetchData;\n                  _context2.t1 = __NEXTAUTH;\n                  _context2.t2 = logger;\n                  _context2.next = 8;\n                  return getCsrfToken();\n\n                case 8:\n                  _context2.t3 = _context2.sent;\n                  _context2.t4 = data;\n                  _context2.t5 = {\n                    csrfToken: _context2.t3,\n                    data: _context2.t4\n                  };\n                  _context2.t6 = {\n                    body: _context2.t5\n                  };\n                  _context2.t7 = {\n                    req: _context2.t6\n                  };\n                  _context2.next = 15;\n                  return (0, _context2.t0)(\"session\", _context2.t1, _context2.t2, _context2.t7);\n\n                case 15:\n                  newSession = _context2.sent;\n                  setLoading(false);\n\n                  if (newSession) {\n                    setSession(newSession);\n                    broadcast.post({\n                      event: \"session\",\n                      data: {\n                        trigger: \"getSession\"\n                      }\n                    });\n                  }\n\n                  return _context2.abrupt(\"return\", newSession);\n\n                case 19:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }\n          }, _callee2);\n        }))();\n      }\n    };\n  }, [session, loading]);\n  return (0, _jsxRuntime.jsx)(SessionContext.Provider, {\n    value: value,\n    children: children\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next-auth/react/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/next-auth/react/types.js":
/*!************************************************!*\
  !*** ../node_modules/next-auth/react/types.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQtYXV0aC9yZWFjdC90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcQXVnbWVudFxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXHJlYWN0XFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next-auth/react/types.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/next-auth/utils/logger.js":
/*!*************************************************!*\
  !*** ../node_modules/next-auth/utils/logger.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/../node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\n\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/defineProperty.js\"));\n\nvar _errors = __webpack_require__(/*! ../core/errors */ \"(ssr)/../node_modules/next-auth/core/errors.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n\n  if (hasErrorProperty(o)) {\n    var _o$message;\n\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n\n  return o;\n}\n\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\n\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\n\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\n\nvar _default = _logger;\nexports[\"default\"] = _default;\n\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n\n  try {\n    if (typeof window === \"undefined\") {\n      return logger;\n    }\n\n    var clientLogger = {};\n\n    var _loop = function _loop(level) {\n      clientLogger[level] = function (code, metadata) {\n        _logger[level](code, metadata);\n\n        if (level === \"error\") {\n          metadata = formatError(metadata);\n        }\n\n        ;\n        metadata.client = true;\n        var url = \"\".concat(basePath, \"/_log\");\n        var body = new URLSearchParams(_objectSpread({\n          level: level,\n          code: code\n        }, metadata));\n\n        if (navigator.sendBeacon) {\n          return navigator.sendBeacon(url, body);\n        }\n\n        return fetch(url, {\n          method: \"POST\",\n          body: body,\n          keepalive: true\n        });\n      };\n    };\n\n    for (var level in logger) {\n      _loop(level);\n    }\n\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next-auth/utils/logger.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/next-auth/utils/parse-url.js":
/*!****************************************************!*\
  !*** ../node_modules/next-auth/utils/parse-url.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = parseUrl;\n\nfunction parseUrl(url) {\n  var _url2;\n\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQtYXV0aC91dGlscy9wYXJzZS11cmwuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7O0FBRWY7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLHFCQUFxQixJQUFJO0FBQ3pCOztBQUVBOztBQUVBO0FBQ0Esa0JBQWtCLFlBQVksRUFBRSxLQUFLO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcQXVnbWVudFxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXHV0aWxzXFxwYXJzZS11cmwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSBwYXJzZVVybDtcblxuZnVuY3Rpb24gcGFyc2VVcmwodXJsKSB7XG4gIHZhciBfdXJsMjtcblxuICBjb25zdCBkZWZhdWx0VXJsID0gbmV3IFVSTChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGkvYXV0aFwiKTtcblxuICBpZiAodXJsICYmICF1cmwuc3RhcnRzV2l0aChcImh0dHBcIikpIHtcbiAgICB1cmwgPSBgaHR0cHM6Ly8ke3VybH1gO1xuICB9XG5cbiAgY29uc3QgX3VybCA9IG5ldyBVUkwoKF91cmwyID0gdXJsKSAhPT0gbnVsbCAmJiBfdXJsMiAhPT0gdm9pZCAwID8gX3VybDIgOiBkZWZhdWx0VXJsKTtcblxuICBjb25zdCBwYXRoID0gKF91cmwucGF0aG5hbWUgPT09IFwiL1wiID8gZGVmYXVsdFVybC5wYXRobmFtZSA6IF91cmwucGF0aG5hbWUpLnJlcGxhY2UoL1xcLyQvLCBcIlwiKTtcbiAgY29uc3QgYmFzZSA9IGAke191cmwub3JpZ2lufSR7cGF0aH1gO1xuICByZXR1cm4ge1xuICAgIG9yaWdpbjogX3VybC5vcmlnaW4sXG4gICAgaG9zdDogX3VybC5ob3N0LFxuICAgIHBhdGgsXG4gICAgYmFzZSxcbiAgICB0b1N0cmluZzogKCkgPT4gYmFzZVxuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next-auth/utils/parse-url.js\n");

/***/ })

};
;