/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/trpc/[trpc]/route";
exports.ids = ["app/api/trpc/[trpc]/route"];
exports.modules = {

/***/ "(rsc)/./lib/db.ts":
/*!*******************!*\
  !*** ./lib/db.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * 数据库连接配置\n * 使用Prisma客户端进行数据库操作\n */ \n// 全局变量声明，避免在开发环境中重复创建连接\nconst globalForPrisma = globalThis;\n// 创建Prisma客户端实例\nconst db = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query',\n        'error',\n        'warn'\n    ]\n});\n// 在开发环境中将实例保存到全局变量，避免热重载时重复创建连接\nif (true) globalForPrisma.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7OztDQUdDLEdBRTZDO0FBRTlDLHdCQUF3QjtBQUN4QixNQUFNQyxrQkFBa0JDO0FBSXhCLGdCQUFnQjtBQUNULE1BQU1DLEtBQ1hGLGdCQUFnQkcsTUFBTSxJQUN0QixJQUFJSix3REFBWUEsQ0FBQztJQUNmSyxLQUFLO1FBQUM7UUFBUztRQUFTO0tBQU87QUFDakMsR0FBRztBQUVMLGdDQUFnQztBQUNoQyxJQUFJQyxJQUFxQyxFQUFFTCxnQkFBZ0JHLE1BQU0sR0FBR0QiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcQXVnbWVudFxccHJvbXB0LW1hbmFnZXJcXGxpYlxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiDmlbDmja7lupPov57mjqXphY3nva5cbiAqIOS9v+eUqFByaXNtYeWuouaIt+err+i/m+ihjOaVsOaNruW6k+aTjeS9nFxuICovXG5cbmltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuLy8g5YWo5bGA5Y+Y6YeP5aOw5piO77yM6YG/5YWN5Zyo5byA5Y+R546v5aKD5Lit6YeN5aSN5Yib5bu66L+e5o6lXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcbn07XG5cbi8vIOWIm+W7ulByaXNtYeWuouaIt+err+WunuS+i1xuZXhwb3J0IGNvbnN0IGRiID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknLCAnZXJyb3InLCAnd2FybiddLFxuICB9KTtcblxuLy8g5Zyo5byA5Y+R546v5aKD5Lit5bCG5a6e5L6L5L+d5a2Y5Yiw5YWo5bGA5Y+Y6YeP77yM6YG/5YWN54Ot6YeN6L295pe26YeN5aSN5Yib5bu66L+e5o6lXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IGRiO1xuXG4vLyDlr7zlh7rnsbvlnotcbmV4cG9ydCB0eXBlIERhdGFiYXNlID0gdHlwZW9mIGRiO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJkYiIsInByaXNtYSIsImxvZyIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_Cursor_Project_Augment_prompt_manager_src_app_api_trpc_trpc_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/trpc/[trpc]/route.ts */ \"(rsc)/./src/app/api/trpc/[trpc]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/trpc/[trpc]/route\",\n        pathname: \"/api/trpc/[trpc]\",\n        filename: \"route\",\n        bundlePath: \"app/api/trpc/[trpc]/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\app\\\\api\\\\trpc\\\\[trpc]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Cursor_Project_Augment_prompt_manager_src_app_api_trpc_trpc_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/trpc/[trpc]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./server/api/root.ts":
/*!****************************!*\
  !*** ./server/api/root.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appRouter: () => (/* binding */ appRouter)\n/* harmony export */ });\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./server/api/trpc.ts\");\n/* harmony import */ var _server_api_routers_category__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/server/api/routers/category */ \"(rsc)/./server/api/routers/category.ts\");\n/* harmony import */ var _server_api_routers_prompt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/server/api/routers/prompt */ \"(rsc)/./server/api/routers/prompt.ts\");\n/* harmony import */ var _server_api_routers_tag__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ~/server/api/routers/tag */ \"(rsc)/./server/api/routers/tag.ts\");\n/* harmony import */ var _server_api_routers_search__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ~/server/api/routers/search */ \"(rsc)/./server/api/routers/search.ts\");\n/**\n * tRPC 根路由器\n * 汇总所有API路由模块\n */ \n\n\n\n\n/**\n * 应用主路由器\n * 包含所有API端点\n */ const appRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    category: _server_api_routers_category__WEBPACK_IMPORTED_MODULE_1__.categoryRouter,\n    prompt: _server_api_routers_prompt__WEBPACK_IMPORTED_MODULE_2__.promptRouter,\n    tag: _server_api_routers_tag__WEBPACK_IMPORTED_MODULE_3__.tagRouter,\n    search: _server_api_routers_search__WEBPACK_IMPORTED_MODULE_4__.searchRouter\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zZXJ2ZXIvYXBpL3Jvb3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBRW9EO0FBQ1U7QUFDSjtBQUNOO0FBQ007QUFFM0Q7OztDQUdDLEdBQ00sTUFBTUssWUFBWUwsa0VBQWdCQSxDQUFDO0lBQ3hDTSxVQUFVTCx3RUFBY0E7SUFDeEJNLFFBQVFMLG9FQUFZQTtJQUNwQk0sS0FBS0wsOERBQVNBO0lBQ2RNLFFBQVFMLG9FQUFZQTtBQUN0QixHQUFHIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXEF1Z21lbnRcXHByb21wdC1tYW5hZ2VyXFxzZXJ2ZXJcXGFwaVxccm9vdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIHRSUEMg5qC56Lev55Sx5ZmoXG4gKiDmsYfmgLvmiYDmnIlBUEnot6/nlLHmqKHlnZdcbiAqL1xuXG5pbXBvcnQgeyBjcmVhdGVUUlBDUm91dGVyIH0gZnJvbSAnfi9zZXJ2ZXIvYXBpL3RycGMnO1xuaW1wb3J0IHsgY2F0ZWdvcnlSb3V0ZXIgfSBmcm9tICd+L3NlcnZlci9hcGkvcm91dGVycy9jYXRlZ29yeSc7XG5pbXBvcnQgeyBwcm9tcHRSb3V0ZXIgfSBmcm9tICd+L3NlcnZlci9hcGkvcm91dGVycy9wcm9tcHQnO1xuaW1wb3J0IHsgdGFnUm91dGVyIH0gZnJvbSAnfi9zZXJ2ZXIvYXBpL3JvdXRlcnMvdGFnJztcbmltcG9ydCB7IHNlYXJjaFJvdXRlciB9IGZyb20gJ34vc2VydmVyL2FwaS9yb3V0ZXJzL3NlYXJjaCc7XG5cbi8qKlxuICog5bqU55So5Li76Lev55Sx5ZmoXG4gKiDljIXlkKvmiYDmnIlBUEnnq6/ngrlcbiAqL1xuZXhwb3J0IGNvbnN0IGFwcFJvdXRlciA9IGNyZWF0ZVRSUENSb3V0ZXIoe1xuICBjYXRlZ29yeTogY2F0ZWdvcnlSb3V0ZXIsXG4gIHByb21wdDogcHJvbXB0Um91dGVyLFxuICB0YWc6IHRhZ1JvdXRlcixcbiAgc2VhcmNoOiBzZWFyY2hSb3V0ZXIsXG59KTtcblxuLy8g5a+85Ye66Lev55Sx5Zmo57G75Z6L5a6a5LmJ77yM55So5LqO5a6i5oi356uv57G75Z6L5o6o5patXG5leHBvcnQgdHlwZSBBcHBSb3V0ZXIgPSB0eXBlb2YgYXBwUm91dGVyO1xuIl0sIm5hbWVzIjpbImNyZWF0ZVRSUENSb3V0ZXIiLCJjYXRlZ29yeVJvdXRlciIsInByb21wdFJvdXRlciIsInRhZ1JvdXRlciIsInNlYXJjaFJvdXRlciIsImFwcFJvdXRlciIsImNhdGVnb3J5IiwicHJvbXB0IiwidGFnIiwic2VhcmNoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./server/api/root.ts\n");

/***/ }),

/***/ "(rsc)/./server/api/routers/category.ts":
/*!****************************************!*\
  !*** ./server/api/routers/category.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categoryRouter: () => (/* binding */ categoryRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/../node_modules/zod/v4/classic/schemas.js\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./server/api/trpc.ts\");\n/**\n * 分类管理路由器\n * 处理分类的CRUD操作\n */ \n\n// 输入验证schema\nconst createCategorySchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '分类名称不能为空').max(50, '分类名称不能超过50个字符'),\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    color: zod__WEBPACK_IMPORTED_MODULE_1__.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').default('#3B82F6'),\n    icon: zod__WEBPACK_IMPORTED_MODULE_1__.string().default('folder')\n});\nconst updateCategorySchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '分类名称不能为空').max(50, '分类名称不能超过50个字符').optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    color: zod__WEBPACK_IMPORTED_MODULE_1__.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').optional(),\n    icon: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n});\nconst categoryRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    /**\n   * 获取用户所有分类\n   */ getAll: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.query(async ({ ctx })=>{\n        return ctx.db.category.findMany({\n            where: {\n                userId: ctx.session.user.id\n            },\n            include: {\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }),\n    /**\n   * 根据ID获取分类详情\n   */ getById: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).query(async ({ ctx, input })=>{\n        const category = await ctx.db.category.findFirst({\n            where: {\n                id: input.id,\n                userId: ctx.session.user.id\n            },\n            include: {\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            }\n        });\n        if (!category) {\n            throw new Error('分类不存在');\n        }\n        return category;\n    }),\n    /**\n   * 创建新分类\n   */ create: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(createCategorySchema).mutation(async ({ ctx, input })=>{\n        // 检查分类名称是否已存在\n        const existingCategory = await ctx.db.category.findFirst({\n            where: {\n                name: input.name,\n                userId: ctx.session.user.id\n            }\n        });\n        if (existingCategory) {\n            throw new Error('分类名称已存在');\n        }\n        return ctx.db.category.create({\n            data: {\n                ...input,\n                userId: ctx.session.user.id\n            }\n        });\n    }),\n    /**\n   * 更新分类\n   */ update: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(updateCategorySchema).mutation(async ({ ctx, input })=>{\n        const { id, ...updateData } = input;\n        // 验证分类所有权\n        const category = await ctx.db.category.findFirst({\n            where: {\n                id,\n                userId: ctx.session.user.id\n            }\n        });\n        if (!category) {\n            throw new Error('分类不存在');\n        }\n        // 如果更新名称，检查是否重复\n        if (updateData.name && updateData.name !== category.name) {\n            const existingCategory = await ctx.db.category.findFirst({\n                where: {\n                    name: updateData.name,\n                    userId: ctx.session.user.id,\n                    id: {\n                        not: id\n                    }\n                }\n            });\n            if (existingCategory) {\n                throw new Error('分类名称已存在');\n            }\n        }\n        return ctx.db.category.update({\n            where: {\n                id\n            },\n            data: updateData\n        });\n    }),\n    /**\n   * 删除分类\n   */ delete: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 验证分类所有权\n        const category = await ctx.db.category.findFirst({\n            where: {\n                id: input.id,\n                userId: ctx.session.user.id\n            }\n        });\n        if (!category) {\n            throw new Error('分类不存在');\n        }\n        // 删除分类（关联的提示词会自动设置categoryId为null）\n        return ctx.db.category.delete({\n            where: {\n                id: input.id\n            }\n        });\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zZXJ2ZXIvYXBpL3JvdXRlcnMvY2F0ZWdvcnkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7OztDQUdDLEdBRXVCO0FBQ2lEO0FBRXpFLGFBQWE7QUFDYixNQUFNRyx1QkFBdUJILHVDQUFRLENBQUM7SUFDcENLLE1BQU1MLHVDQUFRLEdBQUdPLEdBQUcsQ0FBQyxHQUFHLFlBQVlDLEdBQUcsQ0FBQyxJQUFJO0lBQzVDQyxhQUFhVCx1Q0FBUSxHQUFHVSxRQUFRO0lBQ2hDQyxPQUFPWCx1Q0FBUSxHQUFHWSxLQUFLLENBQUMsbUJBQW1CLFdBQVdDLE9BQU8sQ0FBQztJQUM5REMsTUFBTWQsdUNBQVEsR0FBR2EsT0FBTyxDQUFDO0FBQzNCO0FBRUEsTUFBTUUsdUJBQXVCZix1Q0FBUSxDQUFDO0lBQ3BDZ0IsSUFBSWhCLHVDQUFRO0lBQ1pLLE1BQU1MLHVDQUFRLEdBQUdPLEdBQUcsQ0FBQyxHQUFHLFlBQVlDLEdBQUcsQ0FBQyxJQUFJLGlCQUFpQkUsUUFBUTtJQUNyRUQsYUFBYVQsdUNBQVEsR0FBR1UsUUFBUTtJQUNoQ0MsT0FBT1gsdUNBQVEsR0FBR1ksS0FBSyxDQUFDLG1CQUFtQixXQUFXRixRQUFRO0lBQzlESSxNQUFNZCx1Q0FBUSxHQUFHVSxRQUFRO0FBQzNCO0FBRU8sTUFBTU8saUJBQWlCaEIsa0VBQWdCQSxDQUFDO0lBQzdDOztHQUVDLEdBQ0RpQixRQUFRaEIsZ0VBQWtCQSxDQUFDaUIsS0FBSyxDQUFDLE9BQU8sRUFBRUMsR0FBRyxFQUFFO1FBQzdDLE9BQU9BLElBQUlDLEVBQUUsQ0FBQ0MsUUFBUSxDQUFDQyxRQUFRLENBQUM7WUFDOUJDLE9BQU87Z0JBQ0xDLFFBQVFMLElBQUlNLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDWCxFQUFFO1lBQzdCO1lBQ0FZLFNBQVM7Z0JBQ1BDLFFBQVE7b0JBQ05DLFFBQVE7d0JBQ05DLFNBQVM7b0JBQ1g7Z0JBQ0Y7WUFDRjtZQUNBQyxTQUFTO2dCQUNQQyxXQUFXO1lBQ2I7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDREMsU0FBU2hDLGdFQUFrQkEsQ0FDeEJpQyxLQUFLLENBQUNuQyx1Q0FBUSxDQUFDO1FBQUVnQixJQUFJaEIsdUNBQVE7SUFBRyxJQUNoQ21CLEtBQUssQ0FBQyxPQUFPLEVBQUVDLEdBQUcsRUFBRWUsS0FBSyxFQUFFO1FBQzFCLE1BQU1iLFdBQVcsTUFBTUYsSUFBSUMsRUFBRSxDQUFDQyxRQUFRLENBQUNjLFNBQVMsQ0FBQztZQUMvQ1osT0FBTztnQkFDTFIsSUFBSW1CLE1BQU1uQixFQUFFO2dCQUNaUyxRQUFRTCxJQUFJTSxPQUFPLENBQUNDLElBQUksQ0FBQ1gsRUFBRTtZQUM3QjtZQUNBWSxTQUFTO2dCQUNQQyxRQUFRO29CQUNOQyxRQUFRO3dCQUNOQyxTQUFTO29CQUNYO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLElBQUksQ0FBQ1QsVUFBVTtZQUNiLE1BQU0sSUFBSWUsTUFBTTtRQUNsQjtRQUVBLE9BQU9mO0lBQ1Q7SUFFRjs7R0FFQyxHQUNEZ0IsUUFBUXBDLGdFQUFrQkEsQ0FDdkJpQyxLQUFLLENBQUNoQyxzQkFDTm9DLFFBQVEsQ0FBQyxPQUFPLEVBQUVuQixHQUFHLEVBQUVlLEtBQUssRUFBRTtRQUM3QixjQUFjO1FBQ2QsTUFBTUssbUJBQW1CLE1BQU1wQixJQUFJQyxFQUFFLENBQUNDLFFBQVEsQ0FBQ2MsU0FBUyxDQUFDO1lBQ3ZEWixPQUFPO2dCQUNMbkIsTUFBTThCLE1BQU05QixJQUFJO2dCQUNoQm9CLFFBQVFMLElBQUlNLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDWCxFQUFFO1lBQzdCO1FBQ0Y7UUFFQSxJQUFJd0Isa0JBQWtCO1lBQ3BCLE1BQU0sSUFBSUgsTUFBTTtRQUNsQjtRQUVBLE9BQU9qQixJQUFJQyxFQUFFLENBQUNDLFFBQVEsQ0FBQ2dCLE1BQU0sQ0FBQztZQUM1QkcsTUFBTTtnQkFDSixHQUFHTixLQUFLO2dCQUNSVixRQUFRTCxJQUFJTSxPQUFPLENBQUNDLElBQUksQ0FBQ1gsRUFBRTtZQUM3QjtRQUNGO0lBQ0Y7SUFFRjs7R0FFQyxHQUNEMEIsUUFBUXhDLGdFQUFrQkEsQ0FDdkJpQyxLQUFLLENBQUNwQixzQkFDTndCLFFBQVEsQ0FBQyxPQUFPLEVBQUVuQixHQUFHLEVBQUVlLEtBQUssRUFBRTtRQUM3QixNQUFNLEVBQUVuQixFQUFFLEVBQUUsR0FBRzJCLFlBQVksR0FBR1I7UUFFOUIsVUFBVTtRQUNWLE1BQU1iLFdBQVcsTUFBTUYsSUFBSUMsRUFBRSxDQUFDQyxRQUFRLENBQUNjLFNBQVMsQ0FBQztZQUMvQ1osT0FBTztnQkFDTFI7Z0JBQ0FTLFFBQVFMLElBQUlNLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDWCxFQUFFO1lBQzdCO1FBQ0Y7UUFFQSxJQUFJLENBQUNNLFVBQVU7WUFDYixNQUFNLElBQUllLE1BQU07UUFDbEI7UUFFQSxnQkFBZ0I7UUFDaEIsSUFBSU0sV0FBV3RDLElBQUksSUFBSXNDLFdBQVd0QyxJQUFJLEtBQUtpQixTQUFTakIsSUFBSSxFQUFFO1lBQ3hELE1BQU1tQyxtQkFBbUIsTUFBTXBCLElBQUlDLEVBQUUsQ0FBQ0MsUUFBUSxDQUFDYyxTQUFTLENBQUM7Z0JBQ3ZEWixPQUFPO29CQUNMbkIsTUFBTXNDLFdBQVd0QyxJQUFJO29CQUNyQm9CLFFBQVFMLElBQUlNLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDWCxFQUFFO29CQUMzQkEsSUFBSTt3QkFBRTRCLEtBQUs1QjtvQkFBRztnQkFDaEI7WUFDRjtZQUVBLElBQUl3QixrQkFBa0I7Z0JBQ3BCLE1BQU0sSUFBSUgsTUFBTTtZQUNsQjtRQUNGO1FBRUEsT0FBT2pCLElBQUlDLEVBQUUsQ0FBQ0MsUUFBUSxDQUFDb0IsTUFBTSxDQUFDO1lBQzVCbEIsT0FBTztnQkFBRVI7WUFBRztZQUNaeUIsTUFBTUU7UUFDUjtJQUNGO0lBRUY7O0dBRUMsR0FDREUsUUFBUTNDLGdFQUFrQkEsQ0FDdkJpQyxLQUFLLENBQUNuQyx1Q0FBUSxDQUFDO1FBQUVnQixJQUFJaEIsdUNBQVE7SUFBRyxJQUNoQ3VDLFFBQVEsQ0FBQyxPQUFPLEVBQUVuQixHQUFHLEVBQUVlLEtBQUssRUFBRTtRQUM3QixVQUFVO1FBQ1YsTUFBTWIsV0FBVyxNQUFNRixJQUFJQyxFQUFFLENBQUNDLFFBQVEsQ0FBQ2MsU0FBUyxDQUFDO1lBQy9DWixPQUFPO2dCQUNMUixJQUFJbUIsTUFBTW5CLEVBQUU7Z0JBQ1pTLFFBQVFMLElBQUlNLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDWCxFQUFFO1lBQzdCO1FBQ0Y7UUFFQSxJQUFJLENBQUNNLFVBQVU7WUFDYixNQUFNLElBQUllLE1BQU07UUFDbEI7UUFFQSxtQ0FBbUM7UUFDbkMsT0FBT2pCLElBQUlDLEVBQUUsQ0FBQ0MsUUFBUSxDQUFDdUIsTUFBTSxDQUFDO1lBQzVCckIsT0FBTztnQkFBRVIsSUFBSW1CLE1BQU1uQixFQUFFO1lBQUM7UUFDeEI7SUFDRjtBQUNKLEdBQUciLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcQXVnbWVudFxccHJvbXB0LW1hbmFnZXJcXHNlcnZlclxcYXBpXFxyb3V0ZXJzXFxjYXRlZ29yeS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIOWIhuexu+euoeeQhui3r+eUseWZqFxuICog5aSE55CG5YiG57G755qEQ1JVROaTjeS9nFxuICovXG5cbmltcG9ydCB7IHogfSBmcm9tICd6b2QnO1xuaW1wb3J0IHsgY3JlYXRlVFJQQ1JvdXRlciwgcHJvdGVjdGVkUHJvY2VkdXJlIH0gZnJvbSAnfi9zZXJ2ZXIvYXBpL3RycGMnO1xuXG4vLyDovpPlhaXpqozor4FzY2hlbWFcbmNvbnN0IGNyZWF0ZUNhdGVnb3J5U2NoZW1hID0gei5vYmplY3Qoe1xuICBuYW1lOiB6LnN0cmluZygpLm1pbigxLCAn5YiG57G75ZCN56ew5LiN6IO95Li656m6JykubWF4KDUwLCAn5YiG57G75ZCN56ew5LiN6IO96LaF6L+HNTDkuKrlrZfnrKYnKSxcbiAgZGVzY3JpcHRpb246IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgY29sb3I6IHouc3RyaW5nKCkucmVnZXgoL14jWzAtOUEtRl17Nn0kL2ksICfpopzoibLmoLzlvI/kuI3mraPnoa4nKS5kZWZhdWx0KCcjM0I4MkY2JyksXG4gIGljb246IHouc3RyaW5nKCkuZGVmYXVsdCgnZm9sZGVyJyksXG59KTtcblxuY29uc3QgdXBkYXRlQ2F0ZWdvcnlTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIGlkOiB6LnN0cmluZygpLFxuICBuYW1lOiB6LnN0cmluZygpLm1pbigxLCAn5YiG57G75ZCN56ew5LiN6IO95Li656m6JykubWF4KDUwLCAn5YiG57G75ZCN56ew5LiN6IO96LaF6L+HNTDkuKrlrZfnrKYnKS5vcHRpb25hbCgpLFxuICBkZXNjcmlwdGlvbjogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBjb2xvcjogei5zdHJpbmcoKS5yZWdleCgvXiNbMC05QS1GXXs2fSQvaSwgJ+minOiJsuagvOW8j+S4jeato+ehricpLm9wdGlvbmFsKCksXG4gIGljb246IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbn0pO1xuXG5leHBvcnQgY29uc3QgY2F0ZWdvcnlSb3V0ZXIgPSBjcmVhdGVUUlBDUm91dGVyKHtcbiAgLyoqXG4gICAqIOiOt+WPlueUqOaIt+aJgOacieWIhuexu1xuICAgKi9cbiAgZ2V0QWxsOiBwcm90ZWN0ZWRQcm9jZWR1cmUucXVlcnkoYXN5bmMgKHsgY3R4IH0pID0+IHtcbiAgICByZXR1cm4gY3R4LmRiLmNhdGVnb3J5LmZpbmRNYW55KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHVzZXJJZDogY3R4LnNlc3Npb24udXNlci5pZCxcbiAgICAgIH0sXG4gICAgICBpbmNsdWRlOiB7XG4gICAgICAgIF9jb3VudDoge1xuICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgcHJvbXB0czogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICAgIG9yZGVyQnk6IHtcbiAgICAgICAgY3JlYXRlZEF0OiAnYXNjJyxcbiAgICAgIH0sXG4gICAgfSk7XG4gIH0pLFxuXG4gIC8qKlxuICAgKiDmoLnmja5JROiOt+WPluWIhuexu+ivpuaDhVxuICAgKi9cbiAgZ2V0QnlJZDogcHJvdGVjdGVkUHJvY2VkdXJlXG4gICAgLmlucHV0KHoub2JqZWN0KHsgaWQ6IHouc3RyaW5nKCkgfSkpXG4gICAgLnF1ZXJ5KGFzeW5jICh7IGN0eCwgaW5wdXQgfSkgPT4ge1xuICAgICAgY29uc3QgY2F0ZWdvcnkgPSBhd2FpdCBjdHguZGIuY2F0ZWdvcnkuZmluZEZpcnN0KHtcbiAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICBpZDogaW5wdXQuaWQsXG4gICAgICAgICAgdXNlcklkOiBjdHguc2Vzc2lvbi51c2VyLmlkLFxuICAgICAgICB9LFxuICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgX2NvdW50OiB7XG4gICAgICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICAgICAgcHJvbXB0czogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIWNhdGVnb3J5KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcign5YiG57G75LiN5a2Y5ZyoJyk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBjYXRlZ29yeTtcbiAgICB9KSxcblxuICAvKipcbiAgICog5Yib5bu65paw5YiG57G7XG4gICAqL1xuICBjcmVhdGU6IHByb3RlY3RlZFByb2NlZHVyZVxuICAgIC5pbnB1dChjcmVhdGVDYXRlZ29yeVNjaGVtYSlcbiAgICAubXV0YXRpb24oYXN5bmMgKHsgY3R4LCBpbnB1dCB9KSA9PiB7XG4gICAgICAvLyDmo4Dmn6XliIbnsbvlkI3np7DmmK/lkKblt7LlrZjlnKhcbiAgICAgIGNvbnN0IGV4aXN0aW5nQ2F0ZWdvcnkgPSBhd2FpdCBjdHguZGIuY2F0ZWdvcnkuZmluZEZpcnN0KHtcbiAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICBuYW1lOiBpbnB1dC5uYW1lLFxuICAgICAgICAgIHVzZXJJZDogY3R4LnNlc3Npb24udXNlci5pZCxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoZXhpc3RpbmdDYXRlZ29yeSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+WIhuexu+WQjeensOW3suWtmOWcqCcpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gY3R4LmRiLmNhdGVnb3J5LmNyZWF0ZSh7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAuLi5pbnB1dCxcbiAgICAgICAgICB1c2VySWQ6IGN0eC5zZXNzaW9uLnVzZXIuaWQsXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICB9KSxcblxuICAvKipcbiAgICog5pu05paw5YiG57G7XG4gICAqL1xuICB1cGRhdGU6IHByb3RlY3RlZFByb2NlZHVyZVxuICAgIC5pbnB1dCh1cGRhdGVDYXRlZ29yeVNjaGVtYSlcbiAgICAubXV0YXRpb24oYXN5bmMgKHsgY3R4LCBpbnB1dCB9KSA9PiB7XG4gICAgICBjb25zdCB7IGlkLCAuLi51cGRhdGVEYXRhIH0gPSBpbnB1dDtcblxuICAgICAgLy8g6aqM6K+B5YiG57G75omA5pyJ5p2DXG4gICAgICBjb25zdCBjYXRlZ29yeSA9IGF3YWl0IGN0eC5kYi5jYXRlZ29yeS5maW5kRmlyc3Qoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIGlkLFxuICAgICAgICAgIHVzZXJJZDogY3R4LnNlc3Npb24udXNlci5pZCxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIWNhdGVnb3J5KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcign5YiG57G75LiN5a2Y5ZyoJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIOWmguaenOabtOaWsOWQjeensO+8jOajgOafpeaYr+WQpumHjeWkjVxuICAgICAgaWYgKHVwZGF0ZURhdGEubmFtZSAmJiB1cGRhdGVEYXRhLm5hbWUgIT09IGNhdGVnb3J5Lm5hbWUpIHtcbiAgICAgICAgY29uc3QgZXhpc3RpbmdDYXRlZ29yeSA9IGF3YWl0IGN0eC5kYi5jYXRlZ29yeS5maW5kRmlyc3Qoe1xuICAgICAgICAgIHdoZXJlOiB7XG4gICAgICAgICAgICBuYW1lOiB1cGRhdGVEYXRhLm5hbWUsXG4gICAgICAgICAgICB1c2VySWQ6IGN0eC5zZXNzaW9uLnVzZXIuaWQsXG4gICAgICAgICAgICBpZDogeyBub3Q6IGlkIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgaWYgKGV4aXN0aW5nQ2F0ZWdvcnkpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+WIhuexu+WQjeensOW3suWtmOWcqCcpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBjdHguZGIuY2F0ZWdvcnkudXBkYXRlKHtcbiAgICAgICAgd2hlcmU6IHsgaWQgfSxcbiAgICAgICAgZGF0YTogdXBkYXRlRGF0YSxcbiAgICAgIH0pO1xuICAgIH0pLFxuXG4gIC8qKlxuICAgKiDliKDpmaTliIbnsbtcbiAgICovXG4gIGRlbGV0ZTogcHJvdGVjdGVkUHJvY2VkdXJlXG4gICAgLmlucHV0KHoub2JqZWN0KHsgaWQ6IHouc3RyaW5nKCkgfSkpXG4gICAgLm11dGF0aW9uKGFzeW5jICh7IGN0eCwgaW5wdXQgfSkgPT4ge1xuICAgICAgLy8g6aqM6K+B5YiG57G75omA5pyJ5p2DXG4gICAgICBjb25zdCBjYXRlZ29yeSA9IGF3YWl0IGN0eC5kYi5jYXRlZ29yeS5maW5kRmlyc3Qoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIGlkOiBpbnB1dC5pZCxcbiAgICAgICAgICB1c2VySWQ6IGN0eC5zZXNzaW9uLnVzZXIuaWQsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFjYXRlZ29yeSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+WIhuexu+S4jeWtmOWcqCcpO1xuICAgICAgfVxuXG4gICAgICAvLyDliKDpmaTliIbnsbvvvIjlhbPogZTnmoTmj5DnpLror43kvJroh6rliqjorr7nva5jYXRlZ29yeUlk5Li6bnVsbO+8iVxuICAgICAgcmV0dXJuIGN0eC5kYi5jYXRlZ29yeS5kZWxldGUoe1xuICAgICAgICB3aGVyZTogeyBpZDogaW5wdXQuaWQgfSxcbiAgICAgIH0pO1xuICAgIH0pLFxufSk7XG4iXSwibmFtZXMiOlsieiIsImNyZWF0ZVRSUENSb3V0ZXIiLCJwcm90ZWN0ZWRQcm9jZWR1cmUiLCJjcmVhdGVDYXRlZ29yeVNjaGVtYSIsIm9iamVjdCIsIm5hbWUiLCJzdHJpbmciLCJtaW4iLCJtYXgiLCJkZXNjcmlwdGlvbiIsIm9wdGlvbmFsIiwiY29sb3IiLCJyZWdleCIsImRlZmF1bHQiLCJpY29uIiwidXBkYXRlQ2F0ZWdvcnlTY2hlbWEiLCJpZCIsImNhdGVnb3J5Um91dGVyIiwiZ2V0QWxsIiwicXVlcnkiLCJjdHgiLCJkYiIsImNhdGVnb3J5IiwiZmluZE1hbnkiLCJ3aGVyZSIsInVzZXJJZCIsInNlc3Npb24iLCJ1c2VyIiwiaW5jbHVkZSIsIl9jb3VudCIsInNlbGVjdCIsInByb21wdHMiLCJvcmRlckJ5IiwiY3JlYXRlZEF0IiwiZ2V0QnlJZCIsImlucHV0IiwiZmluZEZpcnN0IiwiRXJyb3IiLCJjcmVhdGUiLCJtdXRhdGlvbiIsImV4aXN0aW5nQ2F0ZWdvcnkiLCJkYXRhIiwidXBkYXRlIiwidXBkYXRlRGF0YSIsIm5vdCIsImRlbGV0ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./server/api/routers/category.ts\n");

/***/ }),

/***/ "(rsc)/./server/api/routers/prompt.ts":
/*!**************************************!*\
  !*** ./server/api/routers/prompt.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   promptRouter: () => (/* binding */ promptRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/../node_modules/zod/v4/classic/schemas.js\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./server/api/trpc.ts\");\n/**\n * 提示词管理路由器\n * 处理提示词的CRUD操作\n */ \n\n// 输入验证schema\nconst createPromptSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '标题不能为空').max(200, '标题不能超过200个字符'),\n    content: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '内容不能为空'),\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()).optional()\n});\nconst updatePromptSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    title: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '标题不能为空').max(200, '标题不能超过200个字符').optional(),\n    content: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '内容不能为空').optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()).optional()\n});\nconst getPromptsSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).default(1),\n    limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(100).default(20),\n    categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    search: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()).optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n        'createdAt',\n        'updatedAt',\n        'usageCount',\n        'title'\n    ]).default('createdAt'),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n        'asc',\n        'desc'\n    ]).default('desc')\n});\nconst promptRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    /**\n   * 获取提示词列表（支持分页、筛选、搜索）\n   */ getAll: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(getPromptsSchema).query(async ({ ctx, input })=>{\n        const { page, limit, categoryId, search, tags, sortBy, sortOrder } = input;\n        const skip = (page - 1) * limit;\n        // 构建查询条件\n        const where = {\n            userId: ctx.session.user.id\n        };\n        if (categoryId) {\n            where.categoryId = categoryId;\n        }\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    content: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        if (tags && tags.length > 0) {\n            where.tags = {\n                some: {\n                    tag: {\n                        name: {\n                            in: tags\n                        }\n                    }\n                }\n            };\n        }\n        // 获取总数\n        const total = await ctx.db.prompt.count({\n            where\n        });\n        // 获取数据\n        const prompts = await ctx.db.prompt.findMany({\n            where,\n            include: {\n                category: true,\n                tags: {\n                    include: {\n                        tag: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        });\n        return {\n            prompts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit)\n            }\n        };\n    }),\n    /**\n   * 根据ID获取提示词详情\n   */ getById: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).query(async ({ ctx, input })=>{\n        const prompt = await ctx.db.prompt.findFirst({\n            where: {\n                id: input.id,\n                userId: ctx.session.user.id\n            },\n            include: {\n                category: true,\n                tags: {\n                    include: {\n                        tag: true\n                    }\n                }\n            }\n        });\n        if (!prompt) {\n            throw new Error('提示词不存在');\n        }\n        return prompt;\n    }),\n    /**\n   * 创建提示词\n   */ create: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(createPromptSchema).mutation(async ({ ctx, input })=>{\n        const { tags, ...promptData } = input;\n        // 验证分类是否存在且属于当前用户\n        if (promptData.categoryId) {\n            const category = await ctx.db.category.findFirst({\n                where: {\n                    id: promptData.categoryId,\n                    userId: ctx.session.user.id\n                }\n            });\n            if (!category) {\n                throw new Error('分类不存在');\n            }\n        }\n        // 创建提示词\n        const prompt = await ctx.db.prompt.create({\n            data: {\n                ...promptData,\n                userId: ctx.session.user.id\n            },\n            include: {\n                category: true,\n                tags: {\n                    include: {\n                        tag: true\n                    }\n                }\n            }\n        });\n        // 处理标签\n        if (tags && tags.length > 0) {\n            for (const tagName of tags){\n                // 查找或创建标签\n                let tag = await ctx.db.tag.findUnique({\n                    where: {\n                        name: tagName\n                    }\n                });\n                if (!tag) {\n                    tag = await ctx.db.tag.create({\n                        data: {\n                            name: tagName\n                        }\n                    });\n                }\n                // 关联标签\n                await ctx.db.promptTag.create({\n                    data: {\n                        promptId: prompt.id,\n                        tagId: tag.id\n                    }\n                });\n            }\n        }\n        return prompt;\n    }),\n    /**\n   * 更新提示词\n   */ update: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(updatePromptSchema).mutation(async ({ ctx, input })=>{\n        const { id, tags, ...updateData } = input;\n        // 验证提示词所有权\n        const prompt = await ctx.db.prompt.findFirst({\n            where: {\n                id,\n                userId: ctx.session.user.id\n            }\n        });\n        if (!prompt) {\n            throw new Error('提示词不存在');\n        }\n        // 验证分类是否存在且属于当前用户\n        if (updateData.categoryId) {\n            const category = await ctx.db.category.findFirst({\n                where: {\n                    id: updateData.categoryId,\n                    userId: ctx.session.user.id\n                }\n            });\n            if (!category) {\n                throw new Error('分类不存在');\n            }\n        }\n        // 更新提示词\n        const updatedPrompt = await ctx.db.prompt.update({\n            where: {\n                id\n            },\n            data: updateData,\n            include: {\n                category: true,\n                tags: {\n                    include: {\n                        tag: true\n                    }\n                }\n            }\n        });\n        // 处理标签更新\n        if (tags !== undefined) {\n            // 删除现有标签关联\n            await ctx.db.promptTag.deleteMany({\n                where: {\n                    promptId: id\n                }\n            });\n            // 添加新标签关联\n            if (tags.length > 0) {\n                for (const tagName of tags){\n                    let tag = await ctx.db.tag.findUnique({\n                        where: {\n                            name: tagName\n                        }\n                    });\n                    if (!tag) {\n                        tag = await ctx.db.tag.create({\n                            data: {\n                                name: tagName\n                            }\n                        });\n                    }\n                    await ctx.db.promptTag.create({\n                        data: {\n                            promptId: id,\n                            tagId: tag.id\n                        }\n                    });\n                }\n            }\n        }\n        return updatedPrompt;\n    }),\n    /**\n   * 删除提示词\n   */ delete: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 验证提示词所有权\n        const prompt = await ctx.db.prompt.findFirst({\n            where: {\n                id: input.id,\n                userId: ctx.session.user.id\n            }\n        });\n        if (!prompt) {\n            throw new Error('提示词不存在');\n        }\n        return ctx.db.prompt.delete({\n            where: {\n                id: input.id\n            }\n        });\n    }),\n    /**\n   * 增加使用次数\n   */ incrementUsage: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 验证提示词所有权\n        const prompt = await ctx.db.prompt.findFirst({\n            where: {\n                id: input.id,\n                userId: ctx.session.user.id\n            }\n        });\n        if (!prompt) {\n            throw new Error('提示词不存在');\n        }\n        return ctx.db.prompt.update({\n            where: {\n                id: input.id\n            },\n            data: {\n                usageCount: {\n                    increment: 1\n                }\n            }\n        });\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./server/api/routers/prompt.ts\n");

/***/ }),

/***/ "(rsc)/./server/api/routers/search.ts":
/*!**************************************!*\
  !*** ./server/api/routers/search.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchRouter: () => (/* binding */ searchRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/../node_modules/zod/v4/classic/schemas.js\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./server/api/trpc.ts\");\n/**\n * 搜索功能路由器\n * 处理搜索和搜索历史相关操作\n */ \n\nconst searchRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    /**\n   * 搜索提示词\n   */ prompts: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        query: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '搜索关键词不能为空'),\n        categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        tags: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()).optional(),\n        page: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).default(1),\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(100).default(20)\n    })).mutation(async ({ ctx, input })=>{\n        const { query, categoryId, tags, page, limit } = input;\n        const skip = (page - 1) * limit;\n        // 保存搜索历史\n        await ctx.db.searchHistory.create({\n            data: {\n                query,\n                userId: ctx.session.user.id\n            }\n        });\n        // 构建搜索条件\n        const where = {\n            userId: ctx.session.user.id,\n            OR: [\n                {\n                    title: {\n                        contains: query,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    content: {\n                        contains: query,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    description: {\n                        contains: query,\n                        mode: 'insensitive'\n                    }\n                }\n            ]\n        };\n        if (categoryId) {\n            where.categoryId = categoryId;\n        }\n        if (tags && tags.length > 0) {\n            where.tags = {\n                some: {\n                    tag: {\n                        name: {\n                            in: tags\n                        }\n                    }\n                }\n            };\n        }\n        // 获取总数\n        const total = await ctx.db.prompt.count({\n            where\n        });\n        // 获取搜索结果\n        const prompts = await ctx.db.prompt.findMany({\n            where,\n            include: {\n                category: true,\n                tags: {\n                    include: {\n                        tag: true\n                    }\n                }\n            },\n            orderBy: [\n                {\n                    usageCount: 'desc'\n                },\n                {\n                    updatedAt: 'desc'\n                }\n            ],\n            skip,\n            take: limit\n        });\n        return {\n            prompts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit)\n            }\n        };\n    }),\n    /**\n   * 获取搜索历史\n   */ getHistory: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(50).default(10)\n    })).query(async ({ ctx, input })=>{\n        return ctx.db.searchHistory.findMany({\n            where: {\n                userId: ctx.session.user.id\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: input.limit,\n            distinct: [\n                'query'\n            ]\n        });\n    }),\n    /**\n   * 清除搜索历史\n   */ clearHistory: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.mutation(async ({ ctx })=>{\n        return ctx.db.searchHistory.deleteMany({\n            where: {\n                userId: ctx.session.user.id\n            }\n        });\n    }),\n    /**\n   * 删除特定搜索历史\n   */ deleteHistory: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 验证搜索历史所有权\n        const searchHistory = await ctx.db.searchHistory.findFirst({\n            where: {\n                id: input.id,\n                userId: ctx.session.user.id\n            }\n        });\n        if (!searchHistory) {\n            throw new Error('搜索历史不存在');\n        }\n        return ctx.db.searchHistory.delete({\n            where: {\n                id: input.id\n            }\n        });\n    }),\n    /**\n   * 获取搜索建议\n   */ getSuggestions: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        query: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1)\n    })).query(async ({ ctx, input })=>{\n        const { query } = input;\n        // 搜索匹配的提示词标题\n        const titleSuggestions = await ctx.db.prompt.findMany({\n            where: {\n                userId: ctx.session.user.id,\n                title: {\n                    contains: query,\n                    mode: 'insensitive'\n                }\n            },\n            select: {\n                title: true\n            },\n            take: 5,\n            orderBy: {\n                usageCount: 'desc'\n            }\n        });\n        // 搜索匹配的标签\n        const tagSuggestions = await ctx.db.tag.findMany({\n            where: {\n                name: {\n                    contains: query,\n                    mode: 'insensitive'\n                },\n                prompts: {\n                    some: {\n                        prompt: {\n                            userId: ctx.session.user.id\n                        }\n                    }\n                }\n            },\n            select: {\n                name: true\n            },\n            take: 5,\n            orderBy: {\n                name: 'asc'\n            }\n        });\n        return {\n            titles: titleSuggestions.map((p)=>p.title),\n            tags: tagSuggestions.map((t)=>t.name)\n        };\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./server/api/routers/search.ts\n");

/***/ }),

/***/ "(rsc)/./server/api/routers/tag.ts":
/*!***********************************!*\
  !*** ./server/api/routers/tag.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tagRouter: () => (/* binding */ tagRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/../node_modules/zod/v4/classic/schemas.js\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./server/api/trpc.ts\");\n/**\n * 标签管理路由器\n * 处理标签的CRUD操作\n */ \n\nconst tagRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    /**\n   * 获取所有标签\n   */ getAll: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.query(async ({ ctx })=>{\n        return ctx.db.tag.findMany({\n            include: {\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            },\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    }),\n    /**\n   * 获取用户使用的标签\n   */ getUserTags: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.query(async ({ ctx })=>{\n        const tags = await ctx.db.tag.findMany({\n            where: {\n                prompts: {\n                    some: {\n                        prompt: {\n                            userId: ctx.session.user.id\n                        }\n                    }\n                }\n            },\n            include: {\n                _count: {\n                    select: {\n                        prompts: {\n                            where: {\n                                prompt: {\n                                    userId: ctx.session.user.id\n                                }\n                            }\n                        }\n                    }\n                }\n            },\n            orderBy: {\n                name: 'asc'\n            }\n        });\n        return tags;\n    }),\n    /**\n   * 搜索标签（用于自动完成）\n   */ search: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        query: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1)\n    })).query(async ({ ctx, input })=>{\n        return ctx.db.tag.findMany({\n            where: {\n                name: {\n                    contains: input.query,\n                    mode: 'insensitive'\n                }\n            },\n            take: 10,\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    }),\n    /**\n   * 创建标签\n   */ create: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        name: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '标签名称不能为空').max(50, '标签名称不能超过50个字符'),\n        color: zod__WEBPACK_IMPORTED_MODULE_1__.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').default('#10B981')\n    })).mutation(async ({ ctx, input })=>{\n        // 检查标签是否已存在\n        const existingTag = await ctx.db.tag.findUnique({\n            where: {\n                name: input.name\n            }\n        });\n        if (existingTag) {\n            return existingTag; // 如果已存在，直接返回\n        }\n        return ctx.db.tag.create({\n            data: input\n        });\n    }),\n    /**\n   * 更新标签\n   */ update: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n        name: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, '标签名称不能为空').max(50, '标签名称不能超过50个字符').optional(),\n        color: zod__WEBPACK_IMPORTED_MODULE_1__.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').optional()\n    })).mutation(async ({ ctx, input })=>{\n        const { id, ...updateData } = input;\n        // 检查标签是否存在\n        const tag = await ctx.db.tag.findUnique({\n            where: {\n                id\n            }\n        });\n        if (!tag) {\n            throw new Error('标签不存在');\n        }\n        // 如果更新名称，检查是否重复\n        if (updateData.name && updateData.name !== tag.name) {\n            const existingTag = await ctx.db.tag.findUnique({\n                where: {\n                    name: updateData.name\n                }\n            });\n            if (existingTag) {\n                throw new Error('标签名称已存在');\n            }\n        }\n        return ctx.db.tag.update({\n            where: {\n                id\n            },\n            data: updateData\n        });\n    }),\n    /**\n   * 删除标签\n   */ delete: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 检查标签是否存在\n        const tag = await ctx.db.tag.findUnique({\n            where: {\n                id: input.id\n            }\n        });\n        if (!tag) {\n            throw new Error('标签不存在');\n        }\n        // 删除标签（关联关系会自动删除）\n        return ctx.db.tag.delete({\n            where: {\n                id: input.id\n            }\n        });\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./server/api/routers/tag.ts\n");

/***/ }),

/***/ "(rsc)/./server/api/trpc.ts":
/*!****************************!*\
  !*** ./server/api/trpc.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTRPCContext: () => (/* binding */ createTRPCContext),\n/* harmony export */   createTRPCRouter: () => (/* binding */ createTRPCRouter),\n/* harmony export */   protectedProcedure: () => (/* binding */ protectedProcedure),\n/* harmony export */   publicProcedure: () => (/* binding */ publicProcedure)\n/* harmony export */ });\n/* harmony import */ var _trpc_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @trpc/server */ \"(rsc)/../node_modules/@trpc/server/dist/initTRPC-IT_6ZYJd.mjs\");\n/* harmony import */ var _trpc_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @trpc/server */ \"(rsc)/../node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs\");\n/* harmony import */ var superjson__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! superjson */ \"(rsc)/../node_modules/superjson/dist/index.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/../node_modules/zod/v4/classic/errors.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/lib/db */ \"(rsc)/./lib/db.ts\");\n/**\n * tRPC 服务端配置\n * 定义tRPC上下文、中间件和过程\n */ \n\n\n// 暂时注释掉NextAuth相关导入\n// import { type Session } from 'next-auth';\n// import { getServerSession } from 'next-auth/next';\n// import { authOptions } from '~/lib/auth';\n\n/**\n * 创建tRPC上下文\n * 包含数据库连接和用户会话信息\n */ const createTRPCContext = async (opts)=>{\n    const { req, res } = opts;\n    // 暂时移除会话获取，直接返回null\n    // const session = await getServerSession(req, res, authOptions);\n    return {\n        db: _lib_db__WEBPACK_IMPORTED_MODULE_1__.db,\n        session: null,\n        req,\n        res\n    };\n};\n/**\n * 初始化tRPC\n */ const t = _trpc_server__WEBPACK_IMPORTED_MODULE_2__.initTRPC.context().create({\n    transformer: superjson__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    errorFormatter ({ shape, error }) {\n        return {\n            ...shape,\n            data: {\n                ...shape.data,\n                zodError: error.cause instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError ? error.cause.flatten() : null\n            }\n        };\n    }\n});\n/**\n * 导出tRPC路由器和过程创建器\n */ const createTRPCRouter = t.router;\n/**\n * 公共过程 - 不需要认证\n */ const publicProcedure = t.procedure;\n/**\n * 认证中间件\n * 确保用户已登录\n */ const enforceUserIsAuthed = t.middleware(({ ctx, next })=>{\n    if (!ctx.session || !ctx.session.user) {\n        throw new _trpc_server__WEBPACK_IMPORTED_MODULE_4__.TRPCError({\n            code: 'UNAUTHORIZED'\n        });\n    }\n    return next({\n        ctx: {\n            // 推断出 `session` 不为 null\n            session: {\n                ...ctx.session,\n                user: ctx.session.user\n            }\n        }\n    });\n});\n/**\n * 受保护的过程 - 需要用户认证\n */ const protectedProcedure = t.procedure.use(enforceUserIsAuthed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./server/api/trpc.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/trpc/[trpc]/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/trpc/[trpc]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_adapters_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @trpc/server/adapters/fetch */ \"(rsc)/../node_modules/@trpc/server/dist/adapters/fetch/index.mjs\");\n/* harmony import */ var _server_api_root__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/root */ \"(rsc)/./server/api/root.ts\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./server/api/trpc.ts\");\n/**\n * tRPC API 路由处理器\n * 处理所有tRPC API请求\n */ \n\n\nconst handler = (req)=>(0,_trpc_server_adapters_fetch__WEBPACK_IMPORTED_MODULE_2__.fetchRequestHandler)({\n        endpoint: '/api/trpc',\n        req,\n        router: _server_api_root__WEBPACK_IMPORTED_MODULE_0__.appRouter,\n        createContext: ()=>(0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_1__.createTRPCContext)({\n                req,\n                res: undefined\n            }),\n        onError:  true ? ({ path, error })=>{\n            console.error(`❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`);\n        } : 0\n    });\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/trpc/[trpc]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@trpc","vendor-chunks/zod","vendor-chunks/superjson","vendor-chunks/is-what","vendor-chunks/copy-anything"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5CAugment%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();