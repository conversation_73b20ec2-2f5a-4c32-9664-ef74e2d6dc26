{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/layout/Navbar.tsx"], "sourcesContent": ["/**\n * 导航栏组件\n * 包含应用标题、搜索框、用户菜单等\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\ninterface NavbarProps {\n  onSearchChange?: (query: string) => void;\n  onMenuToggle?: () => void;\n}\n\nexport default function Navbar({ onSearchChange, onMenuToggle }: NavbarProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearchChange?.(query);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* 左侧：Logo和菜单按钮 */}\n          <div className=\"flex items-center\">\n            <button\n              onClick={onMenuToggle}\n              className=\"md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              <svg\n                className=\"h-6 w-6\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              </svg>\n            </button>\n            <Link href=\"/\" className=\"flex items-center ml-2\">\n              <div className=\"flex-shrink-0\">\n                <svg\n                  className=\"h-8 w-8 text-blue-600\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  />\n                </svg>\n              </div>\n              <div className=\"ml-2\">\n                <h1 className=\"text-xl font-bold text-gray-900\">提示词管理工具</h1>\n              </div>\n            </Link>\n          </div>\n\n          {/* 中间：搜索框 */}\n          <div className=\"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end\">\n            <div className=\"max-w-lg w-full lg:max-w-xs\">\n              <label htmlFor=\"search\" className=\"sr-only\">\n                搜索提示词\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg\n                    className=\"h-5 w-5 text-gray-400\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    viewBox=\"0 0 20 20\"\n                    fill=\"currentColor\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n                <input\n                  id=\"search\"\n                  name=\"search\"\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"搜索提示词...\"\n                  type=\"search\"\n                  value={searchQuery}\n                  onChange={handleSearchChange}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* 右侧：导航和用户菜单 */}\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/stats\"\n              className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              统计\n            </Link>\n            <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\">\n              新建提示词\n            </button>\n            <div className=\"relative\">\n              <button className=\"bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                <span className=\"sr-only\">打开用户菜单</span>\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <svg\n                    className=\"h-5 w-5 text-gray-600\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    viewBox=\"0 0 20 20\"\n                    fill=\"currentColor\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AAHA;;;;AAUe,SAAS,OAAO,EAAE,cAAc,EAAE,YAAY,EAAe;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,QAAO;8CAEP,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAMtD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAAU;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAM;gDACN,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDACC,UAAS;oDACT,GAAE;oDACF,UAAS;;;;;;;;;;;;;;;;sDAIf,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,MAAK;4CACL,OAAO;4CACP,UAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAO,WAAU;0CAA8K;;;;;;0CAGhM,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAM;gDACN,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDACC,UAAS;oDACT,GAAE;oDACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/B", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["/**\n * 侧边栏组件\n * 包含分类列表、快捷操作等\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n  icon: string;\n  count: number;\n}\n\ninterface SidebarProps {\n  isOpen?: boolean;\n  onClose?: () => void;\n  categories?: Category[];\n  selectedCategoryId?: string;\n  onCategorySelect?: (categoryId: string | null) => void;\n  onNewPrompt?: () => void;\n  onManageCategories?: () => void;\n}\n\nexport default function Sidebar({\n  isOpen = true,\n  onClose,\n  categories = [],\n  selectedCategoryId,\n  onCategorySelect,\n  onNewPrompt,\n  onManageCategories,\n}: SidebarProps) {\n  const [showAllCategories, setShowAllCategories] = useState(false);\n\n  // 模拟数据\n  const mockCategories: Category[] = [\n    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️', count: 12 },\n    { id: '2', name: '代码生成', color: '#10B981', icon: '💻', count: 8 },\n    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐', count: 5 },\n    { id: '4', name: '数据分析', color: '#EF4444', icon: '📊', count: 3 },\n    { id: '5', name: '创意设计', color: '#8B5CF6', icon: '🎨', count: 7 },\n  ];\n\n  const displayCategories = categories.length > 0 ? categories : mockCategories;\n  const visibleCategories = showAllCategories ? displayCategories : displayCategories.slice(0, 5);\n\n  return (\n    <>\n      {/* 移动端遮罩 */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* 侧边栏 */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex flex-col h-full\">\n          {/* 侧边栏头部 */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200 md:hidden\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">菜单</h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* 快捷操作 */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <button\n              onClick={onNewPrompt}\n              className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\n            >\n              + 新建提示词\n            </button>\n          </div>\n\n          {/* 导航菜单 */}\n          <nav className=\"flex-1 px-4 py-4 space-y-2\">\n            {/* 全部提示词 */}\n            <button\n              onClick={() => onCategorySelect?.(null)}\n              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                selectedCategoryId === null\n                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                  : 'text-gray-700 hover:bg-gray-50'\n              }`}\n            >\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14-7l2 2-2 2m2-2H9m10 7l2 2-2 2m2-2H9\" />\n              </svg>\n              全部提示词\n              <span className=\"ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">\n                {displayCategories.reduce((sum, cat) => sum + cat.count, 0)}\n              </span>\n            </button>\n\n            {/* 收藏夹 */}\n            <button className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n              收藏夹\n              <span className=\"ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">3</span>\n            </button>\n\n            {/* 最近使用 */}\n            <button className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              最近使用\n            </button>\n\n            {/* 分类标题 */}\n            <div className=\"pt-4 pb-2\">\n              <h3 className=\"text-xs font-semibold text-gray-500 uppercase tracking-wider\">分类</h3>\n            </div>\n\n            {/* 分类列表 */}\n            <div className=\"space-y-1\">\n              {visibleCategories.map((category) => (\n                <button\n                  key={category.id}\n                  onClick={() => onCategorySelect?.(category.id)}\n                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                    selectedCategoryId === category.id\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{category.icon}</span>\n                  <span className=\"flex-1 text-left\">{category.name}</span>\n                  <span className=\"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">\n                    {category.count}\n                  </span>\n                </button>\n              ))}\n            </div>\n\n            {/* 显示更多/收起 */}\n            {displayCategories.length > 5 && (\n              <button\n                onClick={() => setShowAllCategories(!showAllCategories)}\n                className=\"w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors\"\n              >\n                {showAllCategories ? '收起' : `显示更多 (${displayCategories.length - 5})`}\n                <svg\n                  className={`ml-1 h-4 w-4 transform transition-transform ${showAllCategories ? 'rotate-180' : ''}`}\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n            )}\n\n            {/* 管理分类 */}\n            <button\n              onClick={onManageCategories}\n              className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              管理分类\n            </button>\n          </nav>\n\n          {/* 底部信息 */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"text-xs text-gray-500 text-center\">\n              <p>提示词管理工具</p>\n              <p className=\"mt-1\">v1.0.0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAFA;;;AAuBe,SAAS,QAAQ,EAC9B,SAAS,IAAI,EACb,OAAO,EACP,aAAa,EAAE,EACf,kBAAkB,EAClB,gBAAgB,EAChB,WAAW,EACX,kBAAkB,EACL;IACb,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,OAAO;IACP,MAAM,iBAA6B;QACjC;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAG;QACjE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;QAChE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;QAChE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;QAChE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;KACjE;IAED,MAAM,oBAAoB,WAAW,MAAM,GAAG,IAAI,aAAa;IAC/D,MAAM,oBAAoB,oBAAoB,oBAAoB,kBAAkB,KAAK,CAAC,GAAG;IAE7F,qBACE;;YAEG,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,8OAAC;gBACC,WAAW,CAAC,kJAAkJ,EAC5J,SAAS,kBAAkB,qBAC3B;0BAEF,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAW,CAAC,oFAAoF,EAC9F,uBAAuB,OACnB,wDACA,kCACJ;;sDAEF,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;sDAEN,8OAAC;4CAAK,WAAU;sDACb,kBAAkB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,KAAK,EAAE;;;;;;;;;;;;8CAK7D,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;sDAEN,8OAAC;4CAAK,WAAU;sDAAmE;;;;;;;;;;;;8CAIrF,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAKR,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAA+D;;;;;;;;;;;8CAI/E,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;4CAEC,SAAS,IAAM,mBAAmB,SAAS,EAAE;4CAC7C,WAAW,CAAC,oFAAoF,EAC9F,uBAAuB,SAAS,EAAE,GAC9B,wDACA,kCACJ;;8DAEF,8OAAC;oDAAK,WAAU;8DAAgB,SAAS,IAAI;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DACb,SAAS,KAAK;;;;;;;2CAXZ,SAAS,EAAE;;;;;;;;;;gCAkBrB,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;oCACC,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;;wCAET,oBAAoB,OAAO,CAAC,MAAM,EAAE,kBAAkB,MAAM,GAAG,EAAE,CAAC,CAAC;sDACpE,8OAAC;4CACC,WAAW,CAAC,4CAA4C,EAAE,oBAAoB,eAAe,IAAI;4CACjG,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;8CAM3E,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;sCAMV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/layout/MainLayout.tsx"], "sourcesContent": ["/**\n * 主布局组件\n * 整合导航栏、侧边栏和主内容区\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n  onNewPrompt?: () => void;\n  onManageCategories?: () => void;\n}\n\nexport default function MainLayout({ children, onNewPrompt, onManageCategories }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleMenuToggle = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  const handleSidebarClose = () => {\n    setSidebarOpen(false);\n  };\n\n  const handleCategorySelect = (categoryId: string | null) => {\n    setSelectedCategoryId(categoryId);\n    // 在移动端选择分类后关闭侧边栏\n    if (window.innerWidth < 768) {\n      setSidebarOpen(false);\n    }\n  };\n\n  const handleSearchChange = (query: string) => {\n    setSearchQuery(query);\n  };\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-50\">\n      {/* 侧边栏 */}\n      <Sidebar\n        isOpen={sidebarOpen}\n        onClose={handleSidebarClose}\n        selectedCategoryId={selectedCategoryId}\n        onCategorySelect={handleCategorySelect}\n        onNewPrompt={onNewPrompt}\n        onManageCategories={onManageCategories}\n      />\n\n      {/* 主内容区 */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* 导航栏 */}\n        <Navbar\n          onMenuToggle={handleMenuToggle}\n          onSearchChange={handleSearchChange}\n        />\n\n        {/* 主内容 */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AACA;AAJA;;;;;AAYe,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAmB;IAC/F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB;QACvB,eAAe,CAAC;IAClB;IAEA,MAAM,qBAAqB;QACzB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,iBAAiB;QACjB,IAAI,OAAO,UAAU,GAAG,KAAK;YAC3B,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,uIAAA,CAAA,UAAO;gBACN,QAAQ;gBACR,SAAS;gBACT,oBAAoB;gBACpB,kBAAkB;gBAClB,aAAa;gBACb,oBAAoB;;;;;;0BAItB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,sIAAA,CAAA,UAAM;wBACL,cAAc;wBACd,gBAAgB;;;;;;kCAIlB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/SearchBar.tsx"], "sourcesContent": ["/**\n * 搜索栏组件\n * 包含搜索输入框、筛选器和排序选项\n */\n\n'use client';\n\nimport { useState, useRef, useEffect } from 'react';\n\ninterface Tag {\n  id: string;\n  name: string;\n  color: string;\n}\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n  icon: string;\n}\n\ninterface SearchFilters {\n  query: string;\n  categoryId: string | null;\n  tags: string[];\n  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title';\n  sortOrder: 'asc' | 'desc';\n}\n\ninterface SearchBarProps {\n  filters: SearchFilters;\n  categories?: Category[];\n  tags?: Tag[];\n  onFiltersChange: (filters: SearchFilters) => void;\n  placeholder?: string;\n}\n\nexport default function SearchBar({\n  filters,\n  categories = [],\n  tags = [],\n  onFiltersChange,\n  placeholder = '搜索提示词...',\n}: SearchBarProps) {\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\n  const [tagSearchQuery, setTagSearchQuery] = useState('');\n  const filterRef = useRef<HTMLDivElement>(null);\n\n  // 模拟数据\n  const mockCategories: Category[] = [\n    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },\n    { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },\n    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },\n  ];\n\n  const mockTags: Tag[] = [\n    { id: '1', name: 'AI', color: '#3B82F6' },\n    { id: '2', name: '编程', color: '#10B981' },\n    { id: '3', name: '创意', color: '#F59E0B' },\n    { id: '4', name: '商务', color: '#EF4444' },\n  ];\n\n  const displayCategories = categories.length > 0 ? categories : mockCategories;\n  const displayTags = tags.length > 0 ? tags : mockTags;\n\n  // 点击外部关闭筛选器\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {\n        setIsFilterOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onFiltersChange({\n      ...filters,\n      query: e.target.value,\n    });\n  };\n\n  const handleCategoryChange = (categoryId: string | null) => {\n    onFiltersChange({\n      ...filters,\n      categoryId,\n    });\n  };\n\n  const handleTagToggle = (tagId: string) => {\n    const newTags = filters.tags.includes(tagId)\n      ? filters.tags.filter(id => id !== tagId)\n      : [...filters.tags, tagId];\n    \n    onFiltersChange({\n      ...filters,\n      tags: newTags,\n    });\n  };\n\n  const handleSortChange = (sortBy: SearchFilters['sortBy'], sortOrder: SearchFilters['sortOrder']) => {\n    onFiltersChange({\n      ...filters,\n      sortBy,\n      sortOrder,\n    });\n  };\n\n  const clearFilters = () => {\n    onFiltersChange({\n      query: '',\n      categoryId: null,\n      tags: [],\n      sortBy: 'updatedAt',\n      sortOrder: 'desc',\n    });\n  };\n\n  const filteredTags = displayTags.filter(tag =>\n    tag.name.toLowerCase().includes(tagSearchQuery.toLowerCase())\n  );\n\n  const hasActiveFilters = filters.categoryId || filters.tags.length > 0;\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n      <div className=\"flex flex-col space-y-4\">\n        {/* 搜索输入框 */}\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n            <input\n              type=\"text\"\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder={placeholder}\n              value={filters.query}\n              onChange={handleQueryChange}\n            />\n          </div>\n\n          {/* 筛选按钮 */}\n          <div className=\"relative\" ref={filterRef}>\n            <button\n              onClick={() => setIsFilterOpen(!isFilterOpen)}\n              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${\n                hasActiveFilters\n                  ? 'bg-blue-50 text-blue-700 border-blue-300'\n                  : 'bg-white text-gray-700 hover:bg-gray-50'\n              }`}\n            >\n              <svg className=\"mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z\" />\n              </svg>\n              筛选\n              {hasActiveFilters && (\n                <span className=\"ml-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full\">\n                  {(filters.categoryId ? 1 : 0) + filters.tags.length}\n                </span>\n              )}\n            </button>\n\n            {/* 筛选面板 */}\n            {isFilterOpen && (\n              <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-20 border border-gray-200\">\n                <div className=\"p-4\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">筛选选项</h3>\n                    {hasActiveFilters && (\n                      <button\n                        onClick={clearFilters}\n                        className=\"text-sm text-blue-600 hover:text-blue-800\"\n                      >\n                        清除筛选\n                      </button>\n                    )}\n                  </div>\n\n                  {/* 分类筛选 */}\n                  <div className=\"mb-4\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">分类</label>\n                    <div className=\"space-y-2\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name=\"category\"\n                          checked={filters.categoryId === null}\n                          onChange={() => handleCategoryChange(null)}\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">全部分类</span>\n                      </label>\n                      {displayCategories.map((category) => (\n                        <label key={category.id} className=\"flex items-center\">\n                          <input\n                            type=\"radio\"\n                            name=\"category\"\n                            checked={filters.categoryId === category.id}\n                            onChange={() => handleCategoryChange(category.id)}\n                            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                          />\n                          <span className=\"ml-2 flex items-center text-sm text-gray-700\">\n                            <span className=\"mr-1\">{category.icon}</span>\n                            {category.name}\n                          </span>\n                        </label>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* 标签筛选 */}\n                  <div className=\"mb-4\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">标签</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"搜索标签...\"\n                      value={tagSearchQuery}\n                      onChange={(e) => setTagSearchQuery(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 mb-2\"\n                    />\n                    <div className=\"max-h-32 overflow-y-auto space-y-2\">\n                      {filteredTags.map((tag) => (\n                        <label key={tag.id} className=\"flex items-center\">\n                          <input\n                            type=\"checkbox\"\n                            checked={filters.tags.includes(tag.id)}\n                            onChange={() => handleTagToggle(tag.id)}\n                            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                          />\n                          <span\n                            className=\"ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white\"\n                            style={{ backgroundColor: tag.color }}\n                          >\n                            {tag.name}\n                          </span>\n                        </label>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* 排序选项 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">排序</label>\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <select\n                        value={filters.sortBy}\n                        onChange={(e) => handleSortChange(e.target.value as SearchFilters['sortBy'], filters.sortOrder)}\n                        className=\"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                      >\n                        <option value=\"updatedAt\">更新时间</option>\n                        <option value=\"createdAt\">创建时间</option>\n                        <option value=\"usageCount\">使用次数</option>\n                        <option value=\"title\">标题</option>\n                      </select>\n                      <select\n                        value={filters.sortOrder}\n                        onChange={(e) => handleSortChange(filters.sortBy, e.target.value as SearchFilters['sortOrder'])}\n                        className=\"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                      >\n                        <option value=\"desc\">降序</option>\n                        <option value=\"asc\">升序</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 活跃筛选器显示 */}\n        {hasActiveFilters && (\n          <div className=\"flex flex-wrap items-center gap-2\">\n            <span className=\"text-sm text-gray-500\">筛选条件:</span>\n            {filters.categoryId && (\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\">\n                {displayCategories.find(c => c.id === filters.categoryId)?.name}\n                <button\n                  onClick={() => handleCategoryChange(null)}\n                  className=\"ml-2 inline-flex items-center justify-center w-4 h-4 text-blue-400 hover:text-blue-600\"\n                >\n                  <svg className=\"w-3 h-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n              </span>\n            )}\n            {filters.tags.map((tagId) => {\n              const tag = displayTags.find(t => t.id === tagId);\n              return tag ? (\n                <span\n                  key={tagId}\n                  className=\"inline-flex items-center px-3 py-1 rounded-full text-sm text-white\"\n                  style={{ backgroundColor: tag.color }}\n                >\n                  {tag.name}\n                  <button\n                    onClick={() => handleTagToggle(tagId)}\n                    className=\"ml-2 inline-flex items-center justify-center w-4 h-4 text-white hover:text-gray-200\"\n                  >\n                    <svg className=\"w-3 h-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </button>\n                </span>\n              ) : null;\n            })}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAFA;;;AAiCe,SAAS,UAAU,EAChC,OAAO,EACP,aAAa,EAAE,EACf,OAAO,EAAE,EACT,eAAe,EACf,cAAc,UAAU,EACT;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,OAAO;IACP,MAAM,iBAA6B;QACjC;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;QACtD;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;QACtD;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;KACvD;IAED,MAAM,WAAkB;QACtB;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;QACxC;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;QACxC;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;QACxC;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;KACzC;IAED,MAAM,oBAAoB,WAAW,MAAM,GAAG,IAAI,aAAa;IAC/D,MAAM,cAAc,KAAK,MAAM,GAAG,IAAI,OAAO;IAE7C,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC1E,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;YACd,GAAG,OAAO;YACV,OAAO,EAAE,MAAM,CAAC,KAAK;QACvB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;YACd,GAAG,OAAO;YACV;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAClC,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,SACjC;eAAI,QAAQ,IAAI;YAAE;SAAM;QAE5B,gBAAgB;YACd,GAAG,OAAO;YACV,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAiC;QACzD,gBAAgB;YACd,GAAG,OAAO;YACV;YACA;QACF;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;YACd,OAAO;YACP,YAAY;YACZ,MAAM,EAAE;YACR,QAAQ;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,YAAY,MAAM,CAAC,CAAA,MACtC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW;IAG5D,MAAM,mBAAmB,QAAQ,UAAU,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG;IAErE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,aAAa;oCACb,OAAO,QAAQ,KAAK;oCACpB,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;4BAAW,KAAK;;8CAC7B,8OAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAW,CAAC,2KAA2K,EACrL,mBACI,6CACA,2CACJ;;sDAEF,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;wCAEL,kCACC,8OAAC;4CAAK,WAAU;sDACb,CAAC,QAAQ,UAAU,GAAG,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,MAAM;;;;;;;;;;;;gCAMxD,8BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;oDACjD,kCACC,8OAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;0DAOL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,SAAS,QAAQ,UAAU,KAAK;wEAChC,UAAU,IAAM,qBAAqB;wEACrC,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;4DAE9C,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oEAAwB,WAAU;;sFACjC,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,SAAS,QAAQ,UAAU,KAAK,SAAS,EAAE;4EAC3C,UAAU,IAAM,qBAAqB,SAAS,EAAE;4EAChD,WAAU;;;;;;sFAEZ,8OAAC;4EAAK,WAAU;;8FACd,8OAAC;oFAAK,WAAU;8FAAQ,SAAS,IAAI;;;;;;gFACpC,SAAS,IAAI;;;;;;;;mEAVN,SAAS,EAAE;;;;;;;;;;;;;;;;;0DAkB7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;gEAAmB,WAAU;;kFAC5B,8OAAC;wEACC,MAAK;wEACL,SAAS,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;wEACrC,UAAU,IAAM,gBAAgB,IAAI,EAAE;wEACtC,WAAU;;;;;;kFAEZ,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,IAAI,KAAK;wEAAC;kFAEnC,IAAI,IAAI;;;;;;;+DAXD,IAAI,EAAE;;;;;;;;;;;;;;;;0DAmBxB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,OAAO,QAAQ,MAAM;gEACrB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK,EAA6B,QAAQ,SAAS;gEAC9F,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;0EAExB,8OAAC;gEACC,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,iBAAiB,QAAQ,MAAM,EAAE,EAAE,MAAM,CAAC,KAAK;gEAChE,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,8OAAC;wEAAO,OAAM;kFAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWnC,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;wBACvC,QAAQ,UAAU,kBACjB,8OAAC;4BAAK,WAAU;;gCACb,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,UAAU,GAAG;8CAC3D,8OAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;wBAKhP,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;4BACjB,MAAM,MAAM,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4BAC3C,OAAO,oBACL,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,IAAI,KAAK;gCAAC;;oCAEnC,IAAI,IAAI;kDACT,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqM,UAAS;;;;;;;;;;;;;;;;;+BAVxO;;;;uCAcL;wBACN;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/PromptCard.tsx"], "sourcesContent": ["/**\n * 提示词卡片组件\n * 显示提示词的基本信息和操作按钮\n */\n\n'use client';\n\nimport { useState } from 'react';\n\ninterface Tag {\n  id: string;\n  name: string;\n  color: string;\n}\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n  icon: string;\n}\n\ninterface Prompt {\n  id: string;\n  title: string;\n  content: string;\n  description?: string;\n  category?: Category;\n  tags: Tag[];\n  usageCount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface PromptCardProps {\n  prompt: Prompt;\n  onEdit?: (prompt: Prompt) => void;\n  onDelete?: (promptId: string) => void;\n  onCopy?: (content: string) => void;\n  onView?: (prompt: Prompt) => void;\n}\n\nexport default function PromptCard({\n  prompt,\n  onEdit,\n  onDelete,\n  onCopy,\n  onView,\n}: PromptCardProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isCopied, setIsCopied] = useState(false);\n\n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(prompt.content);\n      onCopy?.(prompt.content);\n      setIsCopied(true);\n      setTimeout(() => setIsCopied(false), 2000);\n    } catch (error) {\n      console.error('复制失败:', error);\n    }\n  };\n\n  const handleMenuToggle = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const handleEdit = () => {\n    onEdit?.(prompt);\n    setIsMenuOpen(false);\n  };\n\n  const handleDelete = () => {\n    if (window.confirm('确定要删除这个提示词吗？')) {\n      onDelete?.(prompt.id);\n    }\n    setIsMenuOpen(false);\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const truncateContent = (content: string, maxLength: number = 150) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength) + '...';\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      {/* 卡片头部 */}\n      <div className=\"p-4 border-b border-gray-100\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1 min-w-0\">\n            <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n              {prompt.title}\n            </h3>\n            {prompt.description && (\n              <p className=\"mt-1 text-sm text-gray-600 line-clamp-2\">\n                {prompt.description}\n              </p>\n            )}\n          </div>\n          \n          {/* 操作菜单 */}\n          <div className=\"relative ml-4\">\n            <button\n              onClick={handleMenuToggle}\n              className=\"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z\" />\n              </svg>\n            </button>\n            \n            {isMenuOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200\">\n                <div className=\"py-1\">\n                  <button\n                    onClick={() => onView?.(prompt)}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                  >\n                    查看详情\n                  </button>\n                  <button\n                    onClick={handleEdit}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                  >\n                    编辑\n                  </button>\n                  <button\n                    onClick={handleCopy}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                  >\n                    复制内容\n                  </button>\n                  <button\n                    onClick={handleDelete}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                  >\n                    删除\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 分类和标签 */}\n        <div className=\"mt-3 flex items-center space-x-2\">\n          {prompt.category && (\n            <span\n              className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white\"\n              style={{ backgroundColor: prompt.category.color }}\n            >\n              <span className=\"mr-1\">{prompt.category.icon}</span>\n              {prompt.category.name}\n            </span>\n          )}\n          \n          {prompt.tags.slice(0, 3).map((tag) => (\n            <span\n              key={tag.id}\n              className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white\"\n              style={{ backgroundColor: tag.color }}\n            >\n              {tag.name}\n            </span>\n          ))}\n          \n          {prompt.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600\">\n              +{prompt.tags.length - 3}\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* 卡片内容 */}\n      <div className=\"p-4\">\n        <div className=\"text-sm text-gray-700 whitespace-pre-wrap\">\n          {truncateContent(prompt.content)}\n        </div>\n      </div>\n\n      {/* 卡片底部 */}\n      <div className=\"px-4 py-3 bg-gray-50 border-t border-gray-100 flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n          <span>使用 {prompt.usageCount} 次</span>\n          <span>更新于 {formatDate(prompt.updatedAt)}</span>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={handleCopy}\n            className={`inline-flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors ${\n              isCopied\n                ? 'bg-green-100 text-green-800'\n                : 'bg-blue-100 text-blue-800 hover:bg-blue-200'\n            }`}\n          >\n            {isCopied ? (\n              <>\n                <svg className=\"mr-1 h-3 w-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n                已复制\n              </>\n            ) : (\n              <>\n                <svg className=\"mr-1 h-3 w-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n                </svg>\n                复制\n              </>\n            )}\n          </button>\n          \n          <button\n            onClick={() => onView?.(prompt)}\n            className=\"inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors\"\n          >\n            查看\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAFA;;;AAqCe,SAAS,WAAW,EACjC,MAAM,EACN,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACU;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;YAClD,SAAS,OAAO,OAAO;YACvB,YAAY;YACZ,WAAW,IAAM,YAAY,QAAQ;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc,CAAC;IACjB;IAEA,MAAM,aAAa;QACjB,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,iBAAiB;YAClC,WAAW,OAAO,EAAE;QACtB;QACA,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,kBAAkB,CAAC,SAAiB,YAAoB,GAAG;QAC/D,IAAI,QAAQ,MAAM,IAAI,WAAW,OAAO;QACxC,OAAO,QAAQ,SAAS,CAAC,GAAG,aAAa;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,OAAO,KAAK;;;;;;oCAEd,OAAO,WAAW,kBACjB,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;;;;;;;0CAMzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;oCAIX,4BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,SAAS;oDACxB,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,QAAQ,kBACd,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK;gCAAC;;kDAEhD,8OAAC;wCAAK,WAAU;kDAAQ,OAAO,QAAQ,CAAC,IAAI;;;;;;oCAC3C,OAAO,QAAQ,CAAC,IAAI;;;;;;;4BAIxB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC5B,8OAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,IAAI,KAAK;oCAAC;8CAEnC,IAAI,IAAI;mCAJJ,IAAI,EAAE;;;;;4BAQd,OAAO,IAAI,CAAC,MAAM,GAAG,mBACpB,8OAAC;gCAAK,WAAU;;oCAAgG;oCAC5G,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,OAAO,OAAO;;;;;;;;;;;0BAKnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAK;oCAAI,OAAO,UAAU;oCAAC;;;;;;;0CAC5B,8OAAC;;oCAAK;oCAAK,WAAW,OAAO,SAAS;;;;;;;;;;;;;kCAGxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAC,oFAAoF,EAC9F,WACI,gCACA,+CACJ;0CAED,yBACC;;sDACE,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAe,SAAQ;sDACxD,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;wCACrJ;;iEAIR;;sDACE,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;0CAMZ,8OAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/Modal.tsx"], "sourcesContent": ["/**\n * 基础模态框组件\n * 提供可重用的模态框功能\n */\n\n'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n  showCloseButton?: boolean;\n  closeOnOverlayClick?: boolean;\n  closeOnEscape?: boolean;\n}\n\nexport default function Modal({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n  closeOnEscape = true,\n}: ModalProps) {\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  // 处理ESC键关闭\n  useEffect(() => {\n    if (!closeOnEscape) return;\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen, onClose, closeOnEscape]);\n\n  // 处理body滚动锁定\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  // 处理点击遮罩关闭\n  const handleOverlayClick = (event: React.MouseEvent) => {\n    if (closeOnOverlayClick && event.target === event.currentTarget) {\n      onClose();\n    }\n  };\n\n  // 获取尺寸类名\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'max-w-md';\n      case 'md':\n        return 'max-w-lg';\n      case 'lg':\n        return 'max-w-2xl';\n      case 'xl':\n        return 'max-w-4xl';\n      case 'full':\n        return 'max-w-full mx-4';\n      default:\n        return 'max-w-lg';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      {/* 遮罩层 */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={handleOverlayClick}\n      />\n\n      {/* 模态框容器 */}\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        <div\n          ref={modalRef}\n          className={`relative w-full ${getSizeClasses()} transform overflow-hidden rounded-lg bg-white shadow-xl transition-all`}\n        >\n          {/* 头部 */}\n          {(title || showCloseButton) && (\n            <div className=\"flex items-center justify-between px-6 py-4 border-b border-gray-200\">\n              {title && (\n                <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n              )}\n              {showCloseButton && (\n                <button\n                  onClick={onClose}\n                  className=\"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <span className=\"sr-only\">关闭</span>\n                  <svg\n                    className=\"h-6 w-6\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke=\"currentColor\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M6 18L18 6M6 6l12 12\"\n                    />\n                  </svg>\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* 内容 */}\n          <div className=\"px-6 py-4\">{children}</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAFA;;;AAee,SAAS,MAAM,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EAC1B,gBAAgB,IAAI,EACT;IACX,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;gBACpC;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAQ;QAAS;KAAc;IAEnC,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,MAAM,MAAM,KAAK,MAAM,aAAa,EAAE;YAC/D;QACF;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAK;oBACL,WAAW,CAAC,gBAAgB,EAAE,iBAAiB,uEAAuE,CAAC;;wBAGtH,CAAC,SAAS,eAAe,mBACxB,8OAAC;4BAAI,WAAU;;gCACZ,uBACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;gCAEtD,iCACC,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;sCASd,8OAAC;4BAAI,WAAU;sCAAa;;;;;;;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 1991, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/PromptDetailModal.tsx"], "sourcesContent": ["/**\n * 提示词详情模态框组件\n * 显示提示词的完整信息\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Modal from './Modal';\n\ninterface Tag {\n  id: string;\n  name: string;\n  color: string;\n}\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n  icon: string;\n}\n\ninterface Prompt {\n  id: string;\n  title: string;\n  content: string;\n  description?: string;\n  category?: Category;\n  tags: Tag[];\n  usageCount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface PromptDetailModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  prompt: Prompt | null;\n  onEdit?: (prompt: Prompt) => void;\n  onDelete?: (promptId: string) => void;\n  onCopy?: (content: string) => void;\n}\n\nexport default function PromptDetailModal({\n  isOpen,\n  onClose,\n  prompt,\n  onEdit,\n  onDelete,\n  onCopy,\n}: PromptDetailModalProps) {\n  const [isCopied, setIsCopied] = useState(false);\n\n  if (!prompt) return null;\n\n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(prompt.content);\n      onCopy?.(prompt.content);\n      setIsCopied(true);\n      setTimeout(() => setIsCopied(false), 2000);\n    } catch (error) {\n      console.error('复制失败:', error);\n    }\n  };\n\n  const handleEdit = () => {\n    onEdit?.(prompt);\n    onClose();\n  };\n\n  const handleDelete = () => {\n    if (window.confirm('确定要删除这个提示词吗？')) {\n      onDelete?.(prompt.id);\n      onClose();\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"提示词详情\"\n      size=\"lg\"\n    >\n      <div className=\"space-y-6\">\n        {/* 标题和描述 */}\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 mb-2\">{prompt.title}</h2>\n          {prompt.description && (\n            <p className=\"text-gray-600\">{prompt.description}</p>\n          )}\n        </div>\n\n        {/* 分类和标签 */}\n        <div className=\"flex flex-wrap items-center gap-2\">\n          {prompt.category && (\n            <span\n              className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white\"\n              style={{ backgroundColor: prompt.category.color }}\n            >\n              <span className=\"mr-1\">{prompt.category.icon}</span>\n              {prompt.category.name}\n            </span>\n          )}\n          \n          {prompt.tags.map((tag) => (\n            <span\n              key={tag.id}\n              className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white\"\n              style={{ backgroundColor: tag.color }}\n            >\n              {tag.name}\n            </span>\n          ))}\n        </div>\n\n        {/* 提示词内容 */}\n        <div>\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">提示词内容</h3>\n            <button\n              onClick={handleCopy}\n              className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                isCopied\n                  ? 'bg-green-100 text-green-800'\n                  : 'bg-blue-100 text-blue-800 hover:bg-blue-200'\n              }`}\n            >\n              {isCopied ? (\n                <>\n                  <svg className=\"mr-2 h-4 w-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  已复制\n                </>\n              ) : (\n                <>\n                  <svg className=\"mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n                  </svg>\n                  复制内容\n                </>\n              )}\n            </button>\n          </div>\n          <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\n            <pre className=\"whitespace-pre-wrap text-sm text-gray-800 font-mono\">\n              {prompt.content}\n            </pre>\n          </div>\n        </div>\n\n        {/* 统计信息 */}\n        <div className=\"grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg\">\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">使用次数</dt>\n            <dd className=\"mt-1 text-lg font-semibold text-gray-900\">{prompt.usageCount}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">创建时间</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{formatDate(prompt.createdAt)}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">最后更新</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{formatDate(prompt.updatedAt)}</dd>\n          </div>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            关闭\n          </button>\n          <button\n            onClick={handleDelete}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n          >\n            删除\n          </button>\n          <button\n            onClick={handleEdit}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            编辑\n          </button>\n        </div>\n      </div>\n    </Modal>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AAHA;;;;AAuCe,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,QAAQ,EACR,MAAM,EACiB;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;YAClD,SAAS,OAAO,OAAO;YACvB,YAAY;YACZ,WAAW,IAAM,YAAY,QAAQ;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,aAAa;QACjB,SAAS;QACT;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,iBAAiB;YAClC,WAAW,OAAO,EAAE;YACpB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,cAAc,CAAC,SAAS;YAClC,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAwC,OAAO,KAAK;;;;;;wBACjE,OAAO,WAAW,kBACjB,8OAAC;4BAAE,WAAU;sCAAiB,OAAO,WAAW;;;;;;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;;wBACZ,OAAO,QAAQ,kBACd,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK;4BAAC;;8CAEhD,8OAAC;oCAAK,WAAU;8CAAQ,OAAO,QAAQ,CAAC,IAAI;;;;;;gCAC3C,OAAO,QAAQ,CAAC,IAAI;;;;;;;wBAIxB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,IAAI,KAAK;gCAAC;0CAEnC,IAAI,IAAI;+BAJJ,IAAI,EAAE;;;;;;;;;;;8BAUjB,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,oFAAoF,EAC9F,WACI,gCACA,+CACJ;8CAED,yBACC;;0DACE,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAe,SAAQ;0DACxD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;4CACrJ;;qEAIR;;0DACE,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO;;;;;;;;;;;;;;;;;8BAMrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAA4C,OAAO,UAAU;;;;;;;;;;;;sCAE7E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAA8B,WAAW,OAAO,SAAS;;;;;;;;;;;;sCAEzE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAA8B,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2338, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/PromptEditModal.tsx"], "sourcesContent": ["/**\n * 提示词编辑模态框组件\n * 用于创建和编辑提示词\n */\n\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport Modal from './Modal';\n\ninterface Tag {\n  id: string;\n  name: string;\n  color: string;\n}\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n  icon: string;\n}\n\ninterface Prompt {\n  id: string;\n  title: string;\n  content: string;\n  description?: string;\n  category?: Category;\n  tags: Tag[];\n  usageCount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface PromptEditModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  prompt?: Prompt | null;\n  categories?: Category[];\n  availableTags?: Tag[];\n  onSave: (promptData: {\n    title: string;\n    content: string;\n    description?: string;\n    categoryId?: string;\n    tags: string[];\n  }) => void;\n}\n\nexport default function PromptEditModal({\n  isOpen,\n  onClose,\n  prompt,\n  categories = [],\n  availableTags = [],\n  onSave,\n}: PromptEditModalProps) {\n  const [formData, setFormData] = useState({\n    title: '',\n    content: '',\n    description: '',\n    categoryId: '',\n    tags: [] as string[],\n  });\n  const [newTagName, setNewTagName] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // 模拟数据\n  const mockCategories: Category[] = [\n    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },\n    { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },\n    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },\n  ];\n\n  const mockTags: Tag[] = [\n    { id: '1', name: 'AI', color: '#3B82F6' },\n    { id: '2', name: '编程', color: '#10B981' },\n    { id: '3', name: '创意', color: '#F59E0B' },\n    { id: '4', name: '商务', color: '#EF4444' },\n  ];\n\n  const displayCategories = categories.length > 0 ? categories : mockCategories;\n  const displayTags = availableTags.length > 0 ? availableTags : mockTags;\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (prompt) {\n      setFormData({\n        title: prompt.title,\n        content: prompt.content,\n        description: prompt.description || '',\n        categoryId: prompt.category?.id || '',\n        tags: prompt.tags.map(tag => tag.id),\n      });\n    } else {\n      setFormData({\n        title: '',\n        content: '',\n        description: '',\n        categoryId: '',\n        tags: [],\n      });\n    }\n    setErrors({});\n  }, [prompt, isOpen]);\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.title.trim()) {\n      newErrors.title = '标题不能为空';\n    } else if (formData.title.length > 200) {\n      newErrors.title = '标题不能超过200个字符';\n    }\n\n    if (!formData.content.trim()) {\n      newErrors.content = '内容不能为空';\n    }\n\n    if (formData.description && formData.description.length > 500) {\n      newErrors.description = '描述不能超过500个字符';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setIsSubmitting(true);\n    try {\n      await onSave({\n        title: formData.title.trim(),\n        content: formData.content.trim(),\n        description: formData.description.trim() || undefined,\n        categoryId: formData.categoryId || undefined,\n        tags: formData.tags,\n      });\n      onClose();\n    } catch (error) {\n      console.error('保存失败:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleTagToggle = (tagId: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.includes(tagId)\n        ? prev.tags.filter(id => id !== tagId)\n        : [...prev.tags, tagId],\n    }));\n  };\n\n  const handleAddNewTag = () => {\n    if (newTagName.trim()) {\n      // 这里应该调用API创建新标签，暂时模拟\n      const newTagId = `new-${Date.now()}`;\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTagId],\n      }));\n      setNewTagName('');\n    }\n  };\n\n  const isEditing = !!prompt;\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={isEditing ? '编辑提示词' : '新建提示词'}\n      size=\"lg\"\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* 标题 */}\n        <div>\n          <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            标题 *\n          </label>\n          <input\n            type=\"text\"\n            id=\"title\"\n            value={formData.title}\n            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${\n              errors.title ? 'border-red-300' : 'border-gray-300'\n            }`}\n            placeholder=\"输入提示词标题\"\n          />\n          {errors.title && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>\n          )}\n        </div>\n\n        {/* 描述 */}\n        <div>\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            描述\n          </label>\n          <textarea\n            id=\"description\"\n            rows={2}\n            value={formData.description}\n            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${\n              errors.description ? 'border-red-300' : 'border-gray-300'\n            }`}\n            placeholder=\"简要描述这个提示词的用途\"\n          />\n          {errors.description && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>\n          )}\n        </div>\n\n        {/* 分类 */}\n        <div>\n          <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            分类\n          </label>\n          <select\n            id=\"category\"\n            value={formData.categoryId}\n            onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}\n            className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"\">选择分类</option>\n            {displayCategories.map((category) => (\n              <option key={category.id} value={category.id}>\n                {category.icon} {category.name}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* 标签 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">标签</label>\n          <div className=\"space-y-3\">\n            {/* 现有标签 */}\n            <div className=\"flex flex-wrap gap-2\">\n              {displayTags.map((tag) => (\n                <label key={tag.id} className=\"inline-flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.tags.includes(tag.id)}\n                    onChange={() => handleTagToggle(tag.id)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span\n                    className=\"ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white\"\n                    style={{ backgroundColor: tag.color }}\n                  >\n                    {tag.name}\n                  </span>\n                </label>\n              ))}\n            </div>\n\n            {/* 添加新标签 */}\n            <div className=\"flex items-center space-x-2\">\n              <input\n                type=\"text\"\n                value={newTagName}\n                onChange={(e) => setNewTagName(e.target.value)}\n                placeholder=\"添加新标签\"\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                onKeyPress={(e) => {\n                  if (e.key === 'Enter') {\n                    e.preventDefault();\n                    handleAddNewTag();\n                  }\n                }}\n              />\n              <button\n                type=\"button\"\n                onClick={handleAddNewTag}\n                className=\"px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                添加\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 内容 */}\n        <div>\n          <label htmlFor=\"content\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            提示词内容 *\n          </label>\n          <textarea\n            id=\"content\"\n            rows={8}\n            value={formData.content}\n            onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n            className={`block w-full px-3 py-2 border rounded-md shadow-sm font-mono text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${\n              errors.content ? 'border-red-300' : 'border-gray-300'\n            }`}\n            placeholder=\"输入提示词内容...\"\n          />\n          {errors.content && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.content}</p>\n          )}\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={onClose}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            取消\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSubmitting ? '保存中...' : (isEditing ? '保存更改' : '创建提示词')}\n          </button>\n        </div>\n      </form>\n    </Modal>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AAHA;;;;AA6Ce,SAAS,gBAAgB,EACtC,MAAM,EACN,OAAO,EACP,MAAM,EACN,aAAa,EAAE,EACf,gBAAgB,EAAE,EAClB,MAAM,EACe;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,SAAS;QACT,aAAa;QACb,YAAY;QACZ,MAAM,EAAE;IACV;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,OAAO;IACP,MAAM,iBAA6B;QACjC;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;QACtD;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;QACtD;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;KACvD;IAED,MAAM,WAAkB;QACtB;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;QACxC;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;QACxC;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;QACxC;YAAE,IAAI;YAAK,MAAM;YAAM,OAAO;QAAU;KACzC;IAED,MAAM,oBAAoB,WAAW,MAAM,GAAG,IAAI,aAAa;IAC/D,MAAM,cAAc,cAAc,MAAM,GAAG,IAAI,gBAAgB;IAE/D,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,YAAY;gBACV,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,OAAO;gBACvB,aAAa,OAAO,WAAW,IAAI;gBACnC,YAAY,OAAO,QAAQ,EAAE,MAAM;gBACnC,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;YACrC;QACF,OAAO;YACL,YAAY;gBACV,OAAO;gBACP,SAAS;gBACT,aAAa;gBACb,YAAY;gBACZ,MAAM,EAAE;YACV;QACF;QACA,UAAU,CAAC;IACb,GAAG;QAAC;QAAQ;KAAO;IAEnB,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,SAAS,KAAK,CAAC,MAAM,GAAG,KAAK;YACtC,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAChB,IAAI;YACF,MAAM,OAAO;gBACX,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;gBAC5C,YAAY,SAAS,UAAU,IAAI;gBACnC,MAAM,SAAS,IAAI;YACrB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,SACrB,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,SAC9B;uBAAI,KAAK,IAAI;oBAAE;iBAAM;YAC3B,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,IAAI,WAAW,IAAI,IAAI;YACrB,sBAAsB;YACtB,MAAM,WAAW,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;YACpC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE;qBAAS;gBAChC,CAAC;YACD,cAAc;QAChB;IACF;IAEA,MAAM,YAAY,CAAC,CAAC;IAEpB,qBACE,8OAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAO,YAAY,UAAU;QAC7B,MAAK;kBAEL,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BAEtC,8OAAC;;sCACC,8OAAC;4BAAM,SAAQ;4BAAQ,WAAU;sCAA+C;;;;;;sCAGhF,8OAAC;4BACC,MAAK;4BACL,IAAG;4BACH,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oCAAC,CAAC;4BACxE,WAAW,CAAC,6HAA6H,EACvI,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4BACF,aAAY;;;;;;wBAEb,OAAO,KAAK,kBACX,8OAAC;4BAAE,WAAU;sCAA6B,OAAO,KAAK;;;;;;;;;;;;8BAK1D,8OAAC;;sCACC,8OAAC;4BAAM,SAAQ;4BAAc,WAAU;sCAA+C;;;;;;sCAGtF,8OAAC;4BACC,IAAG;4BACH,MAAM;4BACN,OAAO,SAAS,WAAW;4BAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oCAAC,CAAC;4BAC9E,WAAW,CAAC,6HAA6H,EACvI,OAAO,WAAW,GAAG,mBAAmB,mBACxC;4BACF,aAAY;;;;;;wBAEb,OAAO,WAAW,kBACjB,8OAAC;4BAAE,WAAU;sCAA6B,OAAO,WAAW;;;;;;;;;;;;8BAKhE,8OAAC;;sCACC,8OAAC;4BAAM,SAAQ;4BAAW,WAAU;sCAA+C;;;;;;sCAGnF,8OAAC;4BACC,IAAG;4BACH,OAAO,SAAS,UAAU;4BAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oCAAC,CAAC;4BAC7E,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAG;;;;;;gCAChB,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;wCAAyB,OAAO,SAAS,EAAE;;4CACzC,SAAS,IAAI;4CAAC;4CAAE,SAAS,IAAI;;uCADnB,SAAS,EAAE;;;;;;;;;;;;;;;;;8BAQ9B,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAChE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC;4CAAmB,WAAU;;8DAC5B,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;oDACtC,UAAU,IAAM,gBAAgB,IAAI,EAAE;oDACtC,WAAU;;;;;;8DAEZ,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,IAAI,KAAK;oDAAC;8DAEnC,IAAI,IAAI;;;;;;;2CAXD,IAAI,EAAE;;;;;;;;;;8CAkBtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,WAAU;4CACV,YAAY,CAAC;gDACX,IAAI,EAAE,GAAG,KAAK,SAAS;oDACrB,EAAE,cAAc;oDAChB;gDACF;4CACF;;;;;;sDAEF,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;;sCACC,8OAAC;4BAAM,SAAQ;4BAAU,WAAU;sCAA+C;;;;;;sCAGlF,8OAAC;4BACC,IAAG;4BACH,MAAM;4BACN,OAAO,SAAS,OAAO;4BACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oCAAC,CAAC;4BAC1E,WAAW,CAAC,+IAA+I,EACzJ,OAAO,OAAO,GAAG,mBAAmB,mBACpC;4BACF,aAAY;;;;;;wBAEb,OAAO,OAAO,kBACb,8OAAC;4BAAE,WAAU;sCAA6B,OAAO,OAAO;;;;;;;;;;;;8BAK5D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,eAAe,WAAY,YAAY,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 2814, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/CategoryManageModal.tsx"], "sourcesContent": ["/**\n * 分类管理模态框组件\n * 用于管理提示词分类\n */\n\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport Modal from './Modal';\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n  icon: string;\n  count?: number;\n}\n\ninterface CategoryManageModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  categories?: Category[];\n  onSave: (categoryData: {\n    name: string;\n    description?: string;\n    color: string;\n    icon: string;\n  }) => void;\n  onUpdate: (id: string, categoryData: {\n    name: string;\n    description?: string;\n    color: string;\n    icon: string;\n  }) => void;\n  onDelete: (id: string) => void;\n}\n\nexport default function CategoryManageModal({\n  isOpen,\n  onClose,\n  categories = [],\n  onSave,\n  onUpdate,\n  onDelete,\n}: CategoryManageModalProps) {\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: '#3B82F6',\n    icon: '📁',\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // 预设颜色选项\n  const colorOptions = [\n    '#3B82F6', // 蓝色\n    '#10B981', // 绿色\n    '#F59E0B', // 黄色\n    '#EF4444', // 红色\n    '#8B5CF6', // 紫色\n    '#F97316', // 橙色\n    '#06B6D4', // 青色\n    '#84CC16', // 石灰色\n  ];\n\n  // 预设图标选项\n  const iconOptions = [\n    '📁', '✍️', '💻', '🌐', '📊', '🎨', '🔧', '📚',\n    '💡', '🎯', '🚀', '⚡', '🔥', '💎', '🎪', '🎭',\n  ];\n\n  // 模拟数据\n  const mockCategories: Category[] = [\n    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️', count: 12 },\n    { id: '2', name: '代码生成', color: '#10B981', icon: '💻', count: 8 },\n    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐', count: 5 },\n  ];\n\n  const displayCategories = categories.length > 0 ? categories : mockCategories;\n\n  // 重置表单\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      color: '#3B82F6',\n      icon: '📁',\n    });\n    setEditingCategory(null);\n    setErrors({});\n  };\n\n  // 开始编辑分类\n  const startEdit = (category: Category) => {\n    setEditingCategory(category);\n    setFormData({\n      name: category.name,\n      description: '',\n      color: category.color,\n      icon: category.icon,\n    });\n    setErrors({});\n  };\n\n  // 验证表单\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = '分类名称不能为空';\n    } else if (formData.name.length > 50) {\n      newErrors.name = '分类名称不能超过50个字符';\n    }\n\n    // 检查名称是否重复\n    const isDuplicate = displayCategories.some(\n      cat => cat.name === formData.name.trim() && cat.id !== editingCategory?.id\n    );\n    if (isDuplicate) {\n      newErrors.name = '分类名称已存在';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // 提交表单\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    const categoryData = {\n      name: formData.name.trim(),\n      description: formData.description.trim() || undefined,\n      color: formData.color,\n      icon: formData.icon,\n    };\n\n    if (editingCategory) {\n      onUpdate(editingCategory.id, categoryData);\n    } else {\n      onSave(categoryData);\n    }\n\n    resetForm();\n  };\n\n  // 删除分类\n  const handleDelete = (category: Category) => {\n    if (window.confirm(`确定要删除分类\"${category.name}\"吗？${category.count ? `这将影响${category.count}个提示词。` : ''}`)) {\n      onDelete(category.id);\n    }\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"管理分类\"\n      size=\"lg\"\n    >\n      <div className=\"space-y-6\">\n        {/* 分类列表 */}\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">现有分类</h3>\n          <div className=\"space-y-2\">\n            {displayCategories.map((category) => (\n              <div\n                key={category.id}\n                className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span\n                    className=\"inline-flex items-center justify-center w-8 h-8 rounded-full text-white text-sm font-medium\"\n                    style={{ backgroundColor: category.color }}\n                  >\n                    {category.icon}\n                  </span>\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900\">{category.name}</h4>\n                    {category.count !== undefined && (\n                      <p className=\"text-xs text-gray-500\">{category.count} 个提示词</p>\n                    )}\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => startEdit(category)}\n                    className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    编辑\n                  </button>\n                  <button\n                    onClick={() => handleDelete(category)}\n                    className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n                  >\n                    删除\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* 添加/编辑分类表单 */}\n        <div className=\"border-t border-gray-200 pt-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            {editingCategory ? '编辑分类' : '添加新分类'}\n          </h3>\n          \n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {/* 分类名称 */}\n            <div>\n              <label htmlFor=\"categoryName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                分类名称 *\n              </label>\n              <input\n                type=\"text\"\n                id=\"categoryName\"\n                value={formData.name}\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${\n                  errors.name ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"输入分类名称\"\n              />\n              {errors.name && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>\n              )}\n            </div>\n\n            {/* 分类描述 */}\n            <div>\n              <label htmlFor=\"categoryDescription\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                描述\n              </label>\n              <textarea\n                id=\"categoryDescription\"\n                rows={2}\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"简要描述这个分类\"\n              />\n            </div>\n\n            {/* 颜色选择 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">颜色</label>\n              <div className=\"flex flex-wrap gap-2\">\n                {colorOptions.map((color) => (\n                  <button\n                    key={color}\n                    type=\"button\"\n                    onClick={() => setFormData(prev => ({ ...prev, color }))}\n                    className={`w-8 h-8 rounded-full border-2 ${\n                      formData.color === color ? 'border-gray-400' : 'border-gray-200'\n                    }`}\n                    style={{ backgroundColor: color }}\n                  />\n                ))}\n              </div>\n            </div>\n\n            {/* 图标选择 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">图标</label>\n              <div className=\"grid grid-cols-8 gap-2\">\n                {iconOptions.map((icon) => (\n                  <button\n                    key={icon}\n                    type=\"button\"\n                    onClick={() => setFormData(prev => ({ ...prev, icon }))}\n                    className={`w-10 h-10 rounded-md border-2 flex items-center justify-center text-lg ${\n                      formData.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    {icon}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* 预览 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">预览</label>\n              <div className=\"inline-flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg\">\n                <span\n                  className=\"inline-flex items-center justify-center w-6 h-6 rounded-full text-white text-sm\"\n                  style={{ backgroundColor: formData.color }}\n                >\n                  {formData.icon}\n                </span>\n                <span className=\"text-sm font-medium text-gray-900\">\n                  {formData.name || '分类名称'}\n                </span>\n              </div>\n            </div>\n\n            {/* 操作按钮 */}\n            <div className=\"flex justify-end space-x-3\">\n              {editingCategory && (\n                <button\n                  type=\"button\"\n                  onClick={resetForm}\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                >\n                  取消编辑\n                </button>\n              )}\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                {editingCategory ? '保存更改' : '添加分类'}\n              </button>\n            </div>\n          </form>\n        </div>\n\n        {/* 关闭按钮 */}\n        <div className=\"flex justify-end pt-4 border-t border-gray-200\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            关闭\n          </button>\n        </div>\n      </div>\n    </Modal>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AAHA;;;;AAgCe,SAAS,oBAAoB,EAC1C,MAAM,EACN,OAAO,EACP,aAAa,EAAE,EACf,MAAM,EACN,QAAQ,EACR,QAAQ,EACiB;IACzB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,SAAS;IACT,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,SAAS;IACT,MAAM,cAAc;QAClB;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAC1C;QAAM;QAAM;QAAM;QAAK;QAAM;QAAM;QAAM;KAC1C;IAED,OAAO;IACP,MAAM,iBAA6B;QACjC;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAG;QACjE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;QAChE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;KACjE;IAED,MAAM,oBAAoB,WAAW,MAAM,GAAG,IAAI,aAAa;IAE/D,OAAO;IACP,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,aAAa;YACb,OAAO;YACP,MAAM;QACR;QACA,mBAAmB;QACnB,UAAU,CAAC;IACb;IAEA,SAAS;IACT,MAAM,YAAY,CAAC;QACjB,mBAAmB;QACnB,YAAY;YACV,MAAM,SAAS,IAAI;YACnB,aAAa;YACb,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;QACrB;QACA,UAAU,CAAC;IACb;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YACpC,UAAU,IAAI,GAAG;QACnB;QAEA,WAAW;QACX,MAAM,cAAc,kBAAkB,IAAI,CACxC,CAAA,MAAO,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,IAAI,MAAM,IAAI,EAAE,KAAK,iBAAiB;QAE1E,IAAI,aAAa;YACf,UAAU,IAAI,GAAG;QACnB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,MAAM,eAAe;YACnB,MAAM,SAAS,IAAI,CAAC,IAAI;YACxB,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;YAC5C,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;QACrB;QAEA,IAAI,iBAAiB;YACnB,SAAS,gBAAgB,EAAE,EAAE;QAC/B,OAAO;YACL,OAAO;QACT;QAEA;IACF;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,OAAO,CAAC,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,SAAS,KAAK,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG;YACtG,SAAS,SAAS,EAAE;QACtB;IACF;IAEA,qBACE,8OAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,SAAS,KAAK;oDAAC;8DAExC,SAAS,IAAI;;;;;;8DAEhB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqC,SAAS,IAAI;;;;;;wDAC/D,SAAS,KAAK,KAAK,2BAClB,8OAAC;4DAAE,WAAU;;gEAAyB,SAAS,KAAK;gEAAC;;;;;;;;;;;;;;;;;;;sDAI3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,UAAU;oDACzB,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAU;8DACX;;;;;;;;;;;;;mCA3BE,SAAS,EAAE;;;;;;;;;;;;;;;;8BAqCxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,kBAAkB,SAAS;;;;;;sCAG9B,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAA+C;;;;;;sDAGvF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACvE,WAAW,CAAC,6HAA6H,EACvI,OAAO,IAAI,GAAG,mBAAmB,mBACjC;4CACF,aAAY;;;;;;wCAEb,OAAO,IAAI,kBACV,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,IAAI;;;;;;;;;;;;8CAKzD,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAsB,WAAU;sDAA+C;;;;;;sDAG9F,8OAAC;4CACC,IAAG;4CACH,MAAM;4CACN,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC9E,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;oDAEC,MAAK;oDACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE;4DAAM,CAAC;oDACtD,WAAW,CAAC,8BAA8B,EACxC,SAAS,KAAK,KAAK,QAAQ,oBAAoB,mBAC/C;oDACF,OAAO;wDAAE,iBAAiB;oDAAM;mDAN3B;;;;;;;;;;;;;;;;8CAab,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;oDAEC,MAAK;oDACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE;4DAAK,CAAC;oDACrD,WAAW,CAAC,uEAAuE,EACjF,SAAS,IAAI,KAAK,OAAO,+BAA+B,yCACxD;8DAED;mDAPI;;;;;;;;;;;;;;;;8CAcb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,SAAS,KAAK;oDAAC;8DAExC,SAAS,IAAI;;;;;;8DAEhB,8OAAC;oDAAK,WAAU;8DACb,SAAS,IAAI,IAAI;;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAIH,8OAAC;4CACC,MAAK;4CACL,WAAU;sDAET,kBAAkB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;8BAOpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 3343, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/ConfirmDialog.tsx"], "sourcesContent": ["/**\n * 确认对话框组件\n * 用于确认删除等危险操作\n */\n\n'use client';\n\nimport Modal from './Modal';\n\ninterface ConfirmDialogProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => void;\n  title?: string;\n  message: string;\n  confirmText?: string;\n  cancelText?: string;\n  type?: 'danger' | 'warning' | 'info';\n  isLoading?: boolean;\n}\n\nexport default function ConfirmDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  title = '确认操作',\n  message,\n  confirmText = '确认',\n  cancelText = '取消',\n  type = 'danger',\n  isLoading = false,\n}: ConfirmDialogProps) {\n  const getTypeStyles = () => {\n    switch (type) {\n      case 'danger':\n        return {\n          icon: (\n            <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n            </svg>\n          ),\n          iconBg: 'bg-red-100',\n          confirmButton: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',\n        };\n      case 'warning':\n        return {\n          icon: (\n            <svg className=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n            </svg>\n          ),\n          iconBg: 'bg-yellow-100',\n          confirmButton: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',\n        };\n      case 'info':\n        return {\n          icon: (\n            <svg className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          ),\n          iconBg: 'bg-blue-100',\n          confirmButton: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',\n        };\n      default:\n        return {\n          icon: null,\n          iconBg: '',\n          confirmButton: 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500',\n        };\n    }\n  };\n\n  const typeStyles = getTypeStyles();\n\n  const handleConfirm = () => {\n    onConfirm();\n    if (!isLoading) {\n      onClose();\n    }\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      size=\"sm\"\n      showCloseButton={false}\n    >\n      <div className=\"sm:flex sm:items-start\">\n        {/* 图标 */}\n        {typeStyles.icon && (\n          <div className={`mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full ${typeStyles.iconBg} sm:mx-0 sm:h-10 sm:w-10`}>\n            {typeStyles.icon}\n          </div>\n        )}\n\n        {/* 内容 */}\n        <div className=\"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n            {title}\n          </h3>\n          <div className=\"mt-2\">\n            <p className=\"text-sm text-gray-500\">\n              {message}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* 操作按钮 */}\n      <div className=\"mt-5 sm:mt-4 sm:flex sm:flex-row-reverse\">\n        <button\n          type=\"button\"\n          onClick={handleConfirm}\n          disabled={isLoading}\n          className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed ${typeStyles.confirmButton}`}\n        >\n          {isLoading ? (\n            <>\n              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              处理中...\n            </>\n          ) : (\n            confirmText\n          )}\n        </button>\n        <button\n          type=\"button\"\n          onClick={onClose}\n          disabled={isLoading}\n          className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {cancelText}\n        </button>\n      </div>\n    </Modal>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAFA;;;AAgBe,SAAS,cAAc,EACpC,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,MAAM,EACd,OAAO,EACP,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,OAAO,QAAQ,EACf,YAAY,KAAK,EACE;IACnB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,oBACE,8OAAC;wBAAI,WAAU;wBAAuB,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCAC3E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,QAAQ;oBACR,eAAe;gBACjB;YACF,KAAK;gBACH,OAAO;oBACL,oBACE,8OAAC;wBAAI,WAAU;wBAA0B,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCAC9E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,QAAQ;oBACR,eAAe;gBACjB;YACF,KAAK;gBACH,OAAO;oBACL,oBACE,8OAAC;wBAAI,WAAU;wBAAwB,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCAC5E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,QAAQ;oBACR,eAAe;gBACjB;YACF;gBACE,OAAO;oBACL,MAAM;oBACN,QAAQ;oBACR,eAAe;gBACjB;QACJ;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,gBAAgB;QACpB;QACA,IAAI,CAAC,WAAW;YACd;QACF;IACF;IAEA,qBACE,8OAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,MAAK;QACL,iBAAiB;;0BAEjB,8OAAC;gBAAI,WAAU;;oBAEZ,WAAW,IAAI,kBACd,8OAAC;wBAAI,WAAW,CAAC,8EAA8E,EAAE,WAAW,MAAM,CAAC,wBAAwB,CAAC;kCACzI,WAAW,IAAI;;;;;;kCAKpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;;0BAOT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,6PAA6P,EAAE,WAAW,aAAa,EAAE;kCAEpS,0BACC;;8CACE,8OAAC;oCAAI,WAAU;oCAA6C,OAAM;oCAA6B,MAAK;oCAAO,SAAQ;;sDACjH,8OAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,8OAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;2CAIR;;;;;;kCAGJ,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 3581, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport SearchBar from '@/components/ui/SearchBar';\nimport PromptCard from '@/components/ui/PromptCard';\nimport PromptDetailModal from '@/components/ui/PromptDetailModal';\nimport PromptEditModal from '@/components/ui/PromptEditModal';\nimport CategoryManageModal from '@/components/ui/CategoryManageModal';\nimport ConfirmDialog from '@/components/ui/ConfirmDialog';\nimport { useToast } from '@/components/ui/ToastContainer';\n\ninterface SearchFilters {\n  query: string;\n  categoryId: string | null;\n  tags: string[];\n  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title';\n  sortOrder: 'asc' | 'desc';\n}\n\nexport default function Home() {\n  const { showSuccess, showError, showInfo } = useToast();\n\n  const [filters, setFilters] = useState<SearchFilters>({\n    query: '',\n    categoryId: null,\n    tags: [],\n    sortBy: 'updatedAt',\n    sortOrder: 'desc',\n  });\n\n  // 模态框状态\n  const [selectedPrompt, setSelectedPrompt] = useState<any>(null);\n  const [editingPrompt, setEditingPrompt] = useState<any>(null);\n  const [showDetailModal, setShowDetailModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showCategoryModal, setShowCategoryModal] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});\n\n  // 模拟提示词数据\n  const mockPrompts = [\n    {\n      id: '1',\n      title: '写作助手 - 文章大纲生成',\n      content: '请帮我为以下主题创建一个详细的文章大纲：[主题]\\n\\n要求：\\n1. 包含引言、主体和结论\\n2. 主体部分至少3个要点\\n3. 每个要点包含2-3个子点\\n4. 提供吸引人的标题建议',\n      description: '帮助用户快速生成文章大纲，提高写作效率',\n      category: { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },\n      tags: [\n        { id: '1', name: 'AI', color: '#3B82F6' },\n        { id: '3', name: '创意', color: '#F59E0B' },\n      ],\n      usageCount: 25,\n      createdAt: '2024-01-15T10:00:00Z',\n      updatedAt: '2024-01-20T15:30:00Z',\n    },\n    {\n      id: '2',\n      title: '代码生成 - React组件模板',\n      content: '请为我生成一个React函数组件，要求：\\n\\n组件名：[组件名]\\n功能：[功能描述]\\n\\n请包含：\\n- TypeScript类型定义\\n- Props接口\\n- 基本的JSX结构\\n- 简单的样式类名\\n- 必要的注释',\n      description: '快速生成React组件的基础模板代码',\n      category: { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },\n      tags: [\n        { id: '2', name: '编程', color: '#10B981' },\n        { id: '1', name: 'AI', color: '#3B82F6' },\n      ],\n      usageCount: 18,\n      createdAt: '2024-01-10T09:00:00Z',\n      updatedAt: '2024-01-18T11:20:00Z',\n    },\n    {\n      id: '3',\n      title: '翻译工具 - 专业文档翻译',\n      content: '请将以下内容翻译成[目标语言]，要求：\\n\\n1. 保持专业术语的准确性\\n2. 语言流畅自然\\n3. 保留原文格式\\n4. 如有专业术语，请在括号内标注原文\\n\\n原文：\\n[待翻译内容]',\n      description: '专业文档翻译，保持术语准确性和格式完整',\n      category: { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },\n      tags: [\n        { id: '4', name: '商务', color: '#EF4444' },\n      ],\n      usageCount: 12,\n      createdAt: '2024-01-05T14:00:00Z',\n      updatedAt: '2024-01-15T16:45:00Z',\n    },\n  ];\n\n  const handleFiltersChange = (newFilters: SearchFilters) => {\n    setFilters(newFilters);\n  };\n\n  const handlePromptEdit = (prompt: any) => {\n    setEditingPrompt(prompt);\n    setShowEditModal(true);\n  };\n\n  const handlePromptDelete = (promptId: string) => {\n    setConfirmAction(() => () => {\n      console.log('删除提示词:', promptId);\n      // 这里应该调用API删除提示词\n    });\n    setShowConfirmDialog(true);\n  };\n\n  const handlePromptCopy = (content: string) => {\n    console.log('复制内容:', content);\n    showSuccess('复制成功', '提示词内容已复制到剪贴板');\n  };\n\n  const handlePromptView = (prompt: any) => {\n    setSelectedPrompt(prompt);\n    setShowDetailModal(true);\n  };\n\n  const handleNewPrompt = () => {\n    setEditingPrompt(null);\n    setShowEditModal(true);\n  };\n\n  const handlePromptSave = (promptData: any) => {\n    console.log('保存提示词:', promptData);\n    // 这里应该调用API保存提示词\n    showSuccess('保存成功', '提示词已保存');\n  };\n\n  const handleCategorySave = (categoryData: any) => {\n    console.log('保存分类:', categoryData);\n    // 这里应该调用API保存分类\n  };\n\n  const handleCategoryUpdate = (id: string, categoryData: any) => {\n    console.log('更新分类:', id, categoryData);\n    // 这里应该调用API更新分类\n  };\n\n  const handleCategoryDelete = (id: string) => {\n    console.log('删除分类:', id);\n    // 这里应该调用API删除分类\n  };\n\n  return (\n    <MainLayout\n      onNewPrompt={handleNewPrompt}\n      onManageCategories={() => setShowCategoryModal(true)}\n    >\n      <div className=\"space-y-6\">\n        {/* 页面标题 */}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">我的提示词</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            管理和使用您的AI提示词库\n          </p>\n        </div>\n\n        {/* 搜索和筛选 */}\n        <SearchBar\n          filters={filters}\n          onFiltersChange={handleFiltersChange}\n          placeholder=\"搜索提示词标题、内容或描述...\"\n        />\n\n        {/* 提示词列表 */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n          {mockPrompts.map((prompt) => (\n            <PromptCard\n              key={prompt.id}\n              prompt={prompt}\n              onEdit={handlePromptEdit}\n              onDelete={handlePromptDelete}\n              onCopy={handlePromptCopy}\n              onView={handlePromptView}\n            />\n          ))}\n        </div>\n\n        {/* 空状态 */}\n        {mockPrompts.length === 0 && (\n          <div className=\"text-center py-12\">\n            <svg\n              className=\"mx-auto h-12 w-12 text-gray-400\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              />\n            </svg>\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">没有找到提示词</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">开始创建您的第一个提示词吧。</p>\n            <div className=\"mt-6\">\n              <button\n                onClick={handleNewPrompt}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <svg className=\"-ml-1 mr-2 h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clipRule=\"evenodd\" />\n                </svg>\n                新建提示词\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 模态框组件 */}\n      <PromptDetailModal\n        isOpen={showDetailModal}\n        onClose={() => setShowDetailModal(false)}\n        prompt={selectedPrompt}\n        onEdit={handlePromptEdit}\n        onDelete={handlePromptDelete}\n        onCopy={handlePromptCopy}\n      />\n\n      <PromptEditModal\n        isOpen={showEditModal}\n        onClose={() => setShowEditModal(false)}\n        prompt={editingPrompt}\n        onSave={handlePromptSave}\n      />\n\n      <CategoryManageModal\n        isOpen={showCategoryModal}\n        onClose={() => setShowCategoryModal(false)}\n        onSave={handleCategorySave}\n        onUpdate={handleCategoryUpdate}\n        onDelete={handleCategoryDelete}\n      />\n\n      <ConfirmDialog\n        isOpen={showConfirmDialog}\n        onClose={() => setShowConfirmDialog(false)}\n        onConfirm={confirmAction}\n        message=\"此操作无法撤销，确定要继续吗？\"\n      />\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD;IAEpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,OAAO;QACP,YAAY;QACZ,MAAM,EAAE;QACR,QAAQ;QACR,WAAW;IACb;IAEA,QAAQ;IACR,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,KAAO;IAEtE,UAAU;IACV,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,OAAO;gBAAW,MAAM;YAAK;YAChE,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAM,OAAO;gBAAU;gBACxC;oBAAE,IAAI;oBAAK,MAAM;oBAAM,OAAO;gBAAU;aACzC;YACD,YAAY;YACZ,WAAW;YACX,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,OAAO;gBAAW,MAAM;YAAK;YAChE,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAM,OAAO;gBAAU;gBACxC;oBAAE,IAAI;oBAAK,MAAM;oBAAM,OAAO;gBAAU;aACzC;YACD,YAAY;YACZ,WAAW;YACX,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,OAAO;gBAAW,MAAM;YAAK;YAChE,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAM,OAAO;gBAAU;aACzC;YACD,YAAY;YACZ,WAAW;YACX,WAAW;QACb;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,IAAM;gBACrB,QAAQ,GAAG,CAAC,UAAU;YACtB,iBAAiB;YACnB;QACA,qBAAqB;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,SAAS;QACrB,YAAY,QAAQ;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,mBAAmB;IACrB;IAEA,MAAM,kBAAkB;QACtB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,UAAU;QACtB,iBAAiB;QACjB,YAAY,QAAQ;IACtB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,SAAS;IACrB,gBAAgB;IAClB;IAEA,MAAM,uBAAuB,CAAC,IAAY;QACxC,QAAQ,GAAG,CAAC,SAAS,IAAI;IACzB,gBAAgB;IAClB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,SAAS;IACrB,gBAAgB;IAClB;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;QACT,aAAa;QACb,oBAAoB,IAAM,qBAAqB;;0BAE/C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAM5C,8OAAC,qIAAA,CAAA,UAAS;wBACR,SAAS;wBACT,iBAAiB;wBACjB,aAAY;;;;;;kCAId,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,sIAAA,CAAA,UAAU;gCAET,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,QAAQ;gCACR,QAAQ;+BALH,OAAO,EAAE;;;;;;;;;;oBAWnB,YAAY,MAAM,KAAK,mBACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,QAAO;0CAEP,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;0CAGN,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAe,SAAQ;sDAC9D,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAwF,UAAS;;;;;;;;;;;wCACxH;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,8OAAC,6IAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,QAAQ;;;;;;0BAGV,8OAAC,2IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,QAAQ;gBACR,QAAQ;;;;;;0BAGV,8OAAC,+IAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,QAAQ;gBACR,UAAU;gBACV,UAAU;;;;;;0BAGZ,8OAAC,yIAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,WAAW;gBACX,SAAQ;;;;;;;;;;;;AAIhB", "debugId": null}}]}