// 提示词管理工具 Prisma Schema
// 基于设计文档的完整数据模型定义

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js 认证相关模型
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  categories    Category[]
  prompts       Prompt[]
  searchHistory SearchHistory[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// 应用核心模型
model Category {
  id          String   @id @default(cuid())
  name        String
  description String?
  color       String   @default("#3B82F6")
  icon        String   @default("folder")
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  prompts Prompt[]

  @@unique([name, userId])
}

model Prompt {
  id          String   @id @default(cuid())
  title       String
  content     String   @db.Text
  description String?
  categoryId  String?
  userId      String
  usageCount  Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user     User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  category Category?   @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  tags     PromptTag[]

  @@index([userId])
  @@index([categoryId])
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  color     String   @default("#10B981")
  createdAt DateTime @default(now())

  prompts PromptTag[]
}

model PromptTag {
  promptId String
  tagId    String

  prompt Prompt @relation(fields: [promptId], references: [id], onDelete: Cascade)
  tag    Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([promptId, tagId])
}

model SearchHistory {
  id        String   @id @default(cuid())
  query     String
  userId    String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}
