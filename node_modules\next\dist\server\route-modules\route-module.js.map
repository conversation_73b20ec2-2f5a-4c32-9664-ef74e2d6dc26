{"version": 3, "sources": ["../../../src/server/route-modules/route-module.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'node:http'\nimport type {\n  InstrumentationOnRequestError,\n  RequestErrorContext,\n} from '../instrumentation/types'\nimport type { ParsedUrlQuery } from 'node:querystring'\nimport type { UrlWithParsedQuery } from 'node:url'\nimport type {\n  PrerenderManifest,\n  RequiredServerFilesManifest,\n} from '../../build'\nimport type { DevRoutesManifest } from '../lib/router-utils/setup-dev-bundler'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_REFERENCE_MANIFEST,\n  DYNAMIC_CSS_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_FILES_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n} from '../../shared/lib/constants'\nimport { parseReqUrl } from '../../lib/url'\nimport {\n  normalizeLocalePath,\n  type PathLocale,\n} from '../../shared/lib/i18n/normalize-locale-path'\nimport { isDynamicRoute } from '../../shared/lib/router/utils'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { getServerUtils } from '../server-utils'\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { checkIsOnDemandRevalidate } from '../api-utils'\nimport type { PreviewData } from '../../types'\nimport type { BuildManifest } from '../get-page-files'\nimport type { ReactLoadableManifest } from '../load-components'\nimport type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport { normalizeDataPath } from '../../shared/lib/page-path/normalize-data-path'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { addRequestMeta, getRequestMeta } from '../request-meta'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { isStaticMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport { IncrementalCache } from '../lib/incremental-cache'\nimport { initializeCacheHandlers, setCacheHandler } from '../use-cache/handlers'\nimport { interopDefault } from '../app-render/interop-default'\nimport type { RouteKind } from '../route-kind'\nimport type { BaseNextRequest } from '../base-http'\nimport type { I18NConfig, NextConfigComplete } from '../config-shared'\nimport ResponseCache, { type ResponseGenerator } from '../response-cache'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n  type RouterServerContext,\n} from '../lib/router-utils/router-server-context'\nimport { decodePathParams } from '../lib/router-utils/decode-path-params'\nimport { removeTrailingSlash } from '../../shared/lib/router/utils/remove-trailing-slash'\n\n/**\n * RouteModuleOptions is the options that are passed to the route module, other\n * route modules should extend this class to add specific options for their\n * route.\n */\nexport interface RouteModuleOptions<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  readonly definition: Readonly<D>\n  readonly userland: Readonly<U>\n  readonly distDir: string\n  readonly projectDir: string\n}\n\n/**\n * RouteHandlerContext is the base context for a route handler.\n */\nexport interface RouteModuleHandleContext {\n  /**\n   * Any matched parameters for the request. This is only defined for dynamic\n   * routes.\n   */\n  params: Record<string, string | string[] | undefined> | undefined\n}\n\nconst dynamicImportEsmDefault = (id: string) =>\n  import(/* webpackIgnore: true */ /* turbopackIgnore: true */ id).then(\n    (mod) => mod.default || mod\n  )\n\n/**\n * RouteModule is the base class for all route modules. This class should be\n * extended by all route modules.\n */\nexport abstract class RouteModule<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  /**\n   * The userland module. This is the module that is exported from the user's\n   * code. This is marked as readonly to ensure that the module is not mutated\n   * because the module (when compiled) only provides getters.\n   */\n  public readonly userland: Readonly<U>\n\n  /**\n   * The definition of the route.\n   */\n  public readonly definition: Readonly<D>\n\n  /**\n   * The shared modules that are exposed and required for the route module.\n   */\n  public static readonly sharedModules: any\n\n  public isDev: boolean\n  public distDir: string\n  public projectDir: string\n  public isAppRouter?: boolean\n  public incrementCache?: IncrementalCache\n  public responseCache?: ResponseCache\n\n  constructor({\n    userland,\n    definition,\n    distDir,\n    projectDir,\n  }: RouteModuleOptions<D, U>) {\n    this.userland = userland\n    this.definition = definition\n    this.isDev = process.env.NODE_ENV === 'development'\n    this.distDir = distDir\n    this.projectDir = projectDir\n  }\n\n  public async instrumentationOnRequestError(\n    req: IncomingMessage | BaseNextRequest,\n    ...args: Parameters<InstrumentationOnRequestError>\n  ) {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      const { getEdgeInstrumentationModule } = await import('../web/globals')\n      const instrumentation = await getEdgeInstrumentationModule()\n\n      if (instrumentation) {\n        await instrumentation.onRequestError?.(...args)\n      }\n    } else {\n      const { join } = require('node:path') as typeof import('node:path')\n      const absoluteProjectDir =\n        getRequestMeta(req, 'projectDir') ||\n        join(process.cwd(), this.projectDir)\n\n      const { instrumentationOnRequestError } = await import(\n        '../lib/router-utils/instrumentation-globals.external'\n      )\n\n      return instrumentationOnRequestError(\n        absoluteProjectDir,\n        this.distDir,\n        ...args\n      )\n    }\n  }\n\n  private loadManifests(\n    srcPage: string,\n    projectDir?: string\n  ): {\n    buildId: string\n    buildManifest: BuildManifest\n    routesManifest: DeepReadonly<DevRoutesManifest>\n    nextFontManifest: DeepReadonly<NextFontManifest>\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n    serverFilesManifest: RequiredServerFilesManifest\n    reactLoadableManifest: DeepReadonly<ReactLoadableManifest>\n    subresourceIntegrityManifest: any\n    clientReferenceManifest: any\n    serverActionsManifest: any\n    dynamicCssManifest: any\n  } {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      const { getEdgePreviewProps } =\n        require('../web/get-edge-preview-props') as typeof import('../web/get-edge-preview-props')\n\n      const maybeJSONParse = (str?: string) =>\n        str ? JSON.parse(str) : undefined\n\n      return {\n        buildId: process.env.__NEXT_BUILD_ID || '',\n        buildManifest: self.__BUILD_MANIFEST as any,\n        reactLoadableManifest: maybeJSONParse(self.__REACT_LOADABLE_MANIFEST),\n        nextFontManifest: maybeJSONParse(self.__NEXT_FONT_MANIFEST),\n        prerenderManifest: {\n          routes: {},\n          dynamicRoutes: {},\n          notFoundRoutes: [],\n          version: 4,\n          preview: getEdgePreviewProps(),\n        },\n        routesManifest: {\n          version: 4,\n          caseSensitive: Boolean(process.env.__NEXT_CASE_SENSITIVE_ROUTES),\n          basePath: process.env.__NEXT_BASE_PATH || '',\n          rewrites: (process.env.__NEXT_REWRITES as any) || {\n            beforeFiles: [],\n            afterFiles: [],\n            fallback: [],\n          },\n          redirects: [],\n          headers: [],\n          i18n:\n            (process.env.__NEXT_I18N_CONFIG as any as I18NConfig) || undefined,\n          skipMiddlewareUrlNormalize: Boolean(\n            process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n          ),\n        },\n        serverFilesManifest: {\n          config: (globalThis as any).nextConfig || {},\n        } as any,\n        clientReferenceManifest: self.__RSC_MANIFEST?.[srcPage],\n        serverActionsManifest: maybeJSONParse(self.__RSC_SERVER_MANIFEST),\n        subresourceIntegrityManifest: maybeJSONParse(\n          self.__SUBRESOURCE_INTEGRITY_MANIFEST\n        ),\n        dynamicCssManifest: maybeJSONParse(self.__DYNAMIC_CSS_MANIFEST),\n      }\n    } else {\n      if (!projectDir) {\n        throw new Error('Invariant: projectDir is required for node runtime')\n      }\n      const { loadManifestFromRelativePath } =\n        require('../load-manifest.external') as typeof import('../load-manifest.external')\n      const normalizedPagePath = normalizePagePath(srcPage)\n\n      const [\n        routesManifest,\n        prerenderManifest,\n        buildManifest,\n        reactLoadableManifest,\n        nextFontManifest,\n        clientReferenceManifest,\n        serverActionsManifest,\n        subresourceIntegrityManifest,\n        serverFilesManifest,\n        buildId,\n        dynamicCssManifest,\n      ] = [\n        loadManifestFromRelativePath<DevRoutesManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: ROUTES_MANIFEST,\n          shouldCache: !this.isDev,\n        }),\n        loadManifestFromRelativePath<PrerenderManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: PRERENDER_MANIFEST,\n          shouldCache: !this.isDev,\n        }),\n        loadManifestFromRelativePath<BuildManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: BUILD_MANIFEST,\n          shouldCache: !this.isDev,\n        }),\n        loadManifestFromRelativePath<ReactLoadableManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: process.env.TURBOPACK\n            ? `server/${this.isAppRouter ? 'app' : 'pages'}${normalizedPagePath}/${REACT_LOADABLE_MANIFEST}`\n            : REACT_LOADABLE_MANIFEST,\n          handleMissing: true,\n          shouldCache: !this.isDev,\n        }),\n        loadManifestFromRelativePath<NextFontManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: `server/${NEXT_FONT_MANIFEST}.json`,\n          shouldCache: !this.isDev,\n        }),\n        this.isAppRouter && !isStaticMetadataRoute(srcPage)\n          ? loadManifestFromRelativePath({\n              distDir: this.distDir,\n              projectDir,\n              useEval: true,\n              handleMissing: true,\n              manifest: `server/app${srcPage.replace(/%5F/g, '_') + '_' + CLIENT_REFERENCE_MANIFEST}.js`,\n              shouldCache: !this.isDev,\n            })\n          : undefined,\n        this.isAppRouter\n          ? loadManifestFromRelativePath<any>({\n              distDir: this.distDir,\n              projectDir,\n              manifest: `server/${SERVER_REFERENCE_MANIFEST}.json`,\n              handleMissing: true,\n              shouldCache: !this.isDev,\n            })\n          : {},\n        loadManifestFromRelativePath<Record<string, string>>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: `server/${SUBRESOURCE_INTEGRITY_MANIFEST}.json`,\n          handleMissing: true,\n          shouldCache: !this.isDev,\n        }),\n        this.isDev\n          ? ({} as any)\n          : loadManifestFromRelativePath<RequiredServerFilesManifest>({\n              projectDir,\n              distDir: this.distDir,\n              manifest: SERVER_FILES_MANIFEST,\n            }),\n        this.isDev\n          ? 'development'\n          : loadManifestFromRelativePath<any>({\n              projectDir,\n              distDir: this.distDir,\n              manifest: BUILD_ID_FILE,\n              skipParse: true,\n            }),\n        loadManifestFromRelativePath<any>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: DYNAMIC_CSS_MANIFEST,\n          handleMissing: true,\n        }),\n      ]\n\n      return {\n        buildId,\n        buildManifest,\n        routesManifest,\n        nextFontManifest,\n        prerenderManifest,\n        serverFilesManifest,\n        reactLoadableManifest,\n        clientReferenceManifest: (clientReferenceManifest as any)\n          ?.__RSC_MANIFEST?.[srcPage.replace(/%5F/g, '_')],\n        serverActionsManifest,\n        subresourceIntegrityManifest,\n        dynamicCssManifest,\n      }\n    }\n  }\n\n  public async loadCustomCacheHandlers(\n    req: IncomingMessage,\n    nextConfig: NextConfigComplete\n  ) {\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      const { cacheHandlers } = nextConfig.experimental\n      if (!cacheHandlers) return\n\n      // If we've already initialized the cache handlers interface, don't do it\n      // again.\n      if (!initializeCacheHandlers()) return\n\n      for (const [kind, handler] of Object.entries(cacheHandlers)) {\n        if (!handler) continue\n\n        const { formatDynamicImportPath } =\n          require('../../lib/format-dynamic-import-path') as typeof import('../../lib/format-dynamic-import-path')\n\n        const { join } = require('node:path') as typeof import('node:path')\n        const absoluteProjectDir =\n          getRequestMeta(req, 'projectDir') ||\n          join(process.cwd(), this.projectDir)\n\n        setCacheHandler(\n          kind,\n          interopDefault(\n            await dynamicImportEsmDefault(\n              formatDynamicImportPath(\n                `${absoluteProjectDir}/${this.distDir}`,\n                handler\n              )\n            )\n          )\n        )\n      }\n    }\n  }\n\n  public async getIncrementalCache(\n    req: IncomingMessage,\n    nextConfig: NextConfigComplete,\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n  ): Promise<IncrementalCache> {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      return (globalThis as any).__incrementalCache\n    } else {\n      let CacheHandler: any\n      const { cacheHandler } = nextConfig\n\n      if (cacheHandler) {\n        const { formatDynamicImportPath } =\n          require('../../lib/format-dynamic-import-path') as typeof import('../../lib/format-dynamic-import-path')\n\n        CacheHandler = interopDefault(\n          await dynamicImportEsmDefault(\n            formatDynamicImportPath(this.distDir, cacheHandler)\n          )\n        )\n      }\n      const { join } = require('node:path') as typeof import('node:path')\n      const projectDir =\n        getRequestMeta(req, 'projectDir') ||\n        join(process.cwd(), this.projectDir)\n\n      await this.loadCustomCacheHandlers(req, nextConfig)\n\n      // incremental-cache is request specific\n      // although can have shared caches in module scope\n      // per-cache handler\n      return new IncrementalCache({\n        fs: (\n          require('../lib/node-fs-methods') as typeof import('../lib/node-fs-methods')\n        ).nodeFs,\n        dev: this.isDev,\n        requestHeaders: req.headers,\n        allowedRevalidateHeaderKeys:\n          nextConfig.experimental.allowedRevalidateHeaderKeys,\n        minimalMode: getRequestMeta(req, 'minimalMode'),\n        serverDistDir: `${projectDir}/${this.distDir}/server`,\n        fetchCacheKeyPrefix: nextConfig.experimental.fetchCacheKeyPrefix,\n        maxMemoryCacheSize: nextConfig.cacheMaxMemorySize,\n        flushToDisk: nextConfig.experimental.isrFlushToDisk,\n        getPrerenderManifest: () => prerenderManifest,\n        CurCacheHandler: CacheHandler,\n      })\n    }\n  }\n\n  public async onRequestError(\n    req: IncomingMessage | BaseNextRequest,\n    err: unknown,\n    errorContext: RequestErrorContext,\n    routerServerContext?: RouterServerContext[string]\n  ) {\n    if (routerServerContext?.logErrorWithOriginalStack) {\n      routerServerContext.logErrorWithOriginalStack(err, 'app-dir')\n    } else {\n      console.error(err)\n    }\n    await this.instrumentationOnRequestError(\n      req,\n      err,\n      {\n        path: req.url || '/',\n        headers: req.headers,\n        method: req.method || 'GET',\n      },\n      errorContext\n    )\n  }\n\n  public async prepare(\n    req: IncomingMessage | BaseNextRequest,\n    res: ServerResponse | null,\n    {\n      srcPage,\n      multiZoneDraftMode,\n    }: {\n      srcPage: string\n      multiZoneDraftMode?: boolean\n    }\n  ): Promise<\n    | {\n        buildId: string\n        locale?: string\n        locales?: readonly string[]\n        defaultLocale?: string\n        query: ParsedUrlQuery\n        originalQuery: ParsedUrlQuery\n        originalPathname: string\n        params?: ParsedUrlQuery\n        parsedUrl: UrlWithParsedQuery\n        previewData: PreviewData\n        pageIsDynamic: boolean\n        isDraftMode: boolean\n        resolvedPathname: string\n        isNextDataRequest: boolean\n        buildManifest: DeepReadonly<BuildManifest>\n        nextFontManifest: DeepReadonly<NextFontManifest>\n        serverFilesManifest: DeepReadonly<RequiredServerFilesManifest>\n        reactLoadableManifest: DeepReadonly<ReactLoadableManifest>\n        routesManifest: DeepReadonly<DevRoutesManifest>\n        prerenderManifest: DeepReadonly<PrerenderManifest>\n        // we can't pull in the client reference type or it causes issues with\n        // our pre-compiled types\n        clientReferenceManifest?: any\n        serverActionsManifest?: any\n        dynamicCssManifest?: any\n        subresourceIntegrityManifest?: DeepReadonly<Record<string, string>>\n        isOnDemandRevalidate: boolean\n        revalidateOnlyGenerated: boolean\n        nextConfig: NextConfigComplete\n        routerServerContext?: RouterServerContext[string]\n      }\n    | undefined\n  > {\n    let projectDir: string | undefined\n\n    // edge runtime handles loading instrumentation at the edge adapter level\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      const { join, relative } =\n        require('node:path') as typeof import('node:path')\n\n      projectDir =\n        getRequestMeta(req, 'projectDir') ||\n        join(process.cwd(), this.projectDir)\n\n      const absoluteDistDir = getRequestMeta(req, 'distDir')\n\n      if (absoluteDistDir) {\n        this.distDir = relative(projectDir, absoluteDistDir)\n      }\n      const { ensureInstrumentationRegistered } = await import(\n        '../lib/router-utils/instrumentation-globals.external'\n      )\n      // ensure instrumentation is registered and pass\n      // onRequestError below\n      ensureInstrumentationRegistered(projectDir, this.distDir)\n    }\n    const manifests = await this.loadManifests(srcPage, projectDir)\n    const { routesManifest, prerenderManifest, serverFilesManifest } = manifests\n\n    const { basePath, i18n, rewrites } = routesManifest\n\n    if (basePath) {\n      req.url = removePathPrefix(req.url || '/', basePath)\n    }\n\n    const parsedUrl = parseReqUrl(req.url || '/')\n    // if we couldn't parse the URL we can't continue\n    if (!parsedUrl) {\n      return\n    }\n    let isNextDataRequest = false\n\n    if (pathHasPrefix(parsedUrl.pathname || '/', '/_next/data')) {\n      isNextDataRequest = true\n      parsedUrl.pathname = normalizeDataPath(parsedUrl.pathname || '/')\n    }\n    let originalPathname = parsedUrl.pathname || '/'\n    const originalQuery = { ...parsedUrl.query }\n    const pageIsDynamic = isDynamicRoute(srcPage)\n\n    let localeResult: PathLocale | undefined\n    let detectedLocale: string | undefined\n\n    if (i18n) {\n      localeResult = normalizeLocalePath(\n        parsedUrl.pathname || '/',\n        i18n.locales\n      )\n\n      if (localeResult.detectedLocale) {\n        req.url = `${localeResult.pathname}${parsedUrl.search}`\n        originalPathname = localeResult.pathname\n\n        if (!detectedLocale) {\n          detectedLocale = localeResult.detectedLocale\n        }\n      }\n    }\n\n    const serverUtils = getServerUtils({\n      page: srcPage,\n      i18n,\n      basePath,\n      rewrites,\n      pageIsDynamic,\n      trailingSlash: process.env.__NEXT_TRAILING_SLASH as any as boolean,\n      caseSensitive: Boolean(routesManifest.caseSensitive),\n    })\n\n    const domainLocale = detectDomainLocale(\n      i18n?.domains,\n      getHostname(parsedUrl, req.headers),\n      detectedLocale\n    )\n    addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n    const defaultLocale = domainLocale?.defaultLocale || i18n?.defaultLocale\n\n    // Ensure parsedUrl.pathname includes locale before processing\n    // rewrites or they won't match correctly.\n    if (defaultLocale && !detectedLocale) {\n      parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname === '/' ? '' : parsedUrl.pathname}`\n    }\n    const locale =\n      getRequestMeta(req, 'locale') || detectedLocale || defaultLocale\n\n    const rewriteParamKeys = Object.keys(\n      serverUtils.handleRewrites(req, parsedUrl)\n    )\n\n    // after processing rewrites we want to remove locale\n    // from parsedUrl pathname\n    if (i18n) {\n      parsedUrl.pathname = normalizeLocalePath(\n        parsedUrl.pathname || '/',\n        i18n.locales\n      ).pathname\n    }\n\n    let params: Record<string, undefined | string | string[]> | undefined =\n      getRequestMeta(req, 'params')\n\n    // attempt parsing from pathname\n    if (!params && serverUtils.dynamicRouteMatcher) {\n      const paramsMatch = serverUtils.dynamicRouteMatcher(\n        normalizeDataPath(localeResult?.pathname || parsedUrl.pathname || '/')\n      )\n      const paramsResult = serverUtils.normalizeDynamicRouteParams(\n        paramsMatch || {},\n        true\n      )\n\n      if (paramsResult.hasValidParams) {\n        params = paramsResult.params\n      }\n    }\n\n    // Local \"next start\" expects the routing parsed query values\n    // to not be present in the URL although when deployed proxies\n    // will add query values from resolving the routes to pass to function.\n\n    // TODO: do we want to change expectations for \"next start\"\n    // to include these query values in the URL which affects asPath\n    // but would match deployed behavior, e.g. a rewrite from middleware\n    // that adds a query param would be in asPath as query but locally\n    // it won't be in the asPath but still available in the query object\n    const query = getRequestMeta(req, 'query') || {\n      ...parsedUrl.query,\n    }\n\n    const routeParamKeys = new Set<string>()\n    const combinedParamKeys = []\n\n    // we don't include rewriteParamKeys in the combinedParamKeys\n    // for app router since the searchParams is populated from the\n    // URL so we don't want to strip the rewrite params from the URL\n    // so that searchParams can include them\n    if (!this.isAppRouter) {\n      for (const key of [\n        ...rewriteParamKeys,\n        ...Object.keys(serverUtils.defaultRouteMatches || {}),\n      ]) {\n        // We only want to filter rewrite param keys from the URL\n        // if they are matches from the URL e.g. the key/value matches\n        // before and after applying the rewrites /:path for /hello and\n        // { path: 'hello' } but not for { path: 'another' } and /hello\n        // TODO: we should prefix rewrite param keys the same as we do\n        // for dynamic routes so we can identify them properly\n        const originalValue = Array.isArray(originalQuery[key])\n          ? originalQuery[key].join('')\n          : originalQuery[key]\n\n        const queryValue = Array.isArray(query[key])\n          ? query[key].join('')\n          : query[key]\n\n        if (!(key in originalQuery) || originalValue === queryValue) {\n          combinedParamKeys.push(key)\n        }\n      }\n    }\n\n    serverUtils.normalizeCdnUrl(req, combinedParamKeys)\n    serverUtils.normalizeQueryParams(query, routeParamKeys)\n    serverUtils.filterInternalQuery(originalQuery, combinedParamKeys)\n\n    if (pageIsDynamic) {\n      const queryResult = serverUtils.normalizeDynamicRouteParams(query, true)\n\n      const paramsResult = serverUtils.normalizeDynamicRouteParams(\n        params || {},\n        true\n      )\n      const paramsToInterpolate: ParsedUrlQuery =\n        paramsResult.hasValidParams && params\n          ? params\n          : queryResult.hasValidParams\n            ? query\n            : {}\n\n      req.url = serverUtils.interpolateDynamicPath(\n        req.url || '/',\n        paramsToInterpolate\n      )\n      parsedUrl.pathname = serverUtils.interpolateDynamicPath(\n        parsedUrl.pathname || '/',\n        paramsToInterpolate\n      )\n      originalPathname = serverUtils.interpolateDynamicPath(\n        originalPathname,\n        paramsToInterpolate\n      )\n\n      // try pulling from query if valid\n      if (!params) {\n        if (queryResult.hasValidParams) {\n          params = Object.assign({}, queryResult.params)\n\n          // If we pulled from query remove it so it's\n          // only in params\n          for (const key in serverUtils.defaultRouteMatches) {\n            delete query[key]\n          }\n        } else {\n          // use final params from URL matching\n          const paramsMatch = serverUtils.dynamicRouteMatcher?.(\n            normalizeDataPath(\n              localeResult?.pathname || parsedUrl.pathname || '/'\n            )\n          )\n          // we don't normalize these as they are allowed to be\n          // the literal slug matches here e.g. /blog/[slug]\n          // actually being requested\n          if (paramsMatch) {\n            params = Object.assign({}, paramsMatch)\n          }\n        }\n      }\n    }\n\n    // Remove any normalized params from the query if they\n    // weren't present as non-prefixed query key e.g.\n    // ?search=1&nxtPsearch=hello we don't delete search\n    for (const key of routeParamKeys) {\n      if (!(key in originalQuery)) {\n        delete query[key]\n      }\n    }\n\n    const { isOnDemandRevalidate, revalidateOnlyGenerated } =\n      checkIsOnDemandRevalidate(req, prerenderManifest.preview)\n\n    let isDraftMode = false\n    let previewData: PreviewData\n\n    // preview data relies on non-edge utils\n    if (process.env.NEXT_RUNTIME !== 'edge' && res) {\n      const { tryGetPreviewData } =\n        require('../api-utils/node/try-get-preview-data') as typeof import('../api-utils/node/try-get-preview-data')\n\n      previewData = tryGetPreviewData(\n        req,\n        res,\n        prerenderManifest.preview,\n        Boolean(multiZoneDraftMode)\n      )\n      isDraftMode = previewData !== false\n    }\n\n    const routerServerContext =\n      routerServerGlobal[RouterServerContextSymbol]?.[this.projectDir]\n    const nextConfig =\n      routerServerContext?.nextConfig || serverFilesManifest.config\n\n    const normalizedSrcPage = normalizeAppPath(srcPage)\n    let resolvedPathname =\n      getRequestMeta(req, 'rewroteURL') || normalizedSrcPage\n\n    if (isDynamicRoute(resolvedPathname) && params) {\n      resolvedPathname = serverUtils.interpolateDynamicPath(\n        resolvedPathname,\n        params\n      )\n    }\n\n    if (resolvedPathname === '/index') {\n      resolvedPathname = '/'\n    }\n    try {\n      resolvedPathname = decodePathParams(resolvedPathname)\n    } catch (_) {}\n\n    resolvedPathname = removeTrailingSlash(resolvedPathname)\n\n    return {\n      query,\n      originalQuery,\n      originalPathname,\n      params,\n      parsedUrl,\n      locale,\n      isNextDataRequest,\n      locales: i18n?.locales,\n      defaultLocale,\n      isDraftMode,\n      previewData,\n      pageIsDynamic,\n      resolvedPathname,\n      isOnDemandRevalidate,\n      revalidateOnlyGenerated,\n      ...manifests,\n      serverActionsManifest: manifests.serverActionsManifest,\n      clientReferenceManifest: manifests.clientReferenceManifest,\n      nextConfig,\n      routerServerContext,\n    }\n  }\n\n  public getResponseCache(req: IncomingMessage) {\n    if (!this.responseCache) {\n      const minimalMode = getRequestMeta(req, 'minimalMode') ?? false\n      this.responseCache = new ResponseCache(minimalMode)\n    }\n    return this.responseCache\n  }\n\n  public async handleResponse({\n    req,\n    nextConfig,\n    cacheKey,\n    routeKind,\n    isFallback,\n    prerenderManifest,\n    isRoutePPREnabled,\n    isOnDemandRevalidate,\n    revalidateOnlyGenerated,\n    responseGenerator,\n    waitUntil,\n  }: {\n    req: IncomingMessage\n    nextConfig: NextConfigComplete\n    cacheKey: string | null\n    routeKind: RouteKind\n    isFallback?: boolean\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n    isRoutePPREnabled?: boolean\n    isOnDemandRevalidate?: boolean\n    revalidateOnlyGenerated?: boolean\n    responseGenerator: ResponseGenerator\n    waitUntil?: (prom: Promise<any>) => void\n  }) {\n    const responseCache = this.getResponseCache(req)\n    const cacheEntry = await responseCache.get(cacheKey, responseGenerator, {\n      routeKind,\n      isFallback,\n      isRoutePPREnabled,\n      isOnDemandRevalidate,\n      isPrefetch: req.headers.purpose === 'prefetch',\n      incrementalCache: await this.getIncrementalCache(\n        req,\n        nextConfig,\n        prerenderManifest\n      ),\n      waitUntil,\n    })\n\n    if (!cacheEntry) {\n      if (\n        cacheKey &&\n        // revalidate only generated can bail even if cacheKey is provided\n        !(isOnDemandRevalidate && revalidateOnlyGenerated)\n      ) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n    }\n    return cacheEntry\n  }\n}\n"], "names": ["RouteModule", "dynamicImportEsmDefault", "id", "then", "mod", "default", "constructor", "userland", "definition", "distDir", "projectDir", "isDev", "process", "env", "NODE_ENV", "instrumentationOnRequestError", "req", "args", "NEXT_RUNTIME", "getEdgeInstrumentationModule", "instrumentation", "onRequestError", "join", "require", "absoluteProjectDir", "getRequestMeta", "cwd", "loadManifests", "srcPage", "self", "getEdgePreviewProps", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildId", "__NEXT_BUILD_ID", "buildManifest", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "prerenderManifest", "routes", "dynamicRoutes", "notFoundRoutes", "version", "preview", "routesManifest", "caseSensitive", "Boolean", "__NEXT_CASE_SENSITIVE_ROUTES", "basePath", "__NEXT_BASE_PATH", "rewrites", "__NEXT_REWRITES", "beforeFiles", "afterFiles", "fallback", "redirects", "headers", "i18n", "__NEXT_I18N_CONFIG", "skipMiddlewareUrlNormalize", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "serverFilesManifest", "config", "globalThis", "nextConfig", "clientReferenceManifest", "__RSC_MANIFEST", "serverActionsManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "__SUBRESOURCE_INTEGRITY_MANIFEST", "dynamicCssManifest", "__DYNAMIC_CSS_MANIFEST", "Error", "loadManifestFromRelativePath", "normalizedPagePath", "normalizePagePath", "manifest", "ROUTES_MANIFEST", "shouldCache", "PRERENDER_MANIFEST", "BUILD_MANIFEST", "TURBOPACK", "isAppRouter", "REACT_LOADABLE_MANIFEST", "handleMissing", "NEXT_FONT_MANIFEST", "isStaticMetadataRoute", "useEval", "replace", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "SERVER_FILES_MANIFEST", "BUILD_ID_FILE", "<PERSON><PERSON><PERSON><PERSON>", "DYNAMIC_CSS_MANIFEST", "loadCustomCacheHandlers", "cacheHandlers", "experimental", "initializeCacheHandlers", "kind", "handler", "Object", "entries", "formatDynamicImportPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "getIncrementalCache", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cache<PERSON><PERSON><PERSON>", "IncrementalCache", "fs", "nodeFs", "dev", "requestHeaders", "allowedRevalidateHeaderKeys", "minimalMode", "serverDistDir", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "isrFlushToDisk", "getPrerenderManifest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "errorContext", "routerServerContext", "logErrorWithOriginalStack", "console", "error", "path", "url", "method", "prepare", "res", "multiZoneDraftMode", "routerServerGlobal", "relative", "absoluteDistDir", "ensureInstrumentationRegistered", "manifests", "removePathPrefix", "parsedUrl", "parseReqUrl", "isNextDataRequest", "pathHasPrefix", "pathname", "normalizeDataPath", "originalPathname", "originalQuery", "query", "pageIsDynamic", "isDynamicRoute", "localeResult", "detectedLocale", "normalizeLocalePath", "locales", "search", "serverUtils", "getServerUtils", "page", "trailingSlash", "__NEXT_TRAILING_SLASH", "domainLocale", "detectDomainLocale", "domains", "getHostname", "addRequestMeta", "defaultLocale", "locale", "rewriteParamKeys", "keys", "handleRewrites", "params", "dynamicRouteMatcher", "paramsMatch", "paramsResult", "normalizeDynamicRouteParams", "hasValidParams", "routeParamKeys", "Set", "combinedParamKeys", "key", "defaultRouteMatches", "originalValue", "Array", "isArray", "queryValue", "push", "normalizeCdnUrl", "normalizeQueryParams", "filterInternalQuery", "query<PERSON><PERSON>ult", "paramsToInterpolate", "interpolateDynamicPath", "assign", "isOnDemandRevalidate", "revalidateOnlyGenerated", "checkIsOnDemandRevalidate", "isDraftMode", "previewData", "tryGetPreviewData", "RouterServerContextSymbol", "normalizedSrcPage", "normalizeAppPath", "resolvedPathname", "decodePathParams", "_", "removeTrailingSlash", "getResponseCache", "responseCache", "ResponseCache", "handleResponse", "cache<PERSON>ey", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "responseGenerator", "waitUntil", "cacheEntry", "get", "isPrefetch", "purpose", "incrementalCache"], "mappings": ";;;;+BAkGsBA;;;eAAAA;;;2BAxEf;qBACqB;qCAIrB;uBACwB;kCACE;6BACF;oCACI;6BACP;0BACc;mCAKR;+BACJ;6BACiB;mCACb;iCACI;kCACL;0BACwB;gCAC1B;sEAIuB;0BACrB;qCAK1B;kCAC0B;qCACG;;;;;;AA4BpC,MAAMC,0BAA0B,CAACC,KAC/B,MAAM,CAAC,uBAAuB,GAAG,yBAAyB,GAAGA,IAAIC,IAAI,CACnE,CAACC,MAAQA,IAAIC,OAAO,IAAID;AAOrB,MAAeJ;IA4BpBM,YAAY,EACVC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,UAAU,EACe,CAAE;QAC3B,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,UAAU,GAAGA;QAClB,IAAI,CAACG,KAAK,GAAGC,QAAQC,GAAG,CAACC,QAAQ,KAAK;QACtC,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACC,UAAU,GAAGA;IACpB;IAEA,MAAaK,8BACXC,GAAsC,EACtC,GAAGC,IAA+C,EAClD;QACA,IAAIL,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,MAAM,EAAEC,4BAA4B,EAAE,GAAG,MAAM,MAAM,CAAC;YACtD,MAAMC,kBAAkB,MAAMD;YAE9B,IAAIC,iBAAiB;gBACnB,OAAMA,gBAAgBC,cAAc,oBAA9BD,gBAAgBC,cAAc,MAA9BD,oBAAoCH;YAC5C;QACF,OAAO;YACL,MAAM,EAAEK,IAAI,EAAE,GAAGC,QAAQ;YACzB,MAAMC,qBACJC,IAAAA,2BAAc,EAACT,KAAK,iBACpBM,KAAKV,QAAQc,GAAG,IAAI,IAAI,CAAChB,UAAU;YAErC,MAAM,EAAEK,6BAA6B,EAAE,GAAG,MAAM,MAAM,CACpD;YAGF,OAAOA,8BACLS,oBACA,IAAI,CAACf,OAAO,KACTQ;QAEP;IACF;IAEQU,cACNC,OAAe,EACflB,UAAmB,EAanB;QACA,IAAIE,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;gBAuCZW;YAtC3B,MAAM,EAAEC,mBAAmB,EAAE,GAC3BP,QAAQ;YAEV,MAAMQ,iBAAiB,CAACC,MACtBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;YAE1B,OAAO;gBACLC,SAASxB,QAAQC,GAAG,CAACwB,eAAe,IAAI;gBACxCC,eAAeT,KAAKU,gBAAgB;gBACpCC,uBAAuBT,eAAeF,KAAKY,yBAAyB;gBACpEC,kBAAkBX,eAAeF,KAAKc,oBAAoB;gBAC1DC,mBAAmB;oBACjBC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAAS;oBACTC,SAASnB;gBACX;gBACAoB,gBAAgB;oBACdF,SAAS;oBACTG,eAAeC,QAAQxC,QAAQC,GAAG,CAACwC,4BAA4B;oBAC/DC,UAAU1C,QAAQC,GAAG,CAAC0C,gBAAgB,IAAI;oBAC1CC,UAAU,AAAC5C,QAAQC,GAAG,CAAC4C,eAAe,IAAY;wBAChDC,aAAa,EAAE;wBACfC,YAAY,EAAE;wBACdC,UAAU,EAAE;oBACd;oBACAC,WAAW,EAAE;oBACbC,SAAS,EAAE;oBACXC,MACE,AAACnD,QAAQC,GAAG,CAACmD,kBAAkB,IAA0B7B;oBAC3D8B,4BAA4Bb,QAC1BxC,QAAQC,GAAG,CAACqD,kCAAkC;gBAElD;gBACAC,qBAAqB;oBACnBC,QAAQ,AAACC,WAAmBC,UAAU,IAAI,CAAC;gBAC7C;gBACAC,uBAAuB,GAAE1C,uBAAAA,KAAK2C,cAAc,qBAAnB3C,oBAAqB,CAACD,QAAQ;gBACvD6C,uBAAuB1C,eAAeF,KAAK6C,qBAAqB;gBAChEC,8BAA8B5C,eAC5BF,KAAK+C,gCAAgC;gBAEvCC,oBAAoB9C,eAAeF,KAAKiD,sBAAsB;YAChE;QACF,OAAO;gBA+GsB;YA9G3B,IAAI,CAACpE,YAAY;gBACf,MAAM,qBAA+D,CAA/D,IAAIqE,MAAM,uDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8D;YACtE;YACA,MAAM,EAAEC,4BAA4B,EAAE,GACpCzD,QAAQ;YACV,MAAM0D,qBAAqBC,IAAAA,oCAAiB,EAACtD;YAE7C,MAAM,CACJsB,gBACAN,mBACAN,eACAE,uBACAE,kBACA6B,yBACAE,uBACAE,8BACAR,qBACA/B,SACAyC,mBACD,GAAG;gBACFG,6BAAgD;oBAC9CtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAUC,0BAAe;oBACzBC,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B;gBACAqE,6BAAgD;oBAC9CtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAUG,6BAAkB;oBAC5BD,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B;gBACAqE,6BAA4C;oBAC1CtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAUI,yBAAc;oBACxBF,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B;gBACAqE,6BAAoD;oBAClDtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAUvE,QAAQC,GAAG,CAAC2E,SAAS,GAC3B,CAAC,OAAO,EAAE,IAAI,CAACC,WAAW,GAAG,QAAQ,UAAUR,mBAAmB,CAAC,EAAES,kCAAuB,EAAE,GAC9FA,kCAAuB;oBAC3BC,eAAe;oBACfN,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B;gBACAqE,6BAA+C;oBAC7CtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAU,CAAC,OAAO,EAAES,6BAAkB,CAAC,KAAK,CAAC;oBAC7CP,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B;gBACA,IAAI,CAAC8E,WAAW,IAAI,CAACI,IAAAA,sCAAqB,EAACjE,WACvCoD,6BAA6B;oBAC3BvE,SAAS,IAAI,CAACA,OAAO;oBACrBC;oBACAoF,SAAS;oBACTH,eAAe;oBACfR,UAAU,CAAC,UAAU,EAAEvD,QAAQmE,OAAO,CAAC,QAAQ,OAAO,MAAMC,oCAAyB,CAAC,GAAG,CAAC;oBAC1FX,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B,KACAwB;gBACJ,IAAI,CAACsD,WAAW,GACZT,6BAAkC;oBAChCvE,SAAS,IAAI,CAACA,OAAO;oBACrBC;oBACAyE,UAAU,CAAC,OAAO,EAAEc,oCAAyB,CAAC,KAAK,CAAC;oBACpDN,eAAe;oBACfN,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B,KACA,CAAC;gBACLqE,6BAAqD;oBACnDtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAU,CAAC,OAAO,EAAEe,yCAA8B,CAAC,KAAK,CAAC;oBACzDP,eAAe;oBACfN,aAAa,CAAC,IAAI,CAAC1E,KAAK;gBAC1B;gBACA,IAAI,CAACA,KAAK,GACL,CAAC,IACFqE,6BAA0D;oBACxDtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAUgB,gCAAqB;gBACjC;gBACJ,IAAI,CAACxF,KAAK,GACN,gBACAqE,6BAAkC;oBAChCtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAUiB,wBAAa;oBACvBC,WAAW;gBACb;gBACJrB,6BAAkC;oBAChCtE;oBACAD,SAAS,IAAI,CAACA,OAAO;oBACrB0E,UAAUmB,+BAAoB;oBAC9BX,eAAe;gBACjB;aACD;YAED,OAAO;gBACLvD;gBACAE;gBACAY;gBACAR;gBACAE;gBACAuB;gBACA3B;gBACA+B,uBAAuB,EAAGA,4CAAD,0CAAA,AAACA,wBACtBC,cAAc,qBADO,uCACL,CAAC5C,QAAQmE,OAAO,CAAC,QAAQ,KAAK;gBAClDtB;gBACAE;gBACAE;YACF;QACF;IACF;IAEA,MAAa0B,wBACXvF,GAAoB,EACpBsD,UAA8B,EAC9B;QACA,IAAI1D,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,MAAM,EAAEsF,aAAa,EAAE,GAAGlC,WAAWmC,YAAY;YACjD,IAAI,CAACD,eAAe;YAEpB,yEAAyE;YACzE,SAAS;YACT,IAAI,CAACE,IAAAA,iCAAuB,KAAI;YAEhC,KAAK,MAAM,CAACC,MAAMC,QAAQ,IAAIC,OAAOC,OAAO,CAACN,eAAgB;gBAC3D,IAAI,CAACI,SAAS;gBAEd,MAAM,EAAEG,uBAAuB,EAAE,GAC/BxF,QAAQ;gBAEV,MAAM,EAAED,IAAI,EAAE,GAAGC,QAAQ;gBACzB,MAAMC,qBACJC,IAAAA,2BAAc,EAACT,KAAK,iBACpBM,KAAKV,QAAQc,GAAG,IAAI,IAAI,CAAChB,UAAU;gBAErCsG,IAAAA,yBAAe,EACbL,MACAM,IAAAA,8BAAc,EACZ,MAAMhH,wBACJ8G,wBACE,GAAGvF,mBAAmB,CAAC,EAAE,IAAI,CAACf,OAAO,EAAE,EACvCmG;YAKV;QACF;IACF;IAEA,MAAaM,oBACXlG,GAAoB,EACpBsD,UAA8B,EAC9B1B,iBAAkD,EACvB;QAC3B,IAAIhC,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,OAAO,AAACmD,WAAmB8C,kBAAkB;QAC/C,OAAO;YACL,IAAIC;YACJ,MAAM,EAAEC,YAAY,EAAE,GAAG/C;YAEzB,IAAI+C,cAAc;gBAChB,MAAM,EAAEN,uBAAuB,EAAE,GAC/BxF,QAAQ;gBAEV6F,eAAeH,IAAAA,8BAAc,EAC3B,MAAMhH,wBACJ8G,wBAAwB,IAAI,CAACtG,OAAO,EAAE4G;YAG5C;YACA,MAAM,EAAE/F,IAAI,EAAE,GAAGC,QAAQ;YACzB,MAAMb,aACJe,IAAAA,2BAAc,EAACT,KAAK,iBACpBM,KAAKV,QAAQc,GAAG,IAAI,IAAI,CAAChB,UAAU;YAErC,MAAM,IAAI,CAAC6F,uBAAuB,CAACvF,KAAKsD;YAExC,wCAAwC;YACxC,kDAAkD;YAClD,oBAAoB;YACpB,OAAO,IAAIgD,kCAAgB,CAAC;gBAC1BC,IAAI,AACFhG,QAAQ,0BACRiG,MAAM;gBACRC,KAAK,IAAI,CAAC9G,KAAK;gBACf+G,gBAAgB1G,IAAI8C,OAAO;gBAC3B6D,6BACErD,WAAWmC,YAAY,CAACkB,2BAA2B;gBACrDC,aAAanG,IAAAA,2BAAc,EAACT,KAAK;gBACjC6G,eAAe,GAAGnH,WAAW,CAAC,EAAE,IAAI,CAACD,OAAO,CAAC,OAAO,CAAC;gBACrDqH,qBAAqBxD,WAAWmC,YAAY,CAACqB,mBAAmB;gBAChEC,oBAAoBzD,WAAW0D,kBAAkB;gBACjDC,aAAa3D,WAAWmC,YAAY,CAACyB,cAAc;gBACnDC,sBAAsB,IAAMvF;gBAC5BwF,iBAAiBhB;YACnB;QACF;IACF;IAEA,MAAa/F,eACXL,GAAsC,EACtCqH,GAAY,EACZC,YAAiC,EACjCC,mBAAiD,EACjD;QACA,IAAIA,uCAAAA,oBAAqBC,yBAAyB,EAAE;YAClDD,oBAAoBC,yBAAyB,CAACH,KAAK;QACrD,OAAO;YACLI,QAAQC,KAAK,CAACL;QAChB;QACA,MAAM,IAAI,CAACtH,6BAA6B,CACtCC,KACAqH,KACA;YACEM,MAAM3H,IAAI4H,GAAG,IAAI;YACjB9E,SAAS9C,IAAI8C,OAAO;YACpB+E,QAAQ7H,IAAI6H,MAAM,IAAI;QACxB,GACAP;IAEJ;IAEA,MAAaQ,QACX9H,GAAsC,EACtC+H,GAA0B,EAC1B,EACEnH,OAAO,EACPoH,kBAAkB,EAInB,EAmCD;YAkQEC;QAjQF,IAAIvI;QAEJ,yEAAyE;QACzE,IAAIE,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,MAAM,EAAEI,IAAI,EAAE4H,QAAQ,EAAE,GACtB3H,QAAQ;YAEVb,aACEe,IAAAA,2BAAc,EAACT,KAAK,iBACpBM,KAAKV,QAAQc,GAAG,IAAI,IAAI,CAAChB,UAAU;YAErC,MAAMyI,kBAAkB1H,IAAAA,2BAAc,EAACT,KAAK;YAE5C,IAAImI,iBAAiB;gBACnB,IAAI,CAAC1I,OAAO,GAAGyI,SAASxI,YAAYyI;YACtC;YACA,MAAM,EAAEC,+BAA+B,EAAE,GAAG,MAAM,MAAM,CACtD;YAEF,gDAAgD;YAChD,uBAAuB;YACvBA,gCAAgC1I,YAAY,IAAI,CAACD,OAAO;QAC1D;QACA,MAAM4I,YAAY,MAAM,IAAI,CAAC1H,aAAa,CAACC,SAASlB;QACpD,MAAM,EAAEwC,cAAc,EAAEN,iBAAiB,EAAEuB,mBAAmB,EAAE,GAAGkF;QAEnE,MAAM,EAAE/F,QAAQ,EAAES,IAAI,EAAEP,QAAQ,EAAE,GAAGN;QAErC,IAAII,UAAU;YACZtC,IAAI4H,GAAG,GAAGU,IAAAA,kCAAgB,EAACtI,IAAI4H,GAAG,IAAI,KAAKtF;QAC7C;QAEA,MAAMiG,YAAYC,IAAAA,gBAAW,EAACxI,IAAI4H,GAAG,IAAI;QACzC,iDAAiD;QACjD,IAAI,CAACW,WAAW;YACd;QACF;QACA,IAAIE,oBAAoB;QAExB,IAAIC,IAAAA,4BAAa,EAACH,UAAUI,QAAQ,IAAI,KAAK,gBAAgB;YAC3DF,oBAAoB;YACpBF,UAAUI,QAAQ,GAAGC,IAAAA,oCAAiB,EAACL,UAAUI,QAAQ,IAAI;QAC/D;QACA,IAAIE,mBAAmBN,UAAUI,QAAQ,IAAI;QAC7C,MAAMG,gBAAgB;YAAE,GAAGP,UAAUQ,KAAK;QAAC;QAC3C,MAAMC,gBAAgBC,IAAAA,qBAAc,EAACrI;QAErC,IAAIsI;QACJ,IAAIC;QAEJ,IAAIpG,MAAM;YACRmG,eAAeE,IAAAA,wCAAmB,EAChCb,UAAUI,QAAQ,IAAI,KACtB5F,KAAKsG,OAAO;YAGd,IAAIH,aAAaC,cAAc,EAAE;gBAC/BnJ,IAAI4H,GAAG,GAAG,GAAGsB,aAAaP,QAAQ,GAAGJ,UAAUe,MAAM,EAAE;gBACvDT,mBAAmBK,aAAaP,QAAQ;gBAExC,IAAI,CAACQ,gBAAgB;oBACnBA,iBAAiBD,aAAaC,cAAc;gBAC9C;YACF;QACF;QAEA,MAAMI,cAAcC,IAAAA,2BAAc,EAAC;YACjCC,MAAM7I;YACNmC;YACAT;YACAE;YACAwG;YACAU,eAAe9J,QAAQC,GAAG,CAAC8J,qBAAqB;YAChDxH,eAAeC,QAAQF,eAAeC,aAAa;QACrD;QAEA,MAAMyH,eAAeC,IAAAA,sCAAkB,EACrC9G,wBAAAA,KAAM+G,OAAO,EACbC,IAAAA,wBAAW,EAACxB,WAAWvI,IAAI8C,OAAO,GAClCqG;QAEFa,IAAAA,2BAAc,EAAChK,KAAK,kBAAkBoC,QAAQwH;QAE9C,MAAMK,gBAAgBL,CAAAA,gCAAAA,aAAcK,aAAa,MAAIlH,wBAAAA,KAAMkH,aAAa;QAExE,8DAA8D;QAC9D,0CAA0C;QAC1C,IAAIA,iBAAiB,CAACd,gBAAgB;YACpCZ,UAAUI,QAAQ,GAAG,CAAC,CAAC,EAAEsB,gBAAgB1B,UAAUI,QAAQ,KAAK,MAAM,KAAKJ,UAAUI,QAAQ,EAAE;QACjG;QACA,MAAMuB,SACJzJ,IAAAA,2BAAc,EAACT,KAAK,aAAamJ,kBAAkBc;QAErD,MAAME,mBAAmBtE,OAAOuE,IAAI,CAClCb,YAAYc,cAAc,CAACrK,KAAKuI;QAGlC,qDAAqD;QACrD,0BAA0B;QAC1B,IAAIxF,MAAM;YACRwF,UAAUI,QAAQ,GAAGS,IAAAA,wCAAmB,EACtCb,UAAUI,QAAQ,IAAI,KACtB5F,KAAKsG,OAAO,EACZV,QAAQ;QACZ;QAEA,IAAI2B,SACF7J,IAAAA,2BAAc,EAACT,KAAK;QAEtB,gCAAgC;QAChC,IAAI,CAACsK,UAAUf,YAAYgB,mBAAmB,EAAE;YAC9C,MAAMC,cAAcjB,YAAYgB,mBAAmB,CACjD3B,IAAAA,oCAAiB,EAACM,CAAAA,gCAAAA,aAAcP,QAAQ,KAAIJ,UAAUI,QAAQ,IAAI;YAEpE,MAAM8B,eAAelB,YAAYmB,2BAA2B,CAC1DF,eAAe,CAAC,GAChB;YAGF,IAAIC,aAAaE,cAAc,EAAE;gBAC/BL,SAASG,aAAaH,MAAM;YAC9B;QACF;QAEA,6DAA6D;QAC7D,8DAA8D;QAC9D,uEAAuE;QAEvE,2DAA2D;QAC3D,gEAAgE;QAChE,oEAAoE;QACpE,kEAAkE;QAClE,oEAAoE;QACpE,MAAMvB,QAAQtI,IAAAA,2BAAc,EAACT,KAAK,YAAY;YAC5C,GAAGuI,UAAUQ,KAAK;QACpB;QAEA,MAAM6B,iBAAiB,IAAIC;QAC3B,MAAMC,oBAAoB,EAAE;QAE5B,6DAA6D;QAC7D,8DAA8D;QAC9D,gEAAgE;QAChE,wCAAwC;QACxC,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE;YACrB,KAAK,MAAMsG,OAAO;mBACbZ;mBACAtE,OAAOuE,IAAI,CAACb,YAAYyB,mBAAmB,IAAI,CAAC;aACpD,CAAE;gBACD,yDAAyD;gBACzD,8DAA8D;gBAC9D,+DAA+D;gBAC/D,+DAA+D;gBAC/D,8DAA8D;gBAC9D,sDAAsD;gBACtD,MAAMC,gBAAgBC,MAAMC,OAAO,CAACrC,aAAa,CAACiC,IAAI,IAClDjC,aAAa,CAACiC,IAAI,CAACzK,IAAI,CAAC,MACxBwI,aAAa,CAACiC,IAAI;gBAEtB,MAAMK,aAAaF,MAAMC,OAAO,CAACpC,KAAK,CAACgC,IAAI,IACvChC,KAAK,CAACgC,IAAI,CAACzK,IAAI,CAAC,MAChByI,KAAK,CAACgC,IAAI;gBAEd,IAAI,CAAEA,CAAAA,OAAOjC,aAAY,KAAMmC,kBAAkBG,YAAY;oBAC3DN,kBAAkBO,IAAI,CAACN;gBACzB;YACF;QACF;QAEAxB,YAAY+B,eAAe,CAACtL,KAAK8K;QACjCvB,YAAYgC,oBAAoB,CAACxC,OAAO6B;QACxCrB,YAAYiC,mBAAmB,CAAC1C,eAAegC;QAE/C,IAAI9B,eAAe;YACjB,MAAMyC,cAAclC,YAAYmB,2BAA2B,CAAC3B,OAAO;YAEnE,MAAM0B,eAAelB,YAAYmB,2BAA2B,CAC1DJ,UAAU,CAAC,GACX;YAEF,MAAMoB,sBACJjB,aAAaE,cAAc,IAAIL,SAC3BA,SACAmB,YAAYd,cAAc,GACxB5B,QACA,CAAC;YAET/I,IAAI4H,GAAG,GAAG2B,YAAYoC,sBAAsB,CAC1C3L,IAAI4H,GAAG,IAAI,KACX8D;YAEFnD,UAAUI,QAAQ,GAAGY,YAAYoC,sBAAsB,CACrDpD,UAAUI,QAAQ,IAAI,KACtB+C;YAEF7C,mBAAmBU,YAAYoC,sBAAsB,CACnD9C,kBACA6C;YAGF,kCAAkC;YAClC,IAAI,CAACpB,QAAQ;gBACX,IAAImB,YAAYd,cAAc,EAAE;oBAC9BL,SAASzE,OAAO+F,MAAM,CAAC,CAAC,GAAGH,YAAYnB,MAAM;oBAE7C,4CAA4C;oBAC5C,iBAAiB;oBACjB,IAAK,MAAMS,OAAOxB,YAAYyB,mBAAmB,CAAE;wBACjD,OAAOjC,KAAK,CAACgC,IAAI;oBACnB;gBACF,OAAO;oBACL,qCAAqC;oBACrC,MAAMP,cAAcjB,YAAYgB,mBAAmB,oBAA/BhB,YAAYgB,mBAAmB,MAA/BhB,aAClBX,IAAAA,oCAAiB,EACfM,CAAAA,gCAAAA,aAAcP,QAAQ,KAAIJ,UAAUI,QAAQ,IAAI;oBAGpD,qDAAqD;oBACrD,kDAAkD;oBAClD,2BAA2B;oBAC3B,IAAI6B,aAAa;wBACfF,SAASzE,OAAO+F,MAAM,CAAC,CAAC,GAAGpB;oBAC7B;gBACF;YACF;QACF;QAEA,sDAAsD;QACtD,iDAAiD;QACjD,oDAAoD;QACpD,KAAK,MAAMO,OAAOH,eAAgB;YAChC,IAAI,CAAEG,CAAAA,OAAOjC,aAAY,GAAI;gBAC3B,OAAOC,KAAK,CAACgC,IAAI;YACnB;QACF;QAEA,MAAM,EAAEc,oBAAoB,EAAEC,uBAAuB,EAAE,GACrDC,IAAAA,mCAAyB,EAAC/L,KAAK4B,kBAAkBK,OAAO;QAE1D,IAAI+J,cAAc;QAClB,IAAIC;QAEJ,wCAAwC;QACxC,IAAIrM,QAAQC,GAAG,CAACK,YAAY,KAAK,UAAU6H,KAAK;YAC9C,MAAM,EAAEmE,iBAAiB,EAAE,GACzB3L,QAAQ;YAEV0L,cAAcC,kBACZlM,KACA+H,KACAnG,kBAAkBK,OAAO,EACzBG,QAAQ4F;YAEVgE,cAAcC,gBAAgB;QAChC;QAEA,MAAM1E,uBACJU,gDAAAA,uCAAkB,CAACkE,8CAAyB,CAAC,qBAA7ClE,6CAA+C,CAAC,IAAI,CAACvI,UAAU,CAAC;QAClE,MAAM4D,aACJiE,CAAAA,uCAAAA,oBAAqBjE,UAAU,KAAIH,oBAAoBC,MAAM;QAE/D,MAAMgJ,oBAAoBC,IAAAA,0BAAgB,EAACzL;QAC3C,IAAI0L,mBACF7L,IAAAA,2BAAc,EAACT,KAAK,iBAAiBoM;QAEvC,IAAInD,IAAAA,qBAAc,EAACqD,qBAAqBhC,QAAQ;YAC9CgC,mBAAmB/C,YAAYoC,sBAAsB,CACnDW,kBACAhC;QAEJ;QAEA,IAAIgC,qBAAqB,UAAU;YACjCA,mBAAmB;QACrB;QACA,IAAI;YACFA,mBAAmBC,IAAAA,kCAAgB,EAACD;QACtC,EAAE,OAAOE,GAAG,CAAC;QAEbF,mBAAmBG,IAAAA,wCAAmB,EAACH;QAEvC,OAAO;YACLvD;YACAD;YACAD;YACAyB;YACA/B;YACA2B;YACAzB;YACAY,OAAO,EAAEtG,wBAAAA,KAAMsG,OAAO;YACtBY;YACA+B;YACAC;YACAjD;YACAsD;YACAT;YACAC;YACA,GAAGzD,SAAS;YACZ5E,uBAAuB4E,UAAU5E,qBAAqB;YACtDF,yBAAyB8E,UAAU9E,uBAAuB;YAC1DD;YACAiE;QACF;IACF;IAEOmF,iBAAiB1M,GAAoB,EAAE;QAC5C,IAAI,CAAC,IAAI,CAAC2M,aAAa,EAAE;YACvB,MAAM/F,cAAcnG,IAAAA,2BAAc,EAACT,KAAK,kBAAkB;YAC1D,IAAI,CAAC2M,aAAa,GAAG,IAAIC,sBAAa,CAAChG;QACzC;QACA,OAAO,IAAI,CAAC+F,aAAa;IAC3B;IAEA,MAAaE,eAAe,EAC1B7M,GAAG,EACHsD,UAAU,EACVwJ,QAAQ,EACRC,SAAS,EACTC,UAAU,EACVpL,iBAAiB,EACjBqL,iBAAiB,EACjBpB,oBAAoB,EACpBC,uBAAuB,EACvBoB,iBAAiB,EACjBC,SAAS,EAaV,EAAE;QACD,MAAMR,gBAAgB,IAAI,CAACD,gBAAgB,CAAC1M;QAC5C,MAAMoN,aAAa,MAAMT,cAAcU,GAAG,CAACP,UAAUI,mBAAmB;YACtEH;YACAC;YACAC;YACApB;YACAyB,YAAYtN,IAAI8C,OAAO,CAACyK,OAAO,KAAK;YACpCC,kBAAkB,MAAM,IAAI,CAACtH,mBAAmB,CAC9ClG,KACAsD,YACA1B;YAEFuL;QACF;QAEA,IAAI,CAACC,YAAY;YACf,IACEN,YACA,kEAAkE;YAClE,CAAEjB,CAAAA,wBAAwBC,uBAAsB,GAChD;gBACA,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,qBAA8D,CAA9D,IAAI/H,MAAM,sDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;QACF;QACA,OAAOqJ;IACT;AACF", "ignoreList": [0]}