{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/layout/Navbar.tsx"], "sourcesContent": ["/**\n * 导航栏组件\n * 包含应用标题、搜索框、用户菜单等\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\ninterface NavbarProps {\n  onSearchChange?: (query: string) => void;\n  onMenuToggle?: () => void;\n}\n\nexport default function Navbar({ onSearchChange, onMenuToggle }: NavbarProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearchChange?.(query);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* 左侧：Logo和菜单按钮 */}\n          <div className=\"flex items-center\">\n            <button\n              onClick={onMenuToggle}\n              className=\"md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              <svg\n                className=\"h-6 w-6\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              </svg>\n            </button>\n            <Link href=\"/\" className=\"flex items-center ml-2\">\n              <div className=\"flex-shrink-0\">\n                <svg\n                  className=\"h-8 w-8 text-blue-600\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  />\n                </svg>\n              </div>\n              <div className=\"ml-2\">\n                <h1 className=\"text-xl font-bold text-gray-900\">提示词管理工具</h1>\n              </div>\n            </Link>\n          </div>\n\n          {/* 中间：搜索框 */}\n          <div className=\"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end\">\n            <div className=\"max-w-lg w-full lg:max-w-xs\">\n              <label htmlFor=\"search\" className=\"sr-only\">\n                搜索提示词\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg\n                    className=\"h-5 w-5 text-gray-400\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    viewBox=\"0 0 20 20\"\n                    fill=\"currentColor\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n                <input\n                  id=\"search\"\n                  name=\"search\"\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"搜索提示词...\"\n                  type=\"search\"\n                  value={searchQuery}\n                  onChange={handleSearchChange}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* 右侧：导航和用户菜单 */}\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/stats\"\n              className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              统计\n            </Link>\n            <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\">\n              新建提示词\n            </button>\n            <div className=\"relative\">\n              <button className=\"bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                <span className=\"sr-only\">打开用户菜单</span>\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <svg\n                    className=\"h-5 w-5 text-gray-600\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    viewBox=\"0 0 20 20\"\n                    fill=\"currentColor\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;;;AAHA;;;AAUe,SAAS,OAAO,KAA6C;QAA7C,EAAE,cAAc,EAAE,YAAY,EAAe,GAA7C;;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,2BAAA,qCAAA,eAAiB;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,QAAO;8CAEP,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;0CAIR,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAMtD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAAU;;;;;;8CAG5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAM;gDACN,SAAQ;gDACR,MAAK;0DAEL,cAAA,6LAAC;oDACC,UAAS;oDACT,GAAE;oDACF,UAAS;;;;;;;;;;;;;;;;sDAIf,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,MAAK;4CACL,OAAO;4CACP,UAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAO,WAAU;0CAA8K;;;;;;0CAGhM,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAM;gDACN,SAAQ;gDACR,MAAK;0DAEL,cAAA,6LAAC;oDACC,UAAS;oDACT,GAAE;oDACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/B;GA9HwB;KAAA", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["/**\n * 侧边栏组件\n * 包含分类列表、快捷操作等\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n  icon: string;\n  count: number;\n}\n\ninterface SidebarProps {\n  isOpen?: boolean;\n  onClose?: () => void;\n  categories?: Category[];\n  selectedCategoryId?: string;\n  onCategorySelect?: (categoryId: string | null) => void;\n  onNewPrompt?: () => void;\n  onManageCategories?: () => void;\n}\n\nexport default function Sidebar({\n  isOpen = true,\n  onClose,\n  categories = [],\n  selectedCategoryId,\n  onCategorySelect,\n  onNewPrompt,\n  onManageCategories,\n}: SidebarProps) {\n  const [showAllCategories, setShowAllCategories] = useState(false);\n\n  // 模拟数据\n  const mockCategories: Category[] = [\n    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️', count: 12 },\n    { id: '2', name: '代码生成', color: '#10B981', icon: '💻', count: 8 },\n    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐', count: 5 },\n    { id: '4', name: '数据分析', color: '#EF4444', icon: '📊', count: 3 },\n    { id: '5', name: '创意设计', color: '#8B5CF6', icon: '🎨', count: 7 },\n  ];\n\n  const displayCategories = categories.length > 0 ? categories : mockCategories;\n  const visibleCategories = showAllCategories ? displayCategories : displayCategories.slice(0, 5);\n\n  return (\n    <>\n      {/* 移动端遮罩 */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* 侧边栏 */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex flex-col h-full\">\n          {/* 侧边栏头部 */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200 md:hidden\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">菜单</h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* 快捷操作 */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <button\n              onClick={onNewPrompt}\n              className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\n            >\n              + 新建提示词\n            </button>\n          </div>\n\n          {/* 导航菜单 */}\n          <nav className=\"flex-1 px-4 py-4 space-y-2\">\n            {/* 全部提示词 */}\n            <button\n              onClick={() => onCategorySelect?.(null)}\n              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                selectedCategoryId === null\n                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                  : 'text-gray-700 hover:bg-gray-50'\n              }`}\n            >\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14-7l2 2-2 2m2-2H9m10 7l2 2-2 2m2-2H9\" />\n              </svg>\n              全部提示词\n              <span className=\"ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">\n                {displayCategories.reduce((sum, cat) => sum + cat.count, 0)}\n              </span>\n            </button>\n\n            {/* 收藏夹 */}\n            <button className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n              收藏夹\n              <span className=\"ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">3</span>\n            </button>\n\n            {/* 最近使用 */}\n            <button className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              最近使用\n            </button>\n\n            {/* 分类标题 */}\n            <div className=\"pt-4 pb-2\">\n              <h3 className=\"text-xs font-semibold text-gray-500 uppercase tracking-wider\">分类</h3>\n            </div>\n\n            {/* 分类列表 */}\n            <div className=\"space-y-1\">\n              {visibleCategories.map((category) => (\n                <button\n                  key={category.id}\n                  onClick={() => onCategorySelect?.(category.id)}\n                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                    selectedCategoryId === category.id\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <span className=\"mr-3 text-lg\">{category.icon}</span>\n                  <span className=\"flex-1 text-left\">{category.name}</span>\n                  <span className=\"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">\n                    {category.count}\n                  </span>\n                </button>\n              ))}\n            </div>\n\n            {/* 显示更多/收起 */}\n            {displayCategories.length > 5 && (\n              <button\n                onClick={() => setShowAllCategories(!showAllCategories)}\n                className=\"w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors\"\n              >\n                {showAllCategories ? '收起' : `显示更多 (${displayCategories.length - 5})`}\n                <svg\n                  className={`ml-1 h-4 w-4 transform transition-transform ${showAllCategories ? 'rotate-180' : ''}`}\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n            )}\n\n            {/* 管理分类 */}\n            <button\n              onClick={onManageCategories}\n              className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              <svg className=\"mr-3 h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              管理分类\n            </button>\n          </nav>\n\n          {/* 底部信息 */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"text-xs text-gray-500 text-center\">\n              <p>提示词管理工具</p>\n              <p className=\"mt-1\">v1.0.0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;;;AAFA;;AAuBe,SAAS,QAAQ,KAQjB;QARiB,EAC9B,SAAS,IAAI,EACb,OAAO,EACP,aAAa,EAAE,EACf,kBAAkB,EAClB,gBAAgB,EAChB,WAAW,EACX,kBAAkB,EACL,GARiB;;IAS9B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,OAAO;IACP,MAAM,iBAA6B;QACjC;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAG;QACjE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;QAChE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;QAChE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;QAChE;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;YAAM,OAAO;QAAE;KACjE;IAED,MAAM,oBAAoB,WAAW,MAAM,GAAG,IAAI,aAAa;IAC/D,MAAM,oBAAoB,oBAAoB,oBAAoB,kBAAkB,KAAK,CAAC,GAAG;IAE7F,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,WAAW,AAAC,qJAEX,OADC,SAAS,kBAAkB;0BAG7B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,SAAS,IAAM,6BAAA,uCAAA,iBAAmB;oCAClC,WAAW,AAAC,uFAIX,OAHC,uBAAuB,OACnB,wDACA;;sDAGN,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;sDAEN,6LAAC;4CAAK,WAAU;sDACb,kBAAkB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,KAAK,EAAE;;;;;;;;;;;;8CAK7D,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;sDAEN,6LAAC;4CAAK,WAAU;sDAAmE;;;;;;;;;;;;8CAIrF,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAKR,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAA+D;;;;;;;;;;;8CAI/E,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;4CAEC,SAAS,IAAM,6BAAA,uCAAA,iBAAmB,SAAS,EAAE;4CAC7C,WAAW,AAAC,uFAIX,OAHC,uBAAuB,SAAS,EAAE,GAC9B,wDACA;;8DAGN,6LAAC;oDAAK,WAAU;8DAAgB,SAAS,IAAI;;;;;;8DAC7C,6LAAC;oDAAK,WAAU;8DAAoB,SAAS,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;8DACb,SAAS,KAAK;;;;;;;2CAXZ,SAAS,EAAE;;;;;;;;;;gCAkBrB,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;oCACC,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;;wCAET,oBAAoB,OAAO,AAAC,SAAqC,OAA7B,kBAAkB,MAAM,GAAG,GAAE;sDAClE,6LAAC;4CACC,WAAW,AAAC,+CAAoF,OAAtC,oBAAoB,eAAe;4CAC7F,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;8CAM3E,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;sCAMV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GAvKwB;KAAA", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/layout/MainLayout.tsx"], "sourcesContent": ["/**\n * 主布局组件\n * 整合导航栏、侧边栏和主内容区\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n  onNewPrompt?: () => void;\n  onManageCategories?: () => void;\n}\n\nexport default function MainLayout({ children, onNewPrompt, onManageCategories }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleMenuToggle = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  const handleSidebarClose = () => {\n    setSidebarOpen(false);\n  };\n\n  const handleCategorySelect = (categoryId: string | null) => {\n    setSelectedCategoryId(categoryId);\n    // 在移动端选择分类后关闭侧边栏\n    if (window.innerWidth < 768) {\n      setSidebarOpen(false);\n    }\n  };\n\n  const handleSearchChange = (query: string) => {\n    setSearchQuery(query);\n  };\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-50\">\n      {/* 侧边栏 */}\n      <Sidebar\n        isOpen={sidebarOpen}\n        onClose={handleSidebarClose}\n        selectedCategoryId={selectedCategoryId}\n        onCategorySelect={handleCategorySelect}\n        onNewPrompt={onNewPrompt}\n        onManageCategories={onManageCategories}\n      />\n\n      {/* 主内容区 */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* 导航栏 */}\n        <Navbar\n          onMenuToggle={handleMenuToggle}\n          onSearchChange={handleSearchChange}\n        />\n\n        {/* 主内容 */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AACA;;;AAJA;;;;AAYe,SAAS,WAAW,KAA8D;QAA9D,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAmB,GAA9D;;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB;QACvB,eAAe,CAAC;IAClB;IAEA,MAAM,qBAAqB;QACzB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,iBAAiB;QACjB,IAAI,OAAO,UAAU,GAAG,KAAK;YAC3B,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,0IAAA,CAAA,UAAO;gBACN,QAAQ;gBACR,SAAS;gBACT,oBAAoB;gBACpB,kBAAkB;gBAClB,aAAa;gBACb,oBAAoB;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,yIAAA,CAAA,UAAM;wBACL,cAAc;wBACd,gBAAgB;;;;;;kCAIlB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAxDwB;KAAA", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/StatsPanel.tsx"], "sourcesContent": ["/**\n * 统计面板组件\n * 显示提示词使用统计信息\n */\n\n'use client';\n\nimport { useState } from 'react';\n\ninterface StatsData {\n  totalPrompts: number;\n  totalCategories: number;\n  totalTags: number;\n  totalUsage: number;\n  recentActivity: {\n    date: string;\n    count: number;\n  }[];\n  topCategories: {\n    name: string;\n    count: number;\n    color: string;\n  }[];\n  topPrompts: {\n    id: string;\n    title: string;\n    usageCount: number;\n  }[];\n}\n\ninterface StatsPanelProps {\n  data?: StatsData;\n}\n\nexport default function StatsPanel({ data }: StatsPanelProps) {\n  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');\n\n  // 模拟数据\n  const mockData: StatsData = {\n    totalPrompts: 156,\n    totalCategories: 8,\n    totalTags: 24,\n    totalUsage: 1247,\n    recentActivity: [\n      { date: '2024-01-20', count: 15 },\n      { date: '2024-01-19', count: 23 },\n      { date: '2024-01-18', count: 18 },\n      { date: '2024-01-17', count: 12 },\n      { date: '2024-01-16', count: 28 },\n      { date: '2024-01-15', count: 19 },\n      { date: '2024-01-14', count: 22 },\n    ],\n    topCategories: [\n      { name: '写作助手', count: 45, color: '#3B82F6' },\n      { name: '代码生成', count: 32, color: '#10B981' },\n      { name: '翻译工具', count: 28, color: '#F59E0B' },\n      { name: '数据分析', count: 21, color: '#EF4444' },\n      { name: '创意设计', count: 18, color: '#8B5CF6' },\n    ],\n    topPrompts: [\n      { id: '1', title: '写作助手 - 文章大纲生成', usageCount: 89 },\n      { id: '2', title: '代码生成 - React组件模板', usageCount: 67 },\n      { id: '3', title: '翻译工具 - 专业文档翻译', usageCount: 54 },\n      { id: '4', title: 'SQL查询生成器', usageCount: 43 },\n      { id: '5', title: '邮件模板生成', usageCount: 38 },\n    ],\n  };\n\n  const displayData = data || mockData;\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });\n  };\n\n  const getMaxActivity = () => {\n    return Math.max(...displayData.recentActivity.map(item => item.count));\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 概览统计 */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-8 w-8 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">总提示词</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">{displayData.totalPrompts}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-8 w-8 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14-7l2 2-2 2m2-2H9m10 7l2 2-2 2m2-2H9\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">分类数量</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">{displayData.totalCategories}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-8 w-8 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">标签数量</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">{displayData.totalTags}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-8 w-8 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">总使用次数</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">{displayData.totalUsage}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 活动趋势 */}\n      <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-medium text-gray-900\">使用趋势</h3>\n          <select\n            value={timeRange}\n            onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d')}\n            className=\"text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"7d\">最近7天</option>\n            <option value=\"30d\">最近30天</option>\n            <option value=\"90d\">最近90天</option>\n          </select>\n        </div>\n        \n        <div className=\"flex items-end space-x-2 h-32\">\n          {displayData.recentActivity.map((item, index) => (\n            <div key={index} className=\"flex-1 flex flex-col items-center\">\n              <div\n                className=\"w-full bg-blue-500 rounded-t\"\n                style={{\n                  height: `${(item.count / getMaxActivity()) * 100}%`,\n                  minHeight: '4px',\n                }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500 text-center\">\n                {formatDate(item.date)}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* 热门分类 */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">热门分类</h3>\n          <div className=\"space-y-3\">\n            {displayData.topCategories.map((category, index) => (\n              <div key={index} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <div\n                    className=\"w-3 h-3 rounded-full mr-3\"\n                    style={{ backgroundColor: category.color }}\n                  />\n                  <span className=\"text-sm font-medium text-gray-900\">{category.name}</span>\n                </div>\n                <span className=\"text-sm text-gray-500\">{category.count} 个</span>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* 热门提示词 */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">热门提示词</h3>\n          <div className=\"space-y-3\">\n            {displayData.topPrompts.map((prompt, index) => (\n              <div key={prompt.id} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center min-w-0 flex-1\">\n                  <span className=\"text-sm font-medium text-gray-500 mr-3\">#{index + 1}</span>\n                  <span className=\"text-sm font-medium text-gray-900 truncate\">{prompt.title}</span>\n                </div>\n                <span className=\"text-sm text-gray-500 ml-2\">{prompt.usageCount} 次</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;;;AAFA;;AA6Be,SAAS,WAAW,KAAyB;QAAzB,EAAE,IAAI,EAAmB,GAAzB;;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAEjE,OAAO;IACP,MAAM,WAAsB;QAC1B,cAAc;QACd,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,gBAAgB;YACd;gBAAE,MAAM;gBAAc,OAAO;YAAG;YAChC;gBAAE,MAAM;gBAAc,OAAO;YAAG;YAChC;gBAAE,MAAM;gBAAc,OAAO;YAAG;YAChC;gBAAE,MAAM;gBAAc,OAAO;YAAG;YAChC;gBAAE,MAAM;gBAAc,OAAO;YAAG;YAChC;gBAAE,MAAM;gBAAc,OAAO;YAAG;YAChC;gBAAE,MAAM;gBAAc,OAAO;YAAG;SACjC;QACD,eAAe;YACb;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAU;YAC5C;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAU;YAC5C;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAU;YAC5C;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAU;YAC5C;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAU;SAC7C;QACD,YAAY;YACV;gBAAE,IAAI;gBAAK,OAAO;gBAAiB,YAAY;YAAG;YAClD;gBAAE,IAAI;gBAAK,OAAO;gBAAoB,YAAY;YAAG;YACrD;gBAAE,IAAI;gBAAK,OAAO;gBAAiB,YAAY;YAAG;YAClD;gBAAE,IAAI;gBAAK,OAAO;gBAAY,YAAY;YAAG;YAC7C;gBAAE,IAAI;gBAAK,OAAO;gBAAU,YAAY;YAAG;SAC5C;IACH;IAEA,MAAM,cAAc,QAAQ;IAE5B,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,OAAO;YAAS,KAAK;QAAU;IAC3E;IAEA,MAAM,iBAAiB;QACrB,OAAO,KAAK,GAAG,IAAI,YAAY,cAAc,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IACtE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwC,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;kCAKnF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC7E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwC,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;kCAKtF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwC,YAAY,SAAS;;;;;;;;;;;;;;;;;;;;;;;kCAKhF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwC,YAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;kCACZ,YAAY,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrC,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,QAAQ,AAAC,GAAwC,OAAtC,AAAC,KAAK,KAAK,GAAG,mBAAoB,KAAI;4CACjD,WAAW;wCACb;;;;;;kDAEF,6LAAC;wCAAI,WAAU;kDACZ,WAAW,KAAK,IAAI;;;;;;;+BATf;;;;;;;;;;;;;;;;0BAgBhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,YAAY,aAAa,CAAC,GAAG,CAAC,CAAC,UAAU,sBACxC,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,SAAS,KAAK;wDAAC;;;;;;kEAE3C,6LAAC;wDAAK,WAAU;kEAAqC,SAAS,IAAI;;;;;;;;;;;;0DAEpE,6LAAC;gDAAK,WAAU;;oDAAyB,SAAS,KAAK;oDAAC;;;;;;;;uCARhD;;;;;;;;;;;;;;;;kCAehB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACnC,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAAyC;4DAAE,QAAQ;;;;;;;kEACnE,6LAAC;wDAAK,WAAU;kEAA8C,OAAO,KAAK;;;;;;;;;;;;0DAE5E,6LAAC;gDAAK,WAAU;;oDAA8B,OAAO,UAAU;oDAAC;;;;;;;;uCALxD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajC;GAjLwB;KAAA", "debugId": null}}, {"offset": {"line": 1513, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/Modal.tsx"], "sourcesContent": ["/**\n * 基础模态框组件\n * 提供可重用的模态框功能\n */\n\n'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n  showCloseButton?: boolean;\n  closeOnOverlayClick?: boolean;\n  closeOnEscape?: boolean;\n}\n\nexport default function Modal({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n  closeOnEscape = true,\n}: ModalProps) {\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  // 处理ESC键关闭\n  useEffect(() => {\n    if (!closeOnEscape) return;\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen, onClose, closeOnEscape]);\n\n  // 处理body滚动锁定\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  // 处理点击遮罩关闭\n  const handleOverlayClick = (event: React.MouseEvent) => {\n    if (closeOnOverlayClick && event.target === event.currentTarget) {\n      onClose();\n    }\n  };\n\n  // 获取尺寸类名\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'max-w-md';\n      case 'md':\n        return 'max-w-lg';\n      case 'lg':\n        return 'max-w-2xl';\n      case 'xl':\n        return 'max-w-4xl';\n      case 'full':\n        return 'max-w-full mx-4';\n      default:\n        return 'max-w-lg';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      {/* 遮罩层 */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={handleOverlayClick}\n      />\n\n      {/* 模态框容器 */}\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        <div\n          ref={modalRef}\n          className={`relative w-full ${getSizeClasses()} transform overflow-hidden rounded-lg bg-white shadow-xl transition-all`}\n        >\n          {/* 头部 */}\n          {(title || showCloseButton) && (\n            <div className=\"flex items-center justify-between px-6 py-4 border-b border-gray-200\">\n              {title && (\n                <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n              )}\n              {showCloseButton && (\n                <button\n                  onClick={onClose}\n                  className=\"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <span className=\"sr-only\">关闭</span>\n                  <svg\n                    className=\"h-6 w-6\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke=\"currentColor\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M6 18L18 6M6 6l12 12\"\n                    />\n                  </svg>\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* 内容 */}\n          <div className=\"px-6 py-4\">{children}</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;;;AAFA;;AAee,SAAS,MAAM,KASjB;QATiB,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EAC1B,gBAAgB,IAAI,EACT,GATiB;;IAU5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;wBACpC;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;mCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;0BAAG;QAAC;QAAQ;QAAS;KAAc;IAEnC,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,MAAM,MAAM,KAAK,MAAM,aAAa,EAAE;YAC/D;QACF;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,KAAK;oBACL,WAAW,AAAC,mBAAmC,OAAjB,kBAAiB;;wBAG9C,CAAC,SAAS,eAAe,mBACxB,6LAAC;4BAAI,WAAU;;gCACZ,uBACC,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;gCAEtD,iCACC,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;sCASd,6LAAC;4BAAI,WAAU;sCAAa;;;;;;;;;;;;;;;;;;;;;;;AAKtC;GApHwB;KAAA", "debugId": null}}, {"offset": {"line": 1702, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/BatchImportModal.tsx"], "sourcesContent": ["/**\n * 批量导入模态框组件\n * 支持JSON文件导入提示词\n */\n\n'use client';\n\nimport { useState, useRef } from 'react';\nimport Modal from './Modal';\nimport { useToast } from './ToastContainer';\n\ninterface ImportData {\n  title: string;\n  content: string;\n  description?: string;\n  category?: string;\n  tags?: string[];\n}\n\ninterface BatchImportModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onImport: (data: ImportData[]) => Promise<void>;\n}\n\nexport default function BatchImportModal({\n  isOpen,\n  onClose,\n  onImport,\n}: BatchImportModalProps) {\n  const [file, setFile] = useState<File | null>(null);\n  const [importData, setImportData] = useState<ImportData[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [validationErrors, setValidationErrors] = useState<string[]>([]);\n  const [importProgress, setImportProgress] = useState(0);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { showSuccess, showError } = useToast();\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = event.target.files?.[0];\n    if (selectedFile) {\n      if (selectedFile.type !== 'application/json') {\n        showError('文件格式错误', '请选择JSON格式的文件');\n        return;\n      }\n      setFile(selectedFile);\n      parseFile(selectedFile);\n    }\n  };\n\n  const parseFile = async (file: File) => {\n    try {\n      const text = await file.text();\n      const data = JSON.parse(text);\n      \n      // 验证数据格式\n      const errors = validateImportData(data);\n      if (errors.length > 0) {\n        setValidationErrors(errors);\n        setImportData([]);\n        return;\n      }\n\n      setImportData(Array.isArray(data) ? data : [data]);\n      setValidationErrors([]);\n    } catch (error) {\n      showError('文件解析失败', '请检查JSON文件格式是否正确');\n      setImportData([]);\n      setValidationErrors(['JSON格式错误']);\n    }\n  };\n\n  const validateImportData = (data: any): string[] => {\n    const errors: string[] = [];\n    \n    if (!Array.isArray(data) && typeof data !== 'object') {\n      errors.push('数据格式错误：应为对象或对象数组');\n      return errors;\n    }\n\n    const items = Array.isArray(data) ? data : [data];\n    \n    items.forEach((item, index) => {\n      if (!item.title || typeof item.title !== 'string') {\n        errors.push(`第${index + 1}项：缺少标题或标题格式错误`);\n      }\n      if (!item.content || typeof item.content !== 'string') {\n        errors.push(`第${index + 1}项：缺少内容或内容格式错误`);\n      }\n      if (item.tags && !Array.isArray(item.tags)) {\n        errors.push(`第${index + 1}项：标签格式错误，应为字符串数组`);\n      }\n    });\n\n    return errors;\n  };\n\n  const handleImport = async () => {\n    if (importData.length === 0) return;\n\n    setIsProcessing(true);\n    setImportProgress(0);\n\n    try {\n      // 模拟导入进度\n      for (let i = 0; i <= 100; i += 10) {\n        setImportProgress(i);\n        await new Promise(resolve => setTimeout(resolve, 100));\n      }\n\n      await onImport(importData);\n      showSuccess('导入成功', `成功导入${importData.length}个提示词`);\n      handleClose();\n    } catch (error) {\n      showError('导入失败', '请稍后重试');\n    } finally {\n      setIsProcessing(false);\n      setImportProgress(0);\n    }\n  };\n\n  const handleClose = () => {\n    setFile(null);\n    setImportData([]);\n    setValidationErrors([]);\n    setImportProgress(0);\n    setIsProcessing(false);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    onClose();\n  };\n\n  const downloadTemplate = () => {\n    const template = [\n      {\n        title: '示例提示词1',\n        content: '这是提示词的内容...',\n        description: '这是提示词的描述（可选）',\n        category: '分类名称（可选）',\n        tags: ['标签1', '标签2']\n      },\n      {\n        title: '示例提示词2',\n        content: '另一个提示词的内容...',\n        description: '另一个描述',\n        category: '另一个分类',\n        tags: ['标签3']\n      }\n    ];\n\n    const blob = new Blob([JSON.stringify(template, null, 2)], {\n      type: 'application/json',\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'prompt-template.json';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={handleClose}\n      title=\"批量导入提示词\"\n      size=\"lg\"\n    >\n      <div className=\"space-y-6\">\n        {/* 文件上传区域 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            选择JSON文件\n          </label>\n          <div className=\"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors\">\n            <div className=\"space-y-1 text-center\">\n              <svg\n                className=\"mx-auto h-12 w-12 text-gray-400\"\n                stroke=\"currentColor\"\n                fill=\"none\"\n                viewBox=\"0 0 48 48\"\n              >\n                <path\n                  d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\n                  strokeWidth={2}\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                />\n              </svg>\n              <div className=\"flex text-sm text-gray-600\">\n                <label\n                  htmlFor=\"file-upload\"\n                  className=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500\"\n                >\n                  <span>选择文件</span>\n                  <input\n                    id=\"file-upload\"\n                    ref={fileInputRef}\n                    name=\"file-upload\"\n                    type=\"file\"\n                    accept=\".json\"\n                    className=\"sr-only\"\n                    onChange={handleFileSelect}\n                  />\n                </label>\n                <p className=\"pl-1\">或拖拽到此处</p>\n              </div>\n              <p className=\"text-xs text-gray-500\">仅支持JSON格式文件</p>\n            </div>\n          </div>\n          \n          {file && (\n            <div className=\"mt-2 text-sm text-gray-600\">\n              已选择文件: {file.name}\n            </div>\n          )}\n        </div>\n\n        {/* 模板下载 */}\n        <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg\">\n          <div>\n            <h4 className=\"text-sm font-medium text-blue-900\">需要模板文件？</h4>\n            <p className=\"text-sm text-blue-700\">下载示例模板了解正确的数据格式</p>\n          </div>\n          <button\n            onClick={downloadTemplate}\n            className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            下载模板\n          </button>\n        </div>\n\n        {/* 验证错误 */}\n        {validationErrors.length > 0 && (\n          <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <h4 className=\"text-sm font-medium text-red-800 mb-2\">数据验证错误：</h4>\n            <ul className=\"text-sm text-red-700 space-y-1\">\n              {validationErrors.map((error, index) => (\n                <li key={index}>• {error}</li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        {/* 导入预览 */}\n        {importData.length > 0 && (\n          <div>\n            <h4 className=\"text-sm font-medium text-gray-900 mb-2\">\n              预览数据 ({importData.length} 个提示词)\n            </h4>\n            <div className=\"max-h-40 overflow-y-auto border border-gray-200 rounded-lg\">\n              {importData.slice(0, 5).map((item, index) => (\n                <div key={index} className=\"p-3 border-b border-gray-100 last:border-b-0\">\n                  <div className=\"font-medium text-sm text-gray-900\">{item.title}</div>\n                  <div className=\"text-xs text-gray-500 mt-1 truncate\">\n                    {item.content.substring(0, 100)}...\n                  </div>\n                </div>\n              ))}\n              {importData.length > 5 && (\n                <div className=\"p-3 text-center text-sm text-gray-500\">\n                  还有 {importData.length - 5} 个提示词...\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* 导入进度 */}\n        {isProcessing && (\n          <div>\n            <div className=\"flex justify-between text-sm text-gray-600 mb-2\">\n              <span>导入进度</span>\n              <span>{importProgress}%</span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${importProgress}%` }}\n              />\n            </div>\n          </div>\n        )}\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n          <button\n            onClick={handleClose}\n            disabled={isProcessing}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            取消\n          </button>\n          <button\n            onClick={handleImport}\n            disabled={importData.length === 0 || isProcessing || validationErrors.length > 0}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isProcessing ? '导入中...' : `导入 ${importData.length} 个提示词`}\n          </button>\n        </div>\n      </div>\n    </Modal>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AACA;;;AAJA;;;;AAoBe,SAAS,iBAAiB,KAIjB;QAJiB,EACvC,MAAM,EACN,OAAO,EACP,QAAQ,EACc,GAJiB;;IAKvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD;IAE1C,MAAM,mBAAmB,CAAC;YACH;QAArB,MAAM,gBAAe,sBAAA,MAAM,MAAM,CAAC,KAAK,cAAlB,0CAAA,mBAAoB,CAAC,EAAE;QAC5C,IAAI,cAAc;YAChB,IAAI,aAAa,IAAI,KAAK,oBAAoB;gBAC5C,UAAU,UAAU;gBACpB;YACF;YACA,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI;YACF,MAAM,OAAO,MAAM,KAAK,IAAI;YAC5B,MAAM,OAAO,KAAK,KAAK,CAAC;YAExB,SAAS;YACT,MAAM,SAAS,mBAAmB;YAClC,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,oBAAoB;gBACpB,cAAc,EAAE;gBAChB;YACF;YAEA,cAAc,MAAM,OAAO,CAAC,QAAQ,OAAO;gBAAC;aAAK;YACjD,oBAAoB,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,UAAU,UAAU;YACpB,cAAc,EAAE;YAChB,oBAAoB;gBAAC;aAAW;QAClC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAmB,EAAE;QAE3B,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,OAAO,SAAS,UAAU;YACpD,OAAO,IAAI,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,QAAQ,MAAM,OAAO,CAAC,QAAQ,OAAO;YAAC;SAAK;QAEjD,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,IAAI,CAAC,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,UAAU;gBACjD,OAAO,IAAI,CAAC,AAAC,IAAa,OAAV,QAAQ,GAAE;YAC5B;YACA,IAAI,CAAC,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,UAAU;gBACrD,OAAO,IAAI,CAAC,AAAC,IAAa,OAAV,QAAQ,GAAE;YAC5B;YACA,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,IAAI,GAAG;gBAC1C,OAAO,IAAI,CAAC,AAAC,IAAa,OAAV,QAAQ,GAAE;YAC5B;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,MAAM,KAAK,GAAG;QAE7B,gBAAgB;QAChB,kBAAkB;QAElB,IAAI;YACF,SAAS;YACT,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,GAAI;gBACjC,kBAAkB;gBAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,MAAM,SAAS;YACf,YAAY,QAAQ,AAAC,OAAwB,OAAlB,WAAW,MAAM,EAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,UAAU,QAAQ;QACpB,SAAU;YACR,gBAAgB;YAChB,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,cAAc,EAAE;QAChB,oBAAoB,EAAE;QACtB,kBAAkB;QAClB,gBAAgB;QAChB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;QACA;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW;YACf;gBACE,OAAO;gBACP,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,MAAM;oBAAC;oBAAO;iBAAM;YACtB;YACA;gBACE,OAAO;gBACP,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,MAAM;oBAAC;iBAAM;YACf;SACD;QAED,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,UAAU,MAAM;SAAG,EAAE;YACzD,MAAM;QACR;QACA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,6LAAC,oIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,QAAO;wCACP,MAAK;wCACL,SAAQ;kDAER,cAAA,6LAAC;4CACC,GAAE;4CACF,aAAa;4CACb,eAAc;4CACd,gBAAe;;;;;;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAQ;gDACR,WAAU;;kEAEV,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDACC,IAAG;wDACH,KAAK;wDACL,MAAK;wDACL,MAAK;wDACL,QAAO;wDACP,WAAU;wDACV,UAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAE,WAAU;0DAAO;;;;;;;;;;;;kDAEtB,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;wBAIxC,sBACC,6LAAC;4BAAI,WAAU;;gCAA6B;gCAClC,KAAK,IAAI;;;;;;;;;;;;;8BAMvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;gBAMF,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAG,WAAU;sCACX,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;;wCAAe;wCAAG;;mCAAV;;;;;;;;;;;;;;;;gBAOhB,WAAW,MAAM,GAAG,mBACnB,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;;gCAAyC;gCAC9C,WAAW,MAAM;gCAAC;;;;;;;sCAE3B,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACjC,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAqC,KAAK,KAAK;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG;oDAAK;;;;;;;;uCAH1B;;;;;gCAOX,WAAW,MAAM,GAAG,mBACnB,6LAAC;oCAAI,WAAU;;wCAAwC;wCACjD,WAAW,MAAM,GAAG;wCAAE;;;;;;;;;;;;;;;;;;;gBAQnC,8BACC,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;wCAAM;wCAAe;;;;;;;;;;;;;sCAExB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,AAAC,GAAiB,OAAf,gBAAe;gCAAG;;;;;;;;;;;;;;;;;8BAO7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS;4BACT,UAAU,WAAW,MAAM,KAAK,KAAK,gBAAgB,iBAAiB,MAAM,GAAG;4BAC/E,WAAU;sCAET,eAAe,WAAW,AAAC,MAAuB,OAAlB,WAAW,MAAM,EAAC;;;;;;;;;;;;;;;;;;;;;;;AAM/D;GA1RwB;;QAWa,6IAAA,CAAA,WAAQ;;;KAXrB", "debugId": null}}, {"offset": {"line": 2232, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/components/ui/ExportModal.tsx"], "sourcesContent": ["/**\n * 数据导出模态框组件\n * 支持多种格式导出提示词数据\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Modal from './Modal';\nimport { useToast } from './ToastContainer';\n\ninterface ExportOptions {\n  format: 'json' | 'csv' | 'markdown';\n  includeCategories: boolean;\n  includeTags: boolean;\n  includeStats: boolean;\n  dateRange: 'all' | '30d' | '90d' | '1y';\n  selectedCategories: string[];\n}\n\ninterface ExportModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onExport: (options: ExportOptions) => Promise<void>;\n  categories?: Array<{ id: string; name: string; color: string; icon: string }>;\n}\n\nexport default function ExportModal({\n  isOpen,\n  onClose,\n  onExport,\n  categories = [],\n}: ExportModalProps) {\n  const [options, setOptions] = useState<ExportOptions>({\n    format: 'json',\n    includeCategories: true,\n    includeTags: true,\n    includeStats: false,\n    dateRange: 'all',\n    selectedCategories: [],\n  });\n  const [isExporting, setIsExporting] = useState(false);\n  const { showSuccess, showError } = useToast();\n\n  // 模拟分类数据\n  const mockCategories = [\n    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },\n    { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },\n    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },\n  ];\n\n  const displayCategories = categories.length > 0 ? categories : mockCategories;\n\n  const handleExport = async () => {\n    setIsExporting(true);\n    try {\n      await onExport(options);\n      showSuccess('导出成功', '数据已下载到您的设备');\n      onClose();\n    } catch (error) {\n      showError('导出失败', '请稍后重试');\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  const handleCategoryToggle = (categoryId: string) => {\n    setOptions(prev => ({\n      ...prev,\n      selectedCategories: prev.selectedCategories.includes(categoryId)\n        ? prev.selectedCategories.filter(id => id !== categoryId)\n        : [...prev.selectedCategories, categoryId],\n    }));\n  };\n\n  const selectAllCategories = () => {\n    setOptions(prev => ({\n      ...prev,\n      selectedCategories: displayCategories.map(cat => cat.id),\n    }));\n  };\n\n  const deselectAllCategories = () => {\n    setOptions(prev => ({\n      ...prev,\n      selectedCategories: [],\n    }));\n  };\n\n  const getFormatDescription = (format: string) => {\n    switch (format) {\n      case 'json':\n        return '结构化数据格式，适合程序处理和备份';\n      case 'csv':\n        return '表格格式，适合在Excel等软件中查看';\n      case 'markdown':\n        return '文档格式，适合阅读和分享';\n      default:\n        return '';\n    }\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"导出数据\"\n      size=\"lg\"\n    >\n      <div className=\"space-y-6\">\n        {/* 导出格式 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n            导出格式\n          </label>\n          <div className=\"space-y-3\">\n            {[\n              { value: 'json', label: 'JSON', icon: '📄' },\n              { value: 'csv', label: 'CSV', icon: '📊' },\n              { value: 'markdown', label: 'Markdown', icon: '📝' },\n            ].map((format) => (\n              <label key={format.value} className=\"flex items-start\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value={format.value}\n                  checked={options.format === format.value}\n                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as any }))}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-1\"\n                />\n                <div className=\"ml-3\">\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-2\">{format.icon}</span>\n                    <span className=\"text-sm font-medium text-gray-900\">{format.label}</span>\n                  </div>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    {getFormatDescription(format.value)}\n                  </p>\n                </div>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* 包含内容 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n            包含内容\n          </label>\n          <div className=\"space-y-2\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={options.includeCategories}\n                onChange={(e) => setOptions(prev => ({ ...prev, includeCategories: e.target.checked }))}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <span className=\"ml-2 text-sm text-gray-900\">分类信息</span>\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={options.includeTags}\n                onChange={(e) => setOptions(prev => ({ ...prev, includeTags: e.target.checked }))}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <span className=\"ml-2 text-sm text-gray-900\">标签信息</span>\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={options.includeStats}\n                onChange={(e) => setOptions(prev => ({ ...prev, includeStats: e.target.checked }))}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <span className=\"ml-2 text-sm text-gray-900\">使用统计</span>\n            </label>\n          </div>\n        </div>\n\n        {/* 时间范围 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            时间范围\n          </label>\n          <select\n            value={options.dateRange}\n            onChange={(e) => setOptions(prev => ({ ...prev, dateRange: e.target.value as any }))}\n            className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"all\">全部时间</option>\n            <option value=\"30d\">最近30天</option>\n            <option value=\"90d\">最近90天</option>\n            <option value=\"1y\">最近1年</option>\n          </select>\n        </div>\n\n        {/* 分类筛选 */}\n        <div>\n          <div className=\"flex items-center justify-between mb-3\">\n            <label className=\"block text-sm font-medium text-gray-700\">\n              选择分类\n            </label>\n            <div className=\"space-x-2\">\n              <button\n                type=\"button\"\n                onClick={selectAllCategories}\n                className=\"text-sm text-blue-600 hover:text-blue-800\"\n              >\n                全选\n              </button>\n              <button\n                type=\"button\"\n                onClick={deselectAllCategories}\n                className=\"text-sm text-gray-600 hover:text-gray-800\"\n              >\n                清空\n              </button>\n            </div>\n          </div>\n          <div className=\"max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3 space-y-2\">\n            {displayCategories.map((category) => (\n              <label key={category.id} className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={options.selectedCategories.includes(category.id)}\n                  onChange={() => handleCategoryToggle(category.id)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 flex items-center text-sm text-gray-900\">\n                  <span className=\"mr-1\">{category.icon}</span>\n                  {category.name}\n                </span>\n              </label>\n            ))}\n          </div>\n          <p className=\"mt-2 text-sm text-gray-500\">\n            {options.selectedCategories.length === 0 \n              ? '未选择分类，将导出所有分类的数据'\n              : `已选择 ${options.selectedCategories.length} 个分类`\n            }\n          </p>\n        </div>\n\n        {/* 预览信息 */}\n        <div className=\"bg-gray-50 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">导出预览</h4>\n          <div className=\"text-sm text-gray-600 space-y-1\">\n            <p>• 格式: {options.format.toUpperCase()}</p>\n            <p>• 时间范围: {\n              options.dateRange === 'all' ? '全部时间' :\n              options.dateRange === '30d' ? '最近30天' :\n              options.dateRange === '90d' ? '最近90天' : '最近1年'\n            }</p>\n            <p>• 分类: {\n              options.selectedCategories.length === 0 ? '全部分类' :\n              `${options.selectedCategories.length} 个分类`\n            }</p>\n            <p>• 包含: {[\n              options.includeCategories && '分类信息',\n              options.includeTags && '标签信息',\n              options.includeStats && '使用统计',\n            ].filter(Boolean).join('、') || '仅基本信息'}</p>\n          </div>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n          <button\n            onClick={onClose}\n            disabled={isExporting}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            取消\n          </button>\n          <button\n            onClick={handleExport}\n            disabled={isExporting}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isExporting ? '导出中...' : '开始导出'}\n          </button>\n        </div>\n      </div>\n    </Modal>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AACA;;;AAJA;;;;AAsBe,SAAS,YAAY,KAKjB;QALiB,EAClC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,aAAa,EAAE,EACE,GALiB;;IAMlC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,mBAAmB;QACnB,aAAa;QACb,cAAc;QACd,WAAW;QACX,oBAAoB,EAAE;IACxB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD;IAE1C,SAAS;IACT,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;QACtD;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;QACtD;YAAE,IAAI;YAAK,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAK;KACvD;IAED,MAAM,oBAAoB,WAAW,MAAM,GAAG,IAAI,aAAa;IAE/D,MAAM,eAAe;QACnB,eAAe;QACf,IAAI;YACF,MAAM,SAAS;YACf,YAAY,QAAQ;YACpB;QACF,EAAE,OAAO,OAAO;YACd,UAAU,QAAQ;QACpB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,oBAAoB,KAAK,kBAAkB,CAAC,QAAQ,CAAC,cACjD,KAAK,kBAAkB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,cAC5C;uBAAI,KAAK,kBAAkB;oBAAE;iBAAW;YAC9C,CAAC;IACH;IAEA,MAAM,sBAAsB;QAC1B,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,oBAAoB,kBAAkB,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;YACzD,CAAC;IACH;IAEA,MAAM,wBAAwB;QAC5B,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,oBAAoB,EAAE;YACxB,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,oIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,OAAO;oCAAQ,OAAO;oCAAQ,MAAM;gCAAK;gCAC3C;oCAAE,OAAO;oCAAO,OAAO;oCAAO,MAAM;gCAAK;gCACzC;oCAAE,OAAO;oCAAY,OAAO;oCAAY,MAAM;gCAAK;6BACpD,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC;oCAAyB,WAAU;;sDAClC,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,OAAO,KAAK;4CACnB,SAAS,QAAQ,MAAM,KAAK,OAAO,KAAK;4CACxC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAQ,CAAC;4CAC/E,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ,OAAO,IAAI;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAqC,OAAO,KAAK;;;;;;;;;;;;8DAEnE,6LAAC;oDAAE,WAAU;8DACV,qBAAqB,OAAO,KAAK;;;;;;;;;;;;;mCAf5B,OAAO,KAAK;;;;;;;;;;;;;;;;8BAwB9B,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,iBAAiB;4CAClC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,mBAAmB,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CACrF,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAE/C,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,WAAW;4CAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CAC/E,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAE/C,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,YAAY;4CAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,cAAc,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CAChF,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,6LAAC;4BACC,OAAO,QAAQ,SAAS;4BACxB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oCAAQ,CAAC;4BAClF,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAK;;;;;;;;;;;;;;;;;;8BAKvB,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA0C;;;;;;8CAG3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;oCAAwB,WAAU;;sDACjC,6LAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,kBAAkB,CAAC,QAAQ,CAAC,SAAS,EAAE;4CACxD,UAAU,IAAM,qBAAqB,SAAS,EAAE;4CAChD,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAK,WAAU;8DAAQ,SAAS,IAAI;;;;;;gDACpC,SAAS,IAAI;;;;;;;;mCATN,SAAS,EAAE;;;;;;;;;;sCAc3B,6LAAC;4BAAE,WAAU;sCACV,QAAQ,kBAAkB,CAAC,MAAM,KAAK,IACnC,qBACA,AAAC,OAAwC,OAAlC,QAAQ,kBAAkB,CAAC,MAAM,EAAC;;;;;;;;;;;;8BAMjD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAE;wCAAO,QAAQ,MAAM,CAAC,WAAW;;;;;;;8CACpC,6LAAC;;wCAAE;wCACD,QAAQ,SAAS,KAAK,QAAQ,SAC9B,QAAQ,SAAS,KAAK,QAAQ,UAC9B,QAAQ,SAAS,KAAK,QAAQ,UAAU;;;;;;;8CAE1C,6LAAC;;wCAAE;wCACD,QAAQ,kBAAkB,CAAC,MAAM,KAAK,IAAI,SAC1C,AAAC,GAAoC,OAAlC,QAAQ,kBAAkB,CAAC,MAAM,EAAC;;;;;;;8CAEvC,6LAAC;;wCAAE;wCAAO;4CACR,QAAQ,iBAAiB,IAAI;4CAC7B,QAAQ,WAAW,IAAI;4CACvB,QAAQ,YAAY,IAAI;yCACzB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;8BAKnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAMtC;GAnQwB;;QAea,6IAAA,CAAA,WAAQ;;;KAfrB", "debugId": null}}, {"offset": {"line": 2852, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/app/stats/page.tsx"], "sourcesContent": ["/**\n * 统计页面\n * 展示提示词使用统计和数据管理功能\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport StatsPanel from '@/components/ui/StatsPanel';\nimport BatchImportModal from '@/components/ui/BatchImportModal';\nimport ExportModal from '@/components/ui/ExportModal';\nimport { useToast } from '@/components/ui/ToastContainer';\n\nexport default function StatsPage() {\n  const [showImportModal, setShowImportModal] = useState(false);\n  const [showExportModal, setShowExportModal] = useState(false);\n  const { showSuccess, showError } = useToast();\n\n  const handleImport = async (data: any[]) => {\n    // 这里应该调用API导入数据\n    console.log('导入数据:', data);\n    await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用\n  };\n\n  const handleExport = async (options: any) => {\n    // 这里应该调用API导出数据\n    console.log('导出选项:', options);\n    \n    // 模拟生成导出文件\n    const mockData = {\n      prompts: [\n        {\n          title: '示例提示词1',\n          content: '这是提示词内容...',\n          category: '写作助手',\n          tags: ['AI', '创意'],\n          usageCount: 25,\n          createdAt: '2024-01-15T10:00:00Z',\n        },\n        {\n          title: '示例提示词2',\n          content: '另一个提示词内容...',\n          category: '代码生成',\n          tags: ['编程', 'AI'],\n          usageCount: 18,\n          createdAt: '2024-01-10T09:00:00Z',\n        },\n      ],\n      exportedAt: new Date().toISOString(),\n      options,\n    };\n\n    // 根据格式生成文件\n    let content: string;\n    let filename: string;\n    let mimeType: string;\n\n    switch (options.format) {\n      case 'json':\n        content = JSON.stringify(mockData, null, 2);\n        filename = 'prompts-export.json';\n        mimeType = 'application/json';\n        break;\n      case 'csv':\n        const csvHeaders = ['标题', '内容', '分类', '标签', '使用次数', '创建时间'];\n        const csvRows = mockData.prompts.map(prompt => [\n          prompt.title,\n          prompt.content.replace(/\"/g, '\"\"'), // 转义双引号\n          prompt.category,\n          prompt.tags.join(';'),\n          prompt.usageCount.toString(),\n          new Date(prompt.createdAt).toLocaleDateString('zh-CN'),\n        ]);\n        content = [csvHeaders, ...csvRows]\n          .map(row => row.map(cell => `\"${cell}\"`).join(','))\n          .join('\\n');\n        filename = 'prompts-export.csv';\n        mimeType = 'text/csv;charset=utf-8';\n        break;\n      case 'markdown':\n        content = `# 提示词导出\\n\\n导出时间: ${new Date().toLocaleString('zh-CN')}\\n\\n`;\n        mockData.prompts.forEach((prompt, index) => {\n          content += `## ${index + 1}. ${prompt.title}\\n\\n`;\n          content += `**分类**: ${prompt.category}\\n\\n`;\n          content += `**标签**: ${prompt.tags.join(', ')}\\n\\n`;\n          content += `**使用次数**: ${prompt.usageCount}\\n\\n`;\n          content += `**内容**:\\n\\n${prompt.content}\\n\\n---\\n\\n`;\n        });\n        filename = 'prompts-export.md';\n        mimeType = 'text/markdown;charset=utf-8';\n        break;\n      default:\n        throw new Error('不支持的导出格式');\n    }\n\n    // 下载文件\n    const blob = new Blob([content], { type: mimeType });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = filename;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n\n    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟处理时间\n  };\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* 页面标题和操作 */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">数据统计</h1>\n            <p className=\"mt-1 text-sm text-gray-600\">\n              查看提示词使用统计和管理数据\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0 flex space-x-3\">\n            <button\n              onClick={() => setShowImportModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <svg className=\"mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n              </svg>\n              批量导入\n            </button>\n            <button\n              onClick={() => setShowExportModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <svg className=\"mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n              </svg>\n              导出数据\n            </button>\n          </div>\n        </div>\n\n        {/* 统计面板 */}\n        <StatsPanel />\n\n        {/* 快捷操作卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-8 w-8 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">批量导入</h3>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  从JSON文件批量导入提示词\n                </p>\n                <button\n                  onClick={() => setShowImportModal(true)}\n                  className=\"mt-3 text-sm text-blue-600 hover:text-blue-800 font-medium\"\n                >\n                  开始导入 →\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-8 w-8 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">数据导出</h3>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  导出为JSON、CSV或Markdown格式\n                </p>\n                <button\n                  onClick={() => setShowExportModal(true)}\n                  className=\"mt-3 text-sm text-green-600 hover:text-green-800 font-medium\"\n                >\n                  开始导出 →\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-8 w-8 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">数据备份</h3>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  定期备份您的提示词数据\n                </p>\n                <button className=\"mt-3 text-sm text-purple-600 hover:text-purple-800 font-medium\">\n                  设置备份 →\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 模态框 */}\n      <BatchImportModal\n        isOpen={showImportModal}\n        onClose={() => setShowImportModal(false)}\n        onImport={handleImport}\n      />\n\n      <ExportModal\n        isOpen={showExportModal}\n        onClose={() => setShowExportModal(false)}\n        onExport={handleExport}\n      />\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD;IAE1C,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAChB,QAAQ,GAAG,CAAC,SAAS;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,UAAU;IACrE;IAEA,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAChB,QAAQ,GAAG,CAAC,SAAS;QAErB,WAAW;QACX,MAAM,WAAW;YACf,SAAS;gBACP;oBACE,OAAO;oBACP,SAAS;oBACT,UAAU;oBACV,MAAM;wBAAC;wBAAM;qBAAK;oBAClB,YAAY;oBACZ,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,SAAS;oBACT,UAAU;oBACV,MAAM;wBAAC;wBAAM;qBAAK;oBAClB,YAAY;oBACZ,WAAW;gBACb;aACD;YACD,YAAY,IAAI,OAAO,WAAW;YAClC;QACF;QAEA,WAAW;QACX,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,OAAQ,QAAQ,MAAM;YACpB,KAAK;gBACH,UAAU,KAAK,SAAS,CAAC,UAAU,MAAM;gBACzC,WAAW;gBACX,WAAW;gBACX;YACF,KAAK;gBACH,MAAM,aAAa;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAQ;iBAAO;gBAC3D,MAAM,UAAU,SAAS,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU;wBAC7C,OAAO,KAAK;wBACZ,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM;wBAC7B,OAAO,QAAQ;wBACf,OAAO,IAAI,CAAC,IAAI,CAAC;wBACjB,OAAO,UAAU,CAAC,QAAQ;wBAC1B,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB,CAAC;qBAC/C;gBACD,UAAU;oBAAC;uBAAe;iBAAQ,CAC/B,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,AAAC,IAAQ,OAAL,MAAK,MAAI,IAAI,CAAC,MAC7C,IAAI,CAAC;gBACR,WAAW;gBACX,WAAW;gBACX;YACF,KAAK;gBACH,UAAU,AAAC,oBAAsD,OAAnC,IAAI,OAAO,cAAc,CAAC,UAAS;gBACjE,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ;oBAChC,WAAW,AAAC,MAAmB,OAAd,QAAQ,GAAE,MAAiB,OAAb,OAAO,KAAK,EAAC;oBAC5C,WAAW,AAAC,WAA0B,OAAhB,OAAO,QAAQ,EAAC;oBACtC,WAAW,AAAC,WAAiC,OAAvB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAM;oBAC7C,WAAW,AAAC,aAA8B,OAAlB,OAAO,UAAU,EAAC;oBAC1C,WAAW,AAAC,cAA4B,OAAf,OAAO,OAAO,EAAC;gBAC1C;gBACA,WAAW;gBACX,WAAW;gBACX;YACF;gBACE,MAAM,IAAI,MAAM;QACpB;QAEA,OAAO;QACP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAS;QAClD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;QAEpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,SAAS;IACpE;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;;0BACT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;kCAOZ,6LAAC,yIAAA,CAAA,UAAU;;;;;kCAGX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,6LAAC;oDAAO,WAAU;8DAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7F,6LAAC,+IAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,UAAU;;;;;;0BAGZ,6LAAC,0IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,UAAU;;;;;;;;;;;;AAIlB;GArNwB;;QAGa,6IAAA,CAAA,WAAQ;;;KAHrB", "debugId": null}}]}