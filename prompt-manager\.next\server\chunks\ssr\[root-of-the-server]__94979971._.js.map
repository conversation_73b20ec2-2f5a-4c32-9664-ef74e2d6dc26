{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/Augment/prompt-manager/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-base-100\">\n      {/* 导航栏 */}\n      <div className=\"navbar bg-base-100 shadow-lg\">\n        <div className=\"flex-1\">\n          <a className=\"btn btn-ghost text-xl\">提示词管理工具</a>\n        </div>\n        <div className=\"flex-none\">\n          <button className=\"btn btn-primary\">登录</button>\n        </div>\n      </div>\n\n      {/* 主要内容 */}\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"hero min-h-96 bg-base-200 rounded-lg\">\n          <div className=\"hero-content text-center\">\n            <div className=\"max-w-md\">\n              <h1 className=\"text-5xl font-bold\">欢迎使用</h1>\n              <h2 className=\"text-3xl font-bold text-primary mt-2\">\n                提示词管理工具\n              </h2>\n              <p className=\"py-6\">\n                高效管理您的AI提示词，支持分类、搜索、标签等功能，\n                让您的提示词库井然有序。\n              </p>\n              <button className=\"btn btn-primary\">开始使用</button>\n            </div>\n          </div>\n        </div>\n\n        {/* 功能卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-8\">\n          <div className=\"card bg-base-100 shadow-xl\">\n            <div className=\"card-body\">\n              <h2 className=\"card-title text-primary\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-6 w-6\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M19 11H5m14-7l2 2-2 2m2-2H9m10 7l2 2-2 2m2-2H9\"\n                  />\n                </svg>\n                智能分类\n              </h2>\n              <p>按照用途和场景对提示词进行分类管理</p>\n            </div>\n          </div>\n\n          <div className=\"card bg-base-100 shadow-xl\">\n            <div className=\"card-body\">\n              <h2 className=\"card-title text-secondary\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-6 w-6\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  />\n                </svg>\n                快速搜索\n              </h2>\n              <p>支持全文搜索，快速找到需要的提示词</p>\n            </div>\n          </div>\n\n          <div className=\"card bg-base-100 shadow-xl\">\n            <div className=\"card-body\">\n              <h2 className=\"card-title text-accent\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-6 w-6\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                  />\n                </svg>\n                一键复制\n              </h2>\n              <p>点击即可复制提示词到剪贴板，方便使用</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAKxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDAGrD,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDAIpB,8OAAC;wCAAO,WAAU;kDAAkB;;;;;;;;;;;;;;;;;;;;;;kCAM1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDACC,OAAM;oDACN,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;gDAEA;;;;;;;sDAGR,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAIP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDACC,OAAM;oDACN,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;gDAEA;;;;;;;sDAGR,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAIP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDACC,OAAM;oDACN,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;gDAEA;;;;;;;sDAGR,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}